import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

dotenv.config();

const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_KEY;
const supabase = createClient(supabaseUrl, supabaseKey);

async function testPhase2Query() {
    try {
        console.log('Testing Phase 2 query...');
        
        // Test the exact query from Phase 2 matching
        const { data: oslData, error: oslError } = await supabase
            .from('t_order_sheet_lines')
            .select(`
                id, g_code, min_weight, max_weight, color_id, mps_id,
                t_mps!inner (
                    id, g_code, active,
                    t_molds!inner (id, mold),
                    t_plastics!inner (id, plastic),
                    t_stamps!inner (id, stamp)
                ),
                t_colors!inner (id, color)
            `)
            .eq('vendor_id', 2)
            .is('vendor_internal_id', null) // Only unmatched OSLs
            .or('vendor_match_verified.is.null,vendor_match_verified.eq.false') // Include null or false, exclude true
            .eq('t_mps.active', true) // Only active MPS records
            .range(0, 4); // Just get first 5 records

        if (oslError) {
            console.error('Query error:', oslError);
            return;
        }

        console.log(`✅ Query successful! Found ${oslData.length} OSLs`);
        
        if (oslData.length > 0) {
            console.log('\nFirst OSL:');
            console.log(`ID: ${oslData[0].id}`);
            console.log(`Mold: ${oslData[0].t_mps.t_molds.mold}`);
            console.log(`Plastic: ${oslData[0].t_mps.t_plastics.plastic}`);
            console.log(`Stamp: ${oslData[0].t_mps.t_stamps.stamp}`);
        }
        
    } catch (error) {
        console.error('Test failed:', error);
    }
}

testPhase2Query();
