-- Create table for Amazon Fulfilled Inventory Report
-- This is a snapshot table that gets truncated and replaced on each import

CREATE TABLE IF NOT EXISTS it_amaz_fulfilled_inventory_report (
  id SERIAL PRIMARY KEY,
  seller_sku TEXT NOT NULL,
  fulfillment_channel_sku TEXT,
  asin TEXT,
  condition_type TEXT,
  warehouse_condition_code TEXT,
  quantity_available INTEGER DEFAULT 0,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_fulfilled_inventory_seller_sku ON it_amaz_fulfilled_inventory_report(seller_sku);
CREATE INDEX IF NOT EXISTS idx_fulfilled_inventory_asin ON it_amaz_fulfilled_inventory_report(asin);
CREATE INDEX IF NOT EXISTS idx_fulfilled_inventory_fulfillment_sku ON it_amaz_fulfilled_inventory_report(fulfillment_channel_sku);
CREATE INDEX IF NOT EXISTS idx_fulfilled_inventory_quantity ON it_amaz_fulfilled_inventory_report(quantity_available);

-- Add comments for documentation
COMMENT ON TABLE it_amaz_fulfilled_inventory_report IS 'Amazon Fulfilled Inventory Report - snapshot data that gets truncated and replaced on each import';
COMMENT ON COLUMN it_amaz_fulfilled_inventory_report.seller_sku IS 'Your SKU for the item';
COMMENT ON COLUMN it_amaz_fulfilled_inventory_report.fulfillment_channel_sku IS 'Amazon fulfillment channel SKU';
COMMENT ON COLUMN it_amaz_fulfilled_inventory_report.asin IS 'Amazon Standard Identification Number';
COMMENT ON COLUMN it_amaz_fulfilled_inventory_report.condition_type IS 'Item condition (e.g., NewItem)';
COMMENT ON COLUMN it_amaz_fulfilled_inventory_report.warehouse_condition_code IS 'Warehouse condition code (e.g., SELLABLE)';
COMMENT ON COLUMN it_amaz_fulfilled_inventory_report.quantity_available IS 'Available quantity in Amazon fulfillment centers';
