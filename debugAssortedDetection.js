import XLSX from 'xlsx';
import path from 'path';

async function debugAssortedDetection() {
    try {
        console.log('🔍 Debugging Assorted weight detection...\n');
        
        const filePath = path.join(process.cwd(), 'data', 'external data', 'discraftstock.xlsx');
        console.log(`📁 Reading file: ${filePath}`);
        
        const workbook = XLSX.readFile(filePath);
        const sheetName = workbook.SheetNames[0];
        const worksheet = workbook.Sheets[sheetName];
        const range = XLSX.utils.decode_range(worksheet['!ref']);
        
        console.log(`📊 Sheet range: ${worksheet['!ref']} (${range.e.r + 1} rows)`);
        
        // Check the header row (row 23 - 0-indexed row 22)
        console.log('\n📋 Checking header row (row 23):');
        for (let col = 12; col <= 18; col++) {
            const cellAddress = XLSX.utils.encode_cell({ r: 22, c: col }); // Row 23 (0-indexed 22)
            const cellValue = worksheet[cellAddress] ? worksheet[cellAddress].v : null;
            console.log(`   Column ${col} (${String.fromCharCode(65 + col)}): "${cellValue}"`);
        }
        
        // Check Tour Series rows (rows 24-36)
        console.log('\n🎯 Checking Tour Series rows (24-36):');
        for (let row = 23; row <= 35; row++) { // 0-indexed rows 23-35 for rows 24-36
            const plasticCell = XLSX.utils.encode_cell({ r: row, c: 0 }); // Column A
            const modelCell = XLSX.utils.encode_cell({ r: row, c: 1 }); // Column B
            
            const plastic = worksheet[plasticCell] ? worksheet[plasticCell].v : null;
            const model = worksheet[modelCell] ? worksheet[modelCell].v : null;
            
            if (plastic === 'Tour') {
                console.log(`\n   Row ${row + 1}: "${plastic}" | "${model}"`);
                
                // Check weight columns for this row
                let hasAssorted = false;
                for (let col = 12; col <= 18; col++) {
                    const cellAddress = XLSX.utils.encode_cell({ r: row, c: col });
                    const cellValue = worksheet[cellAddress] ? worksheet[cellAddress].v : null;
                    
                    if (cellValue) {
                        console.log(`     Column ${col}: "${cellValue}"`);
                        if (cellValue.toString().toLowerCase().includes('assorted')) {
                            hasAssorted = true;
                        }
                    }
                }
                
                console.log(`     Has "Assorted": ${hasAssorted}`);
            }
        }
        
        // Check if "Assorted" is in the header instead of individual cells
        console.log('\n🔍 Looking for "Assorted" in header row...');
        for (let col = 0; col < range.e.c; col++) {
            const cellAddress = XLSX.utils.encode_cell({ r: 22, c: col }); // Row 23 (header)
            const cellValue = worksheet[cellAddress] ? worksheet[cellAddress].v : null;
            
            if (cellValue && cellValue.toString().toLowerCase().includes('assorted')) {
                console.log(`   Found "Assorted" in column ${col} (${String.fromCharCode(65 + col)}): "${cellValue}"`);
            }
        }
        
    } catch (error) {
        console.error('❌ Debug error:', error);
    }
}

debugAssortedDetection();
