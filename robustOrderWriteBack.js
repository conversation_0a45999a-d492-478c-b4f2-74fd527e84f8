import XLSX from 'xlsx';
import { createClient } from '@supabase/supabase-js';
import path from 'path';
import { fileURLToPath } from 'url';
import dotenv from 'dotenv';
import fs from 'fs';

// Load environment variables
dotenv.config();

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Initialize Supabase client
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_KEY;
const supabase = createClient(supabaseUrl, supabaseKey);

// Robust mapping utilities
function createProductKey(rawLine, rawModel, weightRange) {
    const line = rawLine || 'NO_LINE';
    const model = rawModel || 'NO_MODEL';
    return `${line}|${model}|${weightRange}`;
}

function calculateFileHash(filePath) {
    const stats = fs.statSync(filePath);
    return `${stats.size}_${stats.mtime.getTime()}`;
}

function buildContentMap(worksheet, range) {
    console.log('🗺️ Building content map from current Excel file...');
    const contentMap = new Map();
    let mappedProducts = 0;
    
    for (let row = range.s.r; row <= range.e.r; row++) {
        const lineCell = worksheet[XLSX.utils.encode_cell({ r: row, c: 1 })]; // Column B (Line)
        const modelCell = worksheet[XLSX.utils.encode_cell({ r: row, c: 4 })]; // Column E (Model)
        
        const rawLine = lineCell?.v?.toString().trim() || '';
        const rawModel = modelCell?.v?.toString().trim() || '';
        
        if (rawLine || rawModel) {
            // Map each weight column for this row
            const weightColumns = [
                { col: 12, range: '150g', letter: 'L' },
                { col: 13, range: '160-166g', letter: 'M' },
                { col: 14, range: '167-169g', letter: 'N' },
                { col: 15, range: '170-172g', letter: 'O' },
                { col: 16, range: '173-174g', letter: 'P' },
                { col: 17, range: '175-176g', letter: 'Q' },
                { col: 18, range: '177+', letter: 'R' }
            ];
            
            weightColumns.forEach(weight => {
                const key = createProductKey(rawLine, rawModel, weight.range);
                contentMap.set(key, {
                    row: row + 1, // Convert to 1-based
                    column: weight.letter,
                    rawLine: rawLine,
                    rawModel: rawModel,
                    weightRange: weight.range
                });
                mappedProducts++;
            });
        }
    }
    
    console.log(`📊 Mapped ${mappedProducts} product locations in Excel file`);
    return contentMap;
}

function findProductLocation(mappingKey, contentMap) {
    const location = contentMap.get(mappingKey);
    if (location) {
        return {
            found: true,
            row: location.row,
            column: location.column
        };
    }
    
    return { found: false };
}

async function writeOrdersToExcel(orderQuantities, excelFilePath, outputFilePath) {
    try {
        console.log('📝 Writing order quantities to Excel...');
        console.log(`📁 Source file: ${excelFilePath}`);
        console.log(`📁 Output file: ${outputFilePath}`);
        
        // Check if source file exists
        if (!fs.existsSync(excelFilePath)) {
            throw new Error(`Source Excel file not found: ${excelFilePath}`);
        }
        
        // Read current Excel file
        const workbook = XLSX.readFile(excelFilePath);
        const worksheet = workbook.Sheets['Order Form'];
        
        if (!worksheet) {
            throw new Error('Sheet "Order Form" not found in Excel file');
        }
        
        const range = XLSX.utils.decode_range(worksheet['!ref']);
        console.log(`📊 Excel sheet range: ${XLSX.utils.encode_range(range)}`);
        
        // Build content map of current file
        const contentMap = buildContentMap(worksheet, range);
        
        const successfulWrites = [];
        const failedWrites = [];
        const skippedZeroQuantities = [];
        
        console.log(`🔄 Processing ${orderQuantities.length} order quantities...`);
        
        for (const order of orderQuantities) {
            // Skip zero quantities
            if (!order.quantity || order.quantity === 0) {
                skippedZeroQuantities.push(order);
                continue;
            }
            
            const location = findProductLocation(order.excel_mapping_key, contentMap);
            
            if (location.found) {
                const cellAddress = `${location.column}${location.row}`;
                
                // Write quantity to cell
                worksheet[cellAddress] = { 
                    v: order.quantity, 
                    t: 'n' // number type
                };
                
                successfulWrites.push({
                    key: order.excel_mapping_key,
                    cell: cellAddress,
                    quantity: order.quantity,
                    product: order.product_description || 'Unknown'
                });
            } else {
                failedWrites.push({
                    key: order.excel_mapping_key,
                    quantity: order.quantity,
                    product: order.product_description || 'Unknown'
                });
            }
        }
        
        // Save updated Excel file
        XLSX.writeFile(workbook, outputFilePath);
        
        // Report results
        console.log('\n📊 Write Results:');
        console.log(`✅ Successfully wrote ${successfulWrites.length} orders`);
        console.log(`⏭️ Skipped ${skippedZeroQuantities.length} zero quantities`);
        console.log(`❌ Failed to write ${failedWrites.length} orders`);
        
        if (successfulWrites.length > 0) {
            console.log('\n✅ Successful writes (sample):');
            successfulWrites.slice(0, 5).forEach(write => {
                console.log(`  ${write.cell}: ${write.quantity} (${write.product})`);
            });
            if (successfulWrites.length > 5) {
                console.log(`  ... and ${successfulWrites.length - 5} more`);
            }
        }
        
        if (failedWrites.length > 0) {
            console.log('\n❌ Failed writes:');
            failedWrites.forEach(write => {
                console.log(`  Missing: ${write.key} (qty: ${write.quantity})`);
            });
            
            console.log('\n💡 Possible reasons for failed writes:');
            console.log('  - Product no longer exists in vendor file');
            console.log('  - Vendor changed product names/structure');
            console.log('  - Import data is from different file version');
        }
        
        return {
            success: true,
            successfulWrites: successfulWrites.length,
            failedWrites: failedWrites.length,
            skippedZeroQuantities: skippedZeroQuantities.length,
            outputFile: outputFilePath
        };
        
    } catch (error) {
        console.error('❌ Error writing orders to Excel:', error);
        return {
            success: false,
            error: error.message
        };
    }
}

async function detectFileChanges(currentFilePath, importedFileHash) {
    try {
        const currentHash = calculateFileHash(currentFilePath);
        
        if (currentHash !== importedFileHash) {
            console.warn('⚠️ Excel file has changed since last import!');
            console.warn(`   Imported hash: ${importedFileHash}`);
            console.warn(`   Current hash:  ${currentHash}`);
            console.warn('🔄 Recommend re-importing to update Excel mappings');
            return { changed: true, currentHash, importedHash: importedFileHash };
        }
        
        console.log('✅ Excel file unchanged since import');
        return { changed: false, currentHash, importedHash: importedFileHash };
        
    } catch (error) {
        console.warn('⚠️ Could not detect file changes:', error.message);
        return { changed: null, error: error.message };
    }
}

// Example usage function
async function exampleOrderWriteBack() {
    try {
        console.log('🚀 Example: Writing orders to Excel with robust mapping...\n');
        
        const excelFilePath = path.join(__dirname, 'data', 'external data', 'discraftstock.xlsx');
        const outputFilePath = path.join(__dirname, 'data', 'external data', 'discraftstock_with_orders.xlsx');
        
        // Get some sample order quantities from database
        const { data: sampleProducts, error } = await supabase
            .from('it_discraft_order_sheet_lines')
            .select('excel_mapping_key, plastic_name, mold_name, min_weight, max_weight, import_file_hash')
            .eq('is_currently_available', true)
            .limit(10);
        
        if (error) throw error;
        
        if (sampleProducts.length === 0) {
            console.log('❌ No products found in database. Run import first.');
            return;
        }
        
        // Check for file changes
        const fileChangeInfo = await detectFileChanges(excelFilePath, sampleProducts[0].import_file_hash);
        
        // Create sample order quantities
        const orderQuantities = sampleProducts.map((product, index) => ({
            excel_mapping_key: product.excel_mapping_key,
            quantity: Math.floor(Math.random() * 20) + 1, // Random quantity 1-20
            product_description: `${product.plastic_name} ${product.mold_name} ${product.min_weight}-${product.max_weight}g`
        }));
        
        console.log('📦 Sample order quantities:');
        orderQuantities.forEach(order => {
            console.log(`  ${order.product_description}: ${order.quantity}`);
        });
        
        // Write orders to Excel
        const result = await writeOrdersToExcel(orderQuantities, excelFilePath, outputFilePath);
        
        if (result.success) {
            console.log(`\n🎉 Orders successfully written to: ${result.outputFile}`);
        } else {
            console.log(`\n❌ Failed to write orders: ${result.error}`);
        }
        
    } catch (error) {
        console.error('❌ Error in example:', error);
    }
}

// Export functions for use in other modules
export {
    writeOrdersToExcel,
    detectFileChanges,
    buildContentMap,
    findProductLocation,
    createProductKey
};

// Run example if this file is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
    exampleOrderWriteBack();
}
