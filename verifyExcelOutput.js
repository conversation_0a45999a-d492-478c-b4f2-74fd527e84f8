import XLSX from 'xlsx';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

function verifyExcelOutput() {
    try {
        console.log('🔍 Verifying Excel output...\n');
        
        const outputFilePath = path.join(__dirname, 'data', 'external data', 'discraftstock_with_orders.xlsx');
        
        // Read the output Excel file
        const workbook = XLSX.readFile(outputFilePath);
        const worksheet = workbook.Sheets['Order Form'];
        
        if (!worksheet) {
            console.log('❌ Sheet "Order Form" not found');
            return;
        }
        
        console.log('✅ Excel file read successfully');
        
        // Check specific cells that should have been written
        const cellsToCheck = [
            { cell: 'N132', expected: 'Elite Z Stalker 167-169g' },
            { cell: 'M133', expected: 'Elite Z Surge SS 160-166g' },
            { cell: 'N133', expected: 'Elite Z Surge SS 167-169g' },
            { cell: 'O133', expected: 'Elite Z Surge SS 170-172g' },
            { cell: 'P133', expected: 'Elite Z Surge SS 173-174g' }
        ];
        
        console.log('📊 Checking written order quantities:');
        cellsToCheck.forEach(check => {
            const cellValue = worksheet[check.cell]?.v;
            if (cellValue !== undefined && cellValue !== null && cellValue !== '') {
                console.log(`  ✅ ${check.cell}: ${cellValue} (${check.expected})`);
            } else {
                console.log(`  ❌ ${check.cell}: Empty or not found (expected ${check.expected})`);
            }
        });
        
        // Check the original product names in column D to verify mapping
        console.log('\n📋 Verifying product mapping:');
        const productCells = [
            { row: 132, col: 'D', expected: 'Discontinued - Stalker' },
            { row: 133, col: 'D', expected: 'Discontinued - Surge SS' },
            { row: 134, col: 'D', expected: 'Retired stamp- Banger GT' }
        ];
        
        productCells.forEach(check => {
            const cellAddress = `${check.col}${check.row}`;
            const cellValue = worksheet[cellAddress]?.v;
            console.log(`  ${cellAddress}: "${cellValue}" (expected: "${check.expected}")`);
        });
        
        console.log('\n🎉 Excel output verification complete!');
        
    } catch (error) {
        console.error('❌ Error verifying Excel output:', error);
    }
}

verifyExcelOutput();
