import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

dotenv.config();

const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_KEY;
const supabase = createClient(supabaseUrl, supabaseKey);

async function checkStockStamp() {
    try {
        console.log('🔍 Checking for "Stock" stamp in database...\n');
        
        // Check for exact "Stock" stamp
        const { data: exactStock, error: exactError } = await supabase
            .from('t_stamps')
            .select('id, stamp')
            .eq('stamp', 'Stock');
        
        if (!exactError && exactStock && exactStock.length > 0) {
            console.log(`✅ Found exact "Stock" stamp: ID ${exactStock[0].id}`);
        } else {
            console.log('❌ No exact "Stock" stamp found');
        }
        
        // Check for stamps containing "stock"
        const { data: stockLike, error: stockError } = await supabase
            .from('t_stamps')
            .select('id, stamp')
            .ilike('stamp', '%stock%')
            .limit(10);
        
        if (!stockError && stockLike && stockLike.length > 0) {
            console.log('\n📋 Stamps containing "stock":');
            stockLike.forEach(stamp => {
                console.log(`   ID ${stamp.id}: "${stamp.stamp}"`);
            });
        } else {
            console.log('\n❌ No stamps containing "stock" found');
        }
        
        // Check for empty stamp (might be used as default)
        const { data: emptyStamp, error: emptyError } = await supabase
            .from('t_stamps')
            .select('id, stamp')
            .eq('stamp', '');
        
        if (!emptyError && emptyStamp && emptyStamp.length > 0) {
            console.log(`\n📝 Found empty stamp: ID ${emptyStamp[0].id}`);
        }
        
        // Check what stamps are actually used in successful matches
        const { data: successfulStamps, error: successError } = await supabase
            .from('it_discraft_order_sheet_lines')
            .select('stamp_name, calculated_mps_id')
            .not('calculated_mps_id', 'is', null)
            .limit(20);
        
        if (!successError && successfulStamps) {
            console.log('\n✅ Stamps used in successful matches:');
            const uniqueStamps = [...new Set(successfulStamps.map(s => s.stamp_name))];
            uniqueStamps.forEach(stamp => {
                console.log(`   "${stamp}"`);
            });
        }
        
        // Check for "Tour" plastic
        console.log('\n🔍 Checking for "Tour" plastic...');
        const { data: tourPlastic, error: tourError } = await supabase
            .from('t_plastics')
            .select('id, plastic')
            .eq('brand_id', 6)
            .ilike('plastic', '%tour%');
        
        if (!tourError && tourPlastic && tourPlastic.length > 0) {
            console.log('✅ Found Tour-related plastics:');
            tourPlastic.forEach(plastic => {
                console.log(`   ID ${plastic.id}: "${plastic.plastic}"`);
            });
        } else {
            console.log('❌ No "Tour" plastic found');
        }
        
        console.log('\n💡 Recommendations:');
        if (!exactStock || exactStock.length === 0) {
            console.log('1. Add "Stock" stamp to t_stamps table');
        }
        if (!tourPlastic || tourPlastic.length === 0) {
            console.log('2. Add "Tour" plastic to t_plastics table with brand_id = 6');
        }
        console.log('3. Add missing molds: Drive, Force, Raptor, Scorch, Luna');
        
    } catch (error) {
        console.error('❌ Error checking stamps:', error);
    }
}

checkStockStamp();
