import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

dotenv.config();

const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_KEY;
const supabase = createClient(supabaseUrl, supabaseKey);

async function addMissingDiscraftProducts() {
    try {
        console.log('🔄 Adding missing Discraft products to database...\n');
        
        // Check if "Tour" plastic exists
        const { data: existingTour, error: tourCheckError } = await supabase
            .from('t_plastics')
            .select('id, plastic')
            .eq('brand_id', 6)
            .ilike('plastic', '%tour%');
        
        if (tourCheckError) {
            console.error('❌ Error checking Tour plastic:', tourCheckError);
            return;
        }
        
        if (!existingTour || existingTour.length === 0) {
            console.log('📦 Adding "Tour" plastic...');
            
            // Get the next available ID
            const { data: maxPlastic, error: maxError } = await supabase
                .from('t_plastics')
                .select('id')
                .order('id', { ascending: false })
                .limit(1);
            
            if (maxError) {
                console.error('❌ Error getting max plastic ID:', maxError);
                return;
            }
            
            const nextId = (maxPlastic && maxPlastic.length > 0) ? maxPlastic[0].id + 1 : 1;
            
            const { error: insertError } = await supabase
                .from('t_plastics')
                .insert({
                    id: nextId,
                    plastic: 'Tour',
                    brand_id: 6,  // Discraft
                    code: 'TOUR',
                    description: 'Discraft Tour Series plastic',
                    created_by: 'Discraft Import System',
                    ready: true
                });
            
            if (insertError) {
                console.error('❌ Error inserting Tour plastic:', insertError);
                return;
            }
            
            console.log(`✅ Added "Tour" plastic with ID ${nextId}`);
        } else {
            console.log(`✅ "Tour" plastic already exists: ${existingTour.map(p => p.plastic).join(', ')}`);
        }
        
        // Check for missing molds
        const missingMolds = ['Drive', 'Force', 'Raptor', 'Scorch', 'Luna'];
        
        for (const moldName of missingMolds) {
            const { data: existingMold, error: moldCheckError } = await supabase
                .from('t_molds')
                .select('id, mold')
                .eq('brand_id', 6)
                .eq('mold', moldName);
            
            if (moldCheckError) {
                console.error(`❌ Error checking ${moldName} mold:`, moldCheckError);
                continue;
            }
            
            if (!existingMold || existingMold.length === 0) {
                console.log(`📦 Adding "${moldName}" mold...`);
                
                // Get the next available ID
                const { data: maxMold, error: maxMoldError } = await supabase
                    .from('t_molds')
                    .select('id')
                    .order('id', { ascending: false })
                    .limit(1);
                
                if (maxMoldError) {
                    console.error(`❌ Error getting max mold ID for ${moldName}:`, maxMoldError);
                    continue;
                }
                
                const nextMoldId = (maxMold && maxMold.length > 0) ? maxMold[0].id + 1 : 1;
                
                const { error: insertMoldError } = await supabase
                    .from('t_molds')
                    .insert({
                        id: nextMoldId,
                        mold: moldName,
                        brand_id: 6,  // Discraft
                        code: moldName.toUpperCase(),
                        type: 'Unknown',  // You may want to update this manually
                        description: `Discraft ${moldName} disc`,
                        created_by: 'Discraft Import System'
                    });
                
                if (insertMoldError) {
                    console.error(`❌ Error inserting ${moldName} mold:`, insertMoldError);
                    continue;
                }
                
                console.log(`✅ Added "${moldName}" mold with ID ${nextMoldId}`);
            } else {
                console.log(`✅ "${moldName}" mold already exists`);
            }
        }
        
        console.log('\n🎉 Missing Discraft products added successfully!');
        console.log('\n💡 Next steps:');
        console.log('1. Run MPS calculation again to see improved matching');
        console.log('2. Manually update mold types and flight numbers if needed');
        console.log('3. Create MPS records for new plastic/mold/stamp combinations');
        
    } catch (error) {
        console.error('❌ Error adding missing products:', error);
    }
}

addMissingDiscraftProducts();
