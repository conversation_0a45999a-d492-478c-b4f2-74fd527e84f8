import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

dotenv.config();

const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_KEY;
const supabase = createClient(supabaseUrl, supabaseKey);

async function testEndpoints() {
    try {
        console.log('🧪 Testing Discraft endpoints...\n');
        
        // Test 1: Calculate MPS IDs
        console.log('1. Testing MPS calculation...');
        const { count: totalProducts, error: totalError } = await supabase
            .from('it_discraft_order_sheet_lines')
            .select('*', { count: 'exact', head: true });
        
        if (totalError) {
            console.error('Error getting total products:', totalError);
        } else {
            console.log(`✅ Found ${totalProducts} total Discraft products`);
        }
        
        const { count: withMpsId, error: withMpsError } = await supabase
            .from('it_discraft_order_sheet_lines')
            .select('*', { count: 'exact', head: true })
            .not('calculated_mps_id', 'is', null);
        
        if (withMpsError) {
            console.error('Error getting matched products:', withMpsError);
        } else {
            console.log(`✅ Found ${withMpsId} products with MPS IDs`);
            console.log(`📊 Success rate: ${((withMpsId / totalProducts) * 100).toFixed(1)}%`);
        }
        
        // Test 2: Unmatched OSLs
        console.log('\n2. Testing unmatched OSLs...');
        
        // Get matched MPS IDs
        const { data: matchedMpsIds, error: matchedError } = await supabase
            .from('it_discraft_order_sheet_lines')
            .select('calculated_mps_id')
            .not('calculated_mps_id', 'is', null);
        
        if (matchedError) {
            console.error('Error getting matched MPS IDs:', matchedError);
        } else {
            const matchedIds = matchedMpsIds.map(row => row.calculated_mps_id);
            console.log(`✅ Found ${matchedIds.length} unique matched MPS IDs`);
            
            // Get total Discraft OSLs
            const { count: totalOsls, error: totalOslError } = await supabase
                .from('t_order_sheet_lines')
                .select('*', { count: 'exact', head: true })
                .eq('vendor_id', 9);
            
            if (totalOslError) {
                console.error('Error getting total OSLs:', totalOslError);
            } else {
                console.log(`✅ Found ${totalOsls} total Discraft OSLs`);
                
                // Get unmatched OSLs (simple approach)
                const { data: allOsls, error: allOslError } = await supabase
                    .from('t_order_sheet_lines')
                    .select('id, mps_id')
                    .eq('vendor_id', 9);
                
                if (allOslError) {
                    console.error('Error getting all OSLs:', allOslError);
                } else {
                    const unmatchedOsls = allOsls.filter(osl => !matchedIds.includes(osl.mps_id));
                    console.log(`✅ Found ${unmatchedOsls.length} unmatched OSLs`);
                }
            }
        }
        
        // Test 3: Parsing issues
        console.log('\n3. Testing parsing issues analysis...');
        
        const { data: unmatchedProducts, error: unmatchedError } = await supabase
            .from('it_discraft_order_sheet_lines')
            .select('plastic_name, mold_name, stamp_name')
            .is('calculated_mps_id', null)
            .limit(10);
        
        if (unmatchedError) {
            console.error('Error getting unmatched products:', unmatchedError);
        } else {
            console.log(`✅ Found ${unmatchedProducts.length} sample unmatched products`);
            unmatchedProducts.forEach((product, index) => {
                console.log(`   ${index + 1}. "${product.plastic_name}" | "${product.mold_name}" | "${product.stamp_name}"`);
            });
        }
        
        console.log('\n🎉 All tests completed!');
        
    } catch (error) {
        console.error('❌ Test error:', error);
    }
}

testEndpoints();
