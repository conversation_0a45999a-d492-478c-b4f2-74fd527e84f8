import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

dotenv.config();

const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_KEY;
const supabase = createClient(supabaseUrl, supabaseKey);

async function fixTourSeries() {
    try {
        console.log('🔧 Fixing Tour Series parsing...\n');
        
        // Mapping of molds to their Tour Series player stamps
        const tourSeriesMapping = {
            'Buzzz': '<PERSON> 2025 Tour Series',
            'BuzzzSS': 'Ezra Robinson 2025 Tour Series', 
            'Force': 'Ricky <PERSON>ocki 2025 Tour Series',
            'Vulture': 'Holyn <PERSON>ley 2025 Tour Series',
            'Zone': 'Adam <PERSON> 2025 Tour Series',
            'Nuke': 'Ezra <PERSON> 2025 Tour Series',
            'Thrasher': 'Missy Gannon 2025 Tour Series',
            'Swarm': 'Andrew Presnell 2025 Tour Series',
            'Scorch': '<PERSON> 2025 Tour Series',
            'Raptor': '<PERSON> 2025 Tour Series',
            'Venom': '<PERSON> 2025 Tour Series',
            'Drive': '<PERSON> 2025 Tour Series',
            'Luna': '<PERSON> 2025 Tour Series'
        };
        
        // Get all Tour products
        const { data: tourProducts, error: tourError } = await supabase
            .from('it_discraft_order_sheet_lines')
            .select('id, plastic_name, mold_name, stamp_name, min_weight, max_weight, vendor_description')
            .eq('plastic_name', 'Tour');
        
        if (tourError) {
            console.error('Error getting Tour products:', tourError);
            return;
        }
        
        console.log(`Found ${tourProducts.length} Tour products to fix:`);
        
        let fixedCount = 0;
        let skippedCount = 0;
        
        for (const product of tourProducts) {
            console.log(`\nProcessing: "${product.plastic_name}" | "${product.mold_name}" | "${product.stamp_name}"`);
            console.log(`   Description: ${product.vendor_description || 'N/A'}`);
            console.log(`   Weight: ${product.min_weight}-${product.max_weight}g`);
            
            // Check if this mold has a Tour Series mapping
            const tourStamp = tourSeriesMapping[product.mold_name];
            
            if (tourStamp) {
                // Update the product
                const updates = {
                    plastic_name: 'Elite Z Swirl',
                    stamp_name: tourStamp
                };
                
                // Fix "Assorted" weight range to 160-180g
                if (product.min_weight === null || product.max_weight === null || 
                    product.vendor_description?.includes('Assorted')) {
                    updates.min_weight = 160;
                    updates.max_weight = 180;
                    console.log(`   → Fixing "Assorted" weight to 160-180g`);
                }
                
                const { error: updateError } = await supabase
                    .from('it_discraft_order_sheet_lines')
                    .update(updates)
                    .eq('id', product.id);
                
                if (updateError) {
                    console.error(`   ❌ Error updating product ${product.id}:`, updateError);
                } else {
                    console.log(`   ✅ Updated: "Tour" → "Elite Z Swirl"`);
                    console.log(`   ✅ Updated: "${product.stamp_name}" → "${tourStamp}"`);
                    fixedCount++;
                }
            } else {
                console.log(`   ⚠️ No Tour Series mapping found for mold "${product.mold_name}"`);
                skippedCount++;
            }
        }
        
        console.log(`\n📊 Tour Series Fix Results:`);
        console.log(`   • Fixed: ${fixedCount} products`);
        console.log(`   • Skipped: ${skippedCount} products`);
        
        // Now recalculate MPS IDs for the fixed products
        console.log('\n🔄 Recalculating MPS IDs for Tour Series products...');
        
        // Get the fixed products
        const { data: fixedProducts, error: fixedError } = await supabase
            .from('it_discraft_order_sheet_lines')
            .select('id, plastic_name, mold_name, stamp_name')
            .eq('plastic_name', 'Elite Z Swirl')
            .ilike('stamp_name', '%2025 Tour Series%')
            .is('calculated_mps_id', null);
        
        if (fixedError) {
            console.error('Error getting fixed products:', fixedError);
            return;
        }
        
        // Get matching MPS records
        const { data: mpsRecords, error: mpsError } = await supabase
            .from('t_mps')
            .select(`id, t_plastics!inner(plastic, brand_id), t_molds!inner(mold, brand_id), t_stamps!inner(stamp)`)
            .eq('active', true)
            .eq('t_plastics.brand_id', 6)
            .eq('t_molds.brand_id', 6)
            .eq('t_plastics.plastic', 'Elite Z Swirl')
            .ilike('t_stamps.stamp', '%2025 Tour Series%');
        
        if (mpsError) {
            console.error('Error getting MPS records:', mpsError);
            return;
        }
        
        console.log(`Found ${mpsRecords.length} matching MPS records`);
        
        // Create lookup map
        const mpsMap = new Map();
        mpsRecords.forEach(mps => {
            const key = `${mps.t_plastics.plastic.trim()}|${mps.t_molds.mold.trim()}|${mps.t_stamps.stamp.trim()}`;
            mpsMap.set(key, mps.id);
            console.log(`   MPS ${mps.id}: "${mps.t_plastics.plastic}" | "${mps.t_molds.mold}" | "${mps.t_stamps.stamp}"`);
        });
        
        // Match the fixed products
        let newMatches = 0;
        for (const product of fixedProducts) {
            const key = `${product.plastic_name.trim()}|${product.mold_name.trim()}|${product.stamp_name.trim()}`;
            const mpsId = mpsMap.get(key);
            
            if (mpsId) {
                const { error } = await supabase
                    .from('it_discraft_order_sheet_lines')
                    .update({ calculated_mps_id: mpsId })
                    .eq('id', product.id);
                
                if (!error) {
                    console.log(`   ✅ Matched product ${product.id} to MPS ${mpsId}`);
                    newMatches++;
                }
            } else {
                console.log(`   ❌ No MPS match for: "${key}"`);
            }
        }
        
        console.log(`\n🎉 Found ${newMatches} new Tour Series matches!`);
        
        // Final statistics
        const { count: totalProducts } = await supabase
            .from('it_discraft_order_sheet_lines')
            .select('*', { count: 'exact', head: true });
        
        const { count: matchedProducts } = await supabase
            .from('it_discraft_order_sheet_lines')
            .select('*', { count: 'exact', head: true })
            .not('calculated_mps_id', 'is', null);
        
        const successRate = ((matchedProducts / totalProducts) * 100).toFixed(2);
        
        console.log('\n📊 Final Results:');
        console.log(`   • Total products: ${totalProducts}`);
        console.log(`   • Matched products: ${matchedProducts}`);
        console.log(`   • Success rate: ${successRate}%`);
        
        if (newMatches > 0) {
            console.log('🎉 Tour Series parsing fixed successfully!');
        }
        
    } catch (error) {
        console.error('❌ Error fixing Tour Series:', error);
    }
}

fixTourSeries();
