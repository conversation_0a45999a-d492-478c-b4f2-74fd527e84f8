-- Create table to track rejected Phase 2 matches
-- This prevents showing the same rejected combinations in future Phase 2 runs

CREATE TABLE IF NOT EXISTS t_vendor_match_rejections (
    id SERIAL PRIMARY KEY,
    osl_id INTEGER NOT NULL,
    vendor_id INTEGER NOT NULL DEFAULT 2,
    vendor_internal_id INTEGER NOT NULL,
    rejected_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    rejected_by TEXT DEFAULT 'admin_interface',
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Ensure we don't have duplicate rejections for the same combination
    UNIQUE(osl_id, vendor_id, vendor_internal_id)
);

-- Add indexes for performance
CREATE INDEX IF NOT EXISTS idx_vendor_match_rejections_osl_vendor 
ON t_vendor_match_rejections(osl_id, vendor_id);

CREATE INDEX IF NOT EXISTS idx_vendor_match_rejections_vendor_internal 
ON t_vendor_match_rejections(vendor_id, vendor_internal_id);

-- Add foreign key constraints (optional, but good practice)
-- Note: Uncomment these if you want strict referential integrity
-- ALTER TABLE t_vendor_match_rejections 
-- ADD CONSTRAINT fk_vendor_match_rejections_osl 
-- FOREIGN KEY (osl_id) REFERENCES t_order_sheet_lines(id);

-- Add comments for documentation
COMMENT ON TABLE t_vendor_match_rejections IS 'Tracks rejected vendor matches from Phase 2 matching to prevent showing them again';
COMMENT ON COLUMN t_vendor_match_rejections.osl_id IS 'Order sheet line ID that was rejected';
COMMENT ON COLUMN t_vendor_match_rejections.vendor_id IS 'Vendor ID (2 = Innova)';
COMMENT ON COLUMN t_vendor_match_rejections.vendor_internal_id IS 'Vendor internal ID that was rejected as a match';
COMMENT ON COLUMN t_vendor_match_rejections.rejected_at IS 'When this match was rejected';
COMMENT ON COLUMN t_vendor_match_rejections.rejected_by IS 'Who/what rejected this match';
COMMENT ON COLUMN t_vendor_match_rejections.notes IS 'Optional notes about why this was rejected';

-- Show table structure
\d t_vendor_match_rejections;
