import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

dotenv.config();

const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_KEY;
const supabase = createClient(supabaseUrl, supabaseKey);

async function fixTourSeriesForReal() {
    try {
        console.log('🔧 Fixing Tour Series products FOR REAL this time...\n');
        
        // Mapping of molds to their Tour Series player stamps
        const tourSeriesMapping = {
            'Buzzz': '<PERSON> 2025 Tour Series',
            'BuzzzSS': 'Ezra Robinson 2025 Tour Series', 
            'Force': 'Ricky Wysocki 2025 Tour Series',
            'Vulture': 'Holyn <PERSON>ley 2025 Tour Series',
            'Zone': 'Adam Hammes 2025 Tour Series',
            'Nuke': 'Ezra <PERSON> 2025 Tour Series',
            'Thrasher': 'Missy Gannon 2025 Tour Series',
            'Swarm': 'Andrew <PERSON>snell 2025 Tour Series',
            'Scorch': '<PERSON> 2025 Tour Series',
            'Raptor': 'Aaron Go<PERSON>ge 2025 Tour Series',
            'Venom': 'Anthony <PERSON> 2025 Tour Series',
            'Drive': 'Paige Pierce 2025 Tour Series',
            'Luna': '<PERSON> 2025 Tour Series'
        };
        
        // Get all Tour products
        const { data: tourProducts, error: tourError } = await supabase
            .from('it_discraft_order_sheet_lines')
            .select('id, plastic_name, mold_name, stamp_name, min_weight, max_weight')
            .eq('plastic_name', 'Tour');
        
        if (tourError) {
            console.error('Error getting Tour products:', tourError);
            return;
        }
        
        console.log(`Found ${tourProducts.length} Tour products to fix`);
        
        let fixedCount = 0;
        let skippedCount = 0;
        
        // Process in smaller batches to avoid timeouts
        const batchSize = 10;
        for (let i = 0; i < tourProducts.length; i += batchSize) {
            const batch = tourProducts.slice(i, i + batchSize);
            console.log(`\nProcessing batch ${Math.floor(i/batchSize) + 1}/${Math.ceil(tourProducts.length/batchSize)} (${batch.length} products)...`);
            
            for (const product of batch) {
                const tourStamp = tourSeriesMapping[product.mold_name];
                
                if (tourStamp) {
                    console.log(`   Fixing: ${product.mold_name} (ID ${product.id})`);
                    
                    // Update the product
                    const updates = {
                        plastic_name: 'Elite Z Swirl',
                        stamp_name: tourStamp,
                        min_weight: 160,
                        max_weight: 180
                    };
                    
                    const { error: updateError } = await supabase
                        .from('it_discraft_order_sheet_lines')
                        .update(updates)
                        .eq('id', product.id);
                    
                    if (updateError) {
                        console.error(`   ❌ Error updating product ${product.id}:`, updateError);
                    } else {
                        console.log(`   ✅ Updated: "Tour" → "Elite Z Swirl", stamp → "${tourStamp}"`);
                        fixedCount++;
                    }
                } else {
                    console.log(`   ⚠️ Skipping: No Tour Series mapping for mold "${product.mold_name}"`);
                    skippedCount++;
                }
            }
            
            // Small delay between batches
            await new Promise(resolve => setTimeout(resolve, 100));
        }
        
        console.log(`\n📊 Tour Series Fix Results:`);
        console.log(`   • Fixed: ${fixedCount} products`);
        console.log(`   • Skipped: ${skippedCount} products`);
        
        // Verify the fix worked
        console.log('\n🔍 Verifying the fix...');
        
        const { count: remainingTour, error: remainingError } = await supabase
            .from('it_discraft_order_sheet_lines')
            .select('*', { count: 'exact', head: true })
            .eq('plastic_name', 'Tour');
        
        if (!remainingError) {
            console.log(`   Remaining "Tour" products: ${remainingTour}`);
        }
        
        const { count: newEliteZ, error: eliteZError } = await supabase
            .from('it_discraft_order_sheet_lines')
            .select('*', { count: 'exact', head: true })
            .eq('plastic_name', 'Elite Z Swirl')
            .ilike('stamp_name', '%2025 Tour Series%');
        
        if (!eliteZError) {
            console.log(`   New "Elite Z Swirl" Tour Series products: ${newEliteZ}`);
        }
        
        if (fixedCount > 0) {
            console.log('\n🎉 Tour Series fix completed! Now run MPS calculation to match them.');
        }
        
    } catch (error) {
        console.error('❌ Error fixing Tour Series:', error);
    }
}

fixTourSeriesForReal();
