import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';
import fs from 'fs';

dotenv.config();

const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_KEY;
const supabase = createClient(supabaseUrl, supabaseKey);

async function createTable() {
    try {
        const sql = fs.readFileSync('create_discraft_table.sql', 'utf8');
        console.log('Creating Discraft table...');
        
        // Split SQL into individual statements
        const statements = sql.split(';').filter(stmt => stmt.trim().length > 0);
        
        for (const statement of statements) {
            if (statement.trim()) {
                const { error } = await supabase.rpc('exec_sql', { 
                    sql_query: statement.trim() + ';' 
                });
                
                if (error) {
                    console.error('Error executing statement:', error);
                    console.error('Statement was:', statement.trim());
                } else {
                    console.log('✅ Executed statement successfully');
                }
            }
        }
        
        console.log('✅ Table creation completed!');
    } catch (err) {
        console.error('Exception:', err);
    }
}

createTable();
