// Test the updated parsing logic with training examples

// Copy the updated functions from fullDiscraftImport.js
function standardizePlasticName(rawPlastic, rawModel) {
    if (!rawPlastic && !rawModel) return 'Unknown';
    
    const plastic = rawPlastic?.toString().trim() || '';
    const model = rawModel?.toString().trim() || '';
    
    // Handle special signature series first
    if (plastic.includes('Brodie Smith Z FLX')) {
        return 'Elite Z FLX Confetti';
    }
    
    if (plastic.includes('Anthony Barela Signature Series Z')) {
        return 'Elite Z';
    }
    
    // Handle special cases
    if (plastic.includes('Fuzed') || plastic === 'Fuzed') {
        return 'Elite Z FuZed Line with Saw Pattern';
    }
    
    if (plastic === 'PM Z Lite') {
        return 'Elite Z Lite';
    }
    
    if (plastic === 'PP Z Lite') {
        return 'Elite Z Lite';
    }
    
    if (plastic === 'McBeth') {
        if (model && model.includes('Soft')) {
            return 'Putter Line Soft';
        }
        return 'Putter Line Hard';
    }
    
    if (plastic === 'Pierce') {
        if (model && model.includes('Soft')) {
            return 'Putter Line Soft';
        }
        return 'Putter Line Hard';
    }
    
    // Standard mappings
    const plasticMappings = {
        'Z': 'Elite Z',
        'Z Lite': 'Elite Z Lite',
        'Z Fly Dye': 'Elite Z Fly-Dyed',
        'Z Glo': 'Elite Z Glow',
        'Jawbreaker': 'Jawbreaker',
        'X': 'Elite X',
        'ESP': 'ESP',
        'Recycle ESP': 'Recycled ESP'
    };
    
    return plasticMappings[plastic] || plastic || 'Unknown';
}

function extractMoldName(model) {
    if (!model) return 'Unknown';
    
    const modelStr = model.toString().trim();
    
    // Handle special cases first
    if (modelStr.includes('Cigarra')) return 'Cigarra';
    if (modelStr.includes('Buzzz SS')) return 'Buzzz SS';
    if (modelStr.includes('Surge SS')) return 'Surge SS';
    if (modelStr.includes('Avenger SS')) return 'Avenger SS';
    if (modelStr.includes('Crank SS')) return 'Crank SS';
    if (modelStr.includes('Buzzz OS')) return 'Buzzz OS';
    if (modelStr.includes('Banger GT')) return 'Banger GT';
    
    // Remove common prefixes and suffixes
    let mold = modelStr
        .replace(/^(Fly Dye Z|Z Glo|Z Lite|Z|ESP|Jawbreaker|X)\s+/i, '')
        .replace(/\s+(Discontinued|Retired stamp|NEW).*$/i, '')
        .replace(/^(PS|RW|PP)\s+Z\s+/i, '')
        .replace(/^Soft\s+/i, '') // Remove "Soft" prefix
        .replace(/^Hard\s+/i, '') // Remove "Hard" prefix
        .trim();
    
    // Handle "NEW -" prefix
    if (mold.startsWith('NEW -')) {
        mold = mold.replace(/^NEW\s*-\s*/, '').trim();
    }
    
    // Handle discontinued/retired patterns
    if (mold.includes('Discontinued -')) {
        mold = mold.replace(/.*Discontinued\s*-\s*/, '').trim();
    }
    
    if (mold.includes('Retired stamp -')) {
        mold = mold.replace(/.*Retired stamp\s*-\s*/, '').trim();
    }
    
    // Extract first word as mold name, but handle compound names
    const words = mold.split(/\s+/);
    
    // Handle compound mold names
    if (words.length >= 2) {
        const firstTwo = `${words[0]} ${words[1]}`;
        if (['Buzzz SS', 'Surge SS', 'Avenger SS', 'Crank SS', 'Buzzz OS', 'Banger GT'].includes(firstTwo)) {
            return firstTwo;
        }
    }
    
    // Return first word
    return words[0] || 'Unknown';
}

function parseMoldAndStamp(rawModel, rawPlastic) {
    const model = rawModel?.toString().trim() || '';
    const plastic = rawPlastic?.toString().trim() || '';
    
    // Handle special signature series
    if (plastic.includes('Brodie Smith Z FLX Zone Confetti')) {
        return {
            mold_name: 'Zone',
            stamp_name: 'Get Freaky Dark Horse Brodie Smith'
        };
    }
    
    if (plastic.includes('Anthony Barela Signature Series Z')) {
        // Extract mold from plastic line since model is NO_MODEL
        const moldFromPlastic = plastic.replace('Anthony Barela Signature Series Z', '').trim();
        return {
            mold_name: moldFromPlastic || extractMoldName(model),
            stamp_name: 'Anthony Barela Signature - AB'
        };
    }
    
    // Handle discontinued/retired products
    if (model.includes('Discontinued -')) {
        return {
            mold_name: model.replace(/.*Discontinued\s*-\s*/, '').trim(),
            stamp_name: 'Stock'
        };
    }
    
    if (model.includes('Retired stamp -')) {
        return {
            mold_name: model.replace(/.*Retired stamp\s*-\s*/, '').trim(),
            stamp_name: 'Stock'
        };
    }
    
    // Handle Fuzed with saw pattern
    if (plastic === 'Fuzed' && model.includes('Saw Pattern')) {
        return {
            mold_name: extractMoldName(model.replace(/\s*-\s*Saw Pattern/, '')),
            stamp_name: 'Big Bee with Saw Blade - "Buzzzsaw"'
        };
    }
    
    // Handle PM/PP Z Lite
    if (plastic === 'PM Z Lite') {
        return {
            mold_name: extractMoldName(model),
            stamp_name: 'PM Logo Stock Stamp'
        };
    }
    
    if (plastic === 'PP Z Lite') {
        return {
            mold_name: extractMoldName(model),
            stamp_name: 'PP 29190 5X Paige Pierce World Champion'
        };
    }
    
    // Handle McBeth/Pierce signature lines
    if (plastic === 'McBeth') {
        return {
            mold_name: extractMoldName(model),
            stamp_name: 'PM Logo Stock Stamp'
        };
    }
    
    if (plastic === 'Pierce') {
        return {
            mold_name: extractMoldName(model),
            stamp_name: 'PP Logo Stock Stamp'
        };
    }
    
    // Handle Recycle ESP
    if (plastic === 'Recycle ESP') {
        return {
            mold_name: extractMoldName(model),
            stamp_name: '100% Recycled ESP Stock'
        };
    }
    
    return {
        mold_name: extractMoldName(model),
        stamp_name: 'Stock'
    };
}

// Test cases from training
const testCases = [
    {
        key: 'Brodie Smith Z FLX Zone Confetti "Get Freaky"|NO_MODEL|170-172g',
        expected: 'Elite Z FLX Confetti - Zone - Get Freaky Dark Horse Brodie Smith'
    },
    {
        key: 'Anthony Barela Signature Series Z Cigarra|NO_MODEL|150g',
        expected: 'Elite Z - Cigarra - Anthony Barela Signature - AB'
    },
    {
        key: 'Z|Discontinued - Stalker|160-166g',
        expected: 'Elite Z - Stalker - Stock'
    },
    {
        key: 'Z|Discontinued - Surge SS|167-169g',
        expected: 'Elite Z - Surge SS - Stock'
    },
    {
        key: 'Jawbreaker|Retired stamp - Challenger|160-166g',
        expected: 'Jawbreaker - Challenger - Stock'
    },
    {
        key: 'Fuzed|Buzzz - Saw Pattern|170-172g',
        expected: 'Elite Z FuZed Line with Saw Pattern - Buzzz - Big Bee with Saw Blade - "Buzzzsaw"'
    },
    {
        key: 'PM Z Lite|Anax|150g',
        expected: 'Elite Z Lite - Anax - PM Logo Stock Stamp'
    },
    {
        key: 'PP Z Lite|Drive|160-166g',
        expected: 'Elite Z Lite - Drive - PP 29190 5X Paige Pierce World Champion'
    },
    {
        key: 'McBeth|Soft Luna|160-166g',
        expected: 'Putter Line Soft - Luna - PM Logo Stock Stamp'
    },
    {
        key: 'Pierce|Soft Fierce|160-166g',
        expected: 'Putter Line Soft - Fierce - PP Logo Stock Stamp'
    },
    {
        key: 'Recycle ESP|Avenger SS|160-166g',
        expected: 'Recycled ESP - Avenger SS - 100% Recycled ESP Stock'
    },
    {
        key: 'X|Avenger SS|150g',
        expected: 'Elite X - Avenger SS - Stock'
    },
    {
        key: 'Z|Avenger SS|160-166g',
        expected: 'Elite Z - Avenger SS - Stock'
    },
    {
        key: 'Z|Buzzz OS|160-166g',
        expected: 'Elite Z - Buzzz OS - Stock'
    },
    {
        key: 'Z|Crank SS|160-166g',
        expected: 'Elite Z - Crank SS - Stock'
    }
];

console.log('🧪 Testing Updated Parsing Logic\n');

testCases.forEach((testCase, index) => {
    const [rawPlastic, rawModel, weightRange] = testCase.key.split('|');
    
    const plastic = standardizePlasticName(rawPlastic, rawModel);
    const { mold_name, stamp_name } = parseMoldAndStamp(rawModel, rawPlastic);
    
    const result = `${plastic} - ${mold_name} - ${stamp_name}`;
    const isCorrect = result === testCase.expected;
    
    console.log(`${index + 1}. ${isCorrect ? '✅' : '❌'} ${testCase.key}`);
    console.log(`   Expected: ${testCase.expected}`);
    console.log(`   Got:      ${result}`);
    if (!isCorrect) {
        console.log(`   🔍 Raw: "${rawPlastic}" + "${rawModel}"`);
    }
    console.log('');
});

console.log('🎯 Testing complete!');
