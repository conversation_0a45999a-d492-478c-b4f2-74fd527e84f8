// testOslUpdatedShopifyDeletion.js - Test script for OSL updated Shopify deletion functionality
import dotenv from 'dotenv';
dotenv.config();

import { createClient } from '@supabase/supabase-js';

// Initialize Supabase client
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('ERROR: Missing Supabase URL or key in environment variables.');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

/**
 * Test function to demonstrate the OSL updated Shopify deletion functionality
 */
async function testOslUpdatedShopifyDeletion() {
  try {
    console.log('=== Testing OSL Updated Shopify Deletion Functionality ===');
    
    // Get an OSL that has been uploaded to Shopify for testing
    console.log('Looking for an OSL that has been uploaded to Shopify...');
    
    const { data: uploadedOsls, error: fetchError } = await supabase
      .from('t_order_sheet_lines')
      .select('id, shopify_uploaded_at, shopify_product_uploaded_notes')
      .not('shopify_uploaded_at', 'is', null)
      .limit(5);
    
    if (fetchError) {
      console.error('❌ Error fetching uploaded OSLs:', fetchError.message);
      return;
    }
    
    if (!uploadedOsls || uploadedOsls.length === 0) {
      console.log('ℹ️  No OSLs found that have been uploaded to Shopify.');
      console.log('To test this functionality:');
      console.log('1. Upload an OSL to Shopify first');
      console.log('2. Then trigger an OSL update to see the deletion behavior');
      return;
    }
    
    console.log(`Found ${uploadedOsls.length} OSLs uploaded to Shopify:`);
    uploadedOsls.forEach((osl, index) => {
      console.log(`  ${index + 1}. OSL ID: ${osl.id}, Uploaded: ${osl.shopify_uploaded_at}`);
    });
    
    // Use the first OSL for testing
    const testOsl = uploadedOsls[0];
    const oslId = testOsl.id;
    const oslSku = `OS${oslId}`;
    
    console.log(`\n📝 Testing with OSL ID: ${oslId} (SKU: ${oslSku})`);
    console.log(`Current upload status: ${testOsl.shopify_uploaded_at}`);
    
    // Simulate an OSL update by enqueueing an osl_updated task
    console.log('\n🔄 Enqueueing osl_updated task...');
    
    const { data: task, error: taskError } = await supabase
      .from('t_task_queue')
      .insert({
        task_type: 'osl_updated',
        payload: { 
          id: oslId,
          old_data: { shopify_uploaded_at: testOsl.shopify_uploaded_at }
        },
        status: 'pending',
        scheduled_at: new Date().toISOString(),
        created_at: new Date().toISOString(),
        enqueued_by: 'testOslUpdatedShopifyDeletion'
      })
      .select()
      .single();
    
    if (taskError) {
      console.error('❌ Error enqueueing osl_updated task:', taskError.message);
      return;
    }
    
    console.log('✅ Successfully enqueued osl_updated task!');
    console.log(`Task ID: ${task.id}`);
    console.log(`Task Type: ${task.task_type}`);
    console.log(`Status: ${task.status}`);
    console.log(`Scheduled At: ${task.scheduled_at}`);
    
    console.log('\n📋 Expected behavior when this task is processed:');
    console.log(`1. ✅ Detect that OSL ${oslId} was previously uploaded to Shopify`);
    console.log(`2. 🗑️  Enqueue delete_sku_from_shopify task for SKU: ${oslSku}`);
    console.log(`3. 🔄 Set shopify_uploaded_at to NULL for OSL ${oslId}`);
    console.log(`4. ⚙️  Continue with normal OSL updated processing (unlink discs, etc.)`);
    
    console.log('\n🔍 To monitor the results:');
    console.log('1. Run the task queue worker to process this task');
    console.log('2. Check the t_task_queue table for task status updates');
    console.log('3. Look for a new delete_sku_from_shopify task in the queue');
    console.log('4. Verify that the OSL\'s shopify_uploaded_at field is set to NULL');
    console.log('5. Check that the product is deleted from Shopify');
    
    console.log('\n📊 Monitoring queries:');
    console.log(`-- Check task status:`);
    console.log(`SELECT * FROM t_task_queue WHERE id = ${task.id};`);
    console.log(`-- Check for deletion task:`);
    console.log(`SELECT * FROM t_task_queue WHERE task_type = 'delete_sku_from_shopify' AND payload->>'sku' = '${oslSku}' ORDER BY created_at DESC LIMIT 1;`);
    console.log(`-- Check OSL upload status:`);
    console.log(`SELECT id, shopify_uploaded_at, shopify_product_uploaded_notes FROM t_order_sheet_lines WHERE id = ${oslId};`);
    
  } catch (error) {
    console.error('❌ Error testing OSL updated Shopify deletion:', error.message);
    process.exit(1);
  }
}

/**
 * Function to check the current status of a test
 */
async function checkTestStatus(oslId, taskId) {
  try {
    console.log(`\n=== Checking Test Status for OSL ${oslId} ===`);
    
    // Check the original task status
    const { data: originalTask, error: taskError } = await supabase
      .from('t_task_queue')
      .select('*')
      .eq('id', taskId)
      .single();
    
    if (taskError) {
      console.error('❌ Error fetching task:', taskError.message);
    } else {
      console.log(`📋 Original Task Status: ${originalTask.status}`);
      if (originalTask.result) {
        console.log(`📄 Result:`, JSON.stringify(originalTask.result, null, 2));
      }
    }
    
    // Check for deletion task
    const oslSku = `OS${oslId}`;
    const { data: deletionTasks, error: deletionError } = await supabase
      .from('t_task_queue')
      .select('*')
      .eq('task_type', 'delete_sku_from_shopify')
      .eq('payload->sku', oslSku)
      .order('created_at', { ascending: false })
      .limit(1);
    
    if (deletionError) {
      console.error('❌ Error fetching deletion tasks:', deletionError.message);
    } else if (deletionTasks && deletionTasks.length > 0) {
      const deletionTask = deletionTasks[0];
      console.log(`🗑️  Deletion Task Found: ID ${deletionTask.id}, Status: ${deletionTask.status}`);
      if (deletionTask.result) {
        console.log(`📄 Deletion Result:`, JSON.stringify(deletionTask.result, null, 2));
      }
    } else {
      console.log('❌ No deletion task found');
    }
    
    // Check OSL status
    const { data: oslRecord, error: oslError } = await supabase
      .from('t_order_sheet_lines')
      .select('id, shopify_uploaded_at, shopify_product_uploaded_notes')
      .eq('id', oslId)
      .single();
    
    if (oslError) {
      console.error('❌ Error fetching OSL:', oslError.message);
    } else {
      console.log(`📦 OSL Status:`);
      console.log(`   ID: ${oslRecord.id}`);
      console.log(`   Shopify Uploaded At: ${oslRecord.shopify_uploaded_at || 'NULL'}`);
      console.log(`   Notes: ${oslRecord.shopify_product_uploaded_notes || 'None'}`);
    }
    
  } catch (error) {
    console.error('❌ Error checking test status:', error.message);
  }
}

// Run the test if this script is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  if (process.argv[2] === 'check' && process.argv[3] && process.argv[4]) {
    // Check status mode: node testOslUpdatedShopifyDeletion.js check <oslId> <taskId>
    const oslId = parseInt(process.argv[3]);
    const taskId = parseInt(process.argv[4]);
    checkTestStatus(oslId, taskId);
  } else {
    // Normal test mode
    testOslUpdatedShopifyDeletion();
  }
}

export { testOslUpdatedShopifyDeletion, checkTestStatus };
