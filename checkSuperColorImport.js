import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

dotenv.config();

const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_KEY;
const supabase = createClient(supabaseUrl, supabaseKey);

async function checkSuperColorImport() {
    try {
        console.log('🔍 Checking SuperColor import results...\n');
        
        // Check SuperColor products
        const { data: superColorProducts, error } = await supabase
            .from('it_discraft_order_sheet_lines')
            .select('*')
            .eq('plastic_name', 'ESP SuperColor')
            .order('stamp_name');
        
        if (error) throw error;
        
        console.log(`🎨 ESP SuperColor Products: ${superColorProducts.length} total`);
        
        if (superColorProducts.length > 0) {
            console.log('\n📋 SuperColor Stamps:');
            const stampCounts = {};
            superColorProducts.forEach(product => {
                stampCounts[product.stamp_name] = (stampCounts[product.stamp_name] || 0) + 1;
            });
            
            Object.entries(stampCounts)
                .sort(([,a], [,b]) => b - a)
                .forEach(([stamp, count]) => {
                    console.log(`  ${stamp}: ${count} products`);
                });
            
            console.log('\n📦 Sample SuperColor Products:');
            superColorProducts.slice(0, 10).forEach((product, index) => {
                console.log(`  ${index + 1}. ${product.plastic_name} ${product.mold_name} ${product.min_weight}-${product.max_weight}g`);
                console.log(`     Stamp: ${product.stamp_name}`);
                console.log(`     Available: ${product.is_currently_available ? 'Yes' : 'No'}`);
                console.log(`     Key: ${product.excel_mapping_key}`);
                console.log('');
            });
        }
        
        // Check for Assorted weights
        console.log('🔍 Checking Assorted weight products...\n');
        
        const { data: assortedProducts, error: assortedError } = await supabase
            .from('it_discraft_order_sheet_lines')
            .select('*')
            .eq('raw_weight_range', 'Assorted')
            .limit(10);
        
        if (assortedError) throw assortedError;
        
        console.log(`📦 Assorted Weight Products: ${assortedProducts.length} found`);
        
        if (assortedProducts.length > 0) {
            assortedProducts.forEach((product, index) => {
                console.log(`  ${index + 1}. ${product.plastic_name} ${product.mold_name} ${product.min_weight}-${product.max_weight}g`);
                console.log(`     Weight Range: ${product.raw_weight_range}`);
                console.log(`     Key: ${product.excel_mapping_key}`);
                console.log('');
            });
        } else {
            console.log('  ℹ️ No Assorted weight products found (may not be in current vendor file)');
        }
        
        // Check Total Golf Discs skipping
        console.log('🔍 Checking Total Golf Discs exclusion...\n');
        
        const { data: totalGolfDiscs, error: totalError } = await supabase
            .from('it_discraft_order_sheet_lines')
            .select('*')
            .ilike('raw_line_type', '%Total Golf Discs%')
            .limit(5);
        
        if (totalError) throw totalError;
        
        if (totalGolfDiscs.length === 0) {
            console.log('✅ Total Golf Discs successfully excluded from import');
        } else {
            console.log(`❌ Found ${totalGolfDiscs.length} Total Golf Discs products (should be 0)`);
        }
        
        // Check import stopping at McBeth Apparel
        console.log('\n🔍 Checking McBeth Apparel exclusion...\n');
        
        const { data: maxRow, error: maxRowError } = await supabase
            .from('it_discraft_order_sheet_lines')
            .select('excel_row_hint')
            .order('excel_row_hint', { ascending: false })
            .limit(1);
        
        if (maxRowError) throw maxRowError;
        
        if (maxRow.length > 0) {
            console.log(`📊 Highest row imported: ${maxRow[0].excel_row_hint}`);
            if (maxRow[0].excel_row_hint < 420) {
                console.log('✅ Import correctly stopped before McBeth Apparel section');
            } else {
                console.log('❌ Import may have continued past McBeth Apparel section');
            }
        }
        
        console.log('\n🎉 SuperColor and special cases analysis complete!');
        
    } catch (error) {
        console.error('❌ Error checking SuperColor import:', error);
    }
}

checkSuperColorImport();
