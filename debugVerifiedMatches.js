import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

// Initialize Supabase client
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_KEY;
const supabase = createClient(supabaseUrl, supabaseKey);

async function debugVerifiedMatches() {
    try {
        console.log('Checking verification status of Innova matches...\n');
        
        // Get counts of verification status
        const { data: allInnova, error: allError } = await supabase
            .from('t_order_sheet_lines')
            .select('id, vendor_internal_id, vendor_match_verified, vendor_match_verified_at')
            .eq('vendor_id', 2)
            .not('vendor_internal_id', 'is', null);
        
        if (allError) {
            throw new Error(`Error fetching data: ${allError.message}`);
        }
        
        console.log(`Total Innova OSLs with vendor_internal_id: ${allInnova.length}`);
        
        // Count by verification status
        const verified = allInnova.filter(r => r.vendor_match_verified === true);
        const rejected = allInnova.filter(r => r.vendor_match_verified === false);
        const unverified = allInnova.filter(r => r.vendor_match_verified === null);
        
        console.log(`✅ Verified (true): ${verified.length}`);
        console.log(`❌ Rejected (false): ${rejected.length}`);
        console.log(`⏳ Unverified (null): ${unverified.length}`);
        
        // Show some recent verifications
        const recentVerified = allInnova
            .filter(r => r.vendor_match_verified_at)
            .sort((a, b) => new Date(b.vendor_match_verified_at) - new Date(a.vendor_match_verified_at))
            .slice(0, 10);
        
        console.log('\n=== RECENT VERIFICATIONS ===');
        recentVerified.forEach(record => {
            const status = record.vendor_match_verified ? '✅ CONFIRMED' : '❌ REJECTED';
            console.log(`OSL ${record.id}: ${status} at ${record.vendor_match_verified_at}`);
        });
        
        // Test the filter that enhanced matching uses
        const { data: filteredData, error: filterError } = await supabase
            .from('t_order_sheet_lines')
            .select('id, vendor_match_verified')
            .eq('vendor_id', 2)
            .not('vendor_internal_id', 'is', null)
            .is('vendor_match_verified', null);
        
        if (filterError) {
            console.error('Filter error:', filterError);
        } else {
            console.log(`\n=== FILTER TEST ===`);
            console.log(`Enhanced matching filter should return: ${filteredData.length} records`);
            console.log(`This should match "Unverified (null)" count above: ${unverified.length}`);
            
            if (filteredData.length !== unverified.length) {
                console.log('❌ MISMATCH! Filter is not working correctly.');
            } else {
                console.log('✅ Filter is working correctly.');
            }
        }
        
        return {
            success: true,
            total: allInnova.length,
            verified: verified.length,
            rejected: rejected.length,
            unverified: unverified.length,
            filteredCount: filteredData?.length || 0
        };
        
    } catch (error) {
        console.error('Debug failed:', error);
        return {
            success: false,
            error: error.message
        };
    }
}

// Run if executed directly
if (process.argv[1] && process.argv[1].endsWith('debugVerifiedMatches.js')) {
    console.log('Debugging verified matches...');
    debugVerifiedMatches()
        .then(result => {
            if (result.success) {
                console.log('\n✅ Debug completed successfully');
                process.exit(0);
            } else {
                console.error('❌ Debug failed:', result.error);
                process.exit(1);
            }
        })
        .catch(error => {
            console.error('Unexpected error:', error);
            process.exit(1);
        });
}

export { debugVerifiedMatches };
