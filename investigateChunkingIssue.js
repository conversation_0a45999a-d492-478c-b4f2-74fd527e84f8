import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

dotenv.config();

const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_KEY;
const supabase = createClient(supabaseUrl, supabaseKey);

async function investigateChunkingIssue() {
    try {
        console.log('🔍 Investigating chunking issue...\n');
        
        // Check total count in database
        const { count: totalCount, error: countError } = await supabase
            .from('it_discraft_order_sheet_lines')
            .select('*', { count: 'exact', head: true });
        
        if (countError) throw countError;
        
        console.log(`📊 Total records in database: ${totalCount}`);
        console.log(`📊 Expected from parsing: 1,308`);
        console.log(`📊 Missing records: ${1308 - totalCount}`);
        
        // Check if there's a Supabase limit
        console.log('\n🔍 Checking Supabase query limits...');
        
        const { data: allRecords, error: allError } = await supabase
            .from('it_discraft_order_sheet_lines')
            .select('id, excel_row_hint, import_batch_id')
            .order('excel_row_hint');
        
        if (allError) throw allError;
        
        console.log(`📊 Records retrieved by query: ${allRecords.length}`);
        
        // Check row distribution
        const rowHints = allRecords.map(r => r.excel_row_hint).sort((a, b) => a - b);
        console.log(`📊 Row range: ${rowHints[0]} to ${rowHints[rowHints.length - 1]}`);
        
        // Check for gaps in row numbers
        console.log('\n🔍 Checking for missing row ranges...');
        
        const rowSet = new Set(rowHints);
        const missingRows = [];
        
        for (let row = rowHints[0]; row <= rowHints[rowHints.length - 1]; row++) {
            if (!rowSet.has(row)) {
                missingRows.push(row);
            }
        }
        
        if (missingRows.length > 0) {
            console.log(`❌ Missing rows: ${missingRows.slice(0, 20).join(', ')}${missingRows.length > 20 ? '...' : ''}`);
            console.log(`📊 Total missing rows: ${missingRows.length}`);
        } else {
            console.log('✅ No missing rows in sequence');
        }
        
        // Check batch IDs
        console.log('\n🔍 Checking import batch IDs...');
        
        const batchIds = [...new Set(allRecords.map(r => r.import_batch_id))];
        console.log(`📦 Number of batch IDs: ${batchIds.length}`);
        
        batchIds.forEach(batchId => {
            const batchCount = allRecords.filter(r => r.import_batch_id === batchId).length;
            console.log(`  ${batchId}: ${batchCount} records`);
        });
        
        // Check if we hit the 1000 record limit
        if (totalCount === 1000) {
            console.log('\n🚨 FOUND THE ISSUE!');
            console.log('📊 Exactly 1000 records suggests we hit a query limit');
            console.log('🔍 This could be:');
            console.log('  1. Supabase default query limit');
            console.log('  2. Our batch insert stopping early');
            console.log('  3. A database constraint');
        }
        
        // Check the last few records to see where it stopped
        console.log('\n🔍 Last 10 records by row number:');
        const lastRecords = allRecords.slice(-10);
        lastRecords.forEach(record => {
            console.log(`  Row ${record.excel_row_hint}: ID ${record.id}`);
        });
        
        // Check if there are records beyond row 1000
        const { data: highRowRecords, error: highRowError } = await supabase
            .from('it_discraft_order_sheet_lines')
            .select('excel_row_hint, id')
            .gt('excel_row_hint', 400)
            .order('excel_row_hint', { ascending: false })
            .limit(10);
        
        if (highRowError) throw highRowError;
        
        console.log('\n🔍 Highest row numbers in database:');
        highRowRecords.forEach(record => {
            console.log(`  Row ${record.excel_row_hint}: ID ${record.id}`);
        });
        
    } catch (error) {
        console.error('❌ Error investigating chunking issue:', error);
    }
}

investigateChunkingIssue();
