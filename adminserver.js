// adminServer.js

// Suppress Node.js deprecation warnings
process.noDeprecation = true;

import express from 'express';
import { spawn, exec } from 'child_process';
import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';
import path from 'path';
import { fileURLToPath } from 'url';
import os from 'os';
import { registerInformedApiEndpoints } from './informedApiHandler.js';
import { importDiscsFromGoogleSheets } from './googleSheetsImporter.js';

// Load environment variables
dotenv.config();

// Get the directory name of the current module
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Create Express app
const app = express();
// Increase the JSON payload limit to handle large CSV uploads (50MB)
app.use(express.json({ limit: '50mb' }));
app.use(express.urlencoded({ limit: '50mb', extended: true }));
app.use(express.static(__dirname));

// Create Supabase client
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_KEY;
let supabase;

if (supabaseUrl && supabaseKey) {
  supabase = createClient(supabaseUrl, supabaseKey);
}

// Function to get local IP address for LAN access
function getLocalIPAddress() {
    const interfaces = os.networkInterfaces();
    for (const name of Object.keys(interfaces)) {
        for (const iface of interfaces[name]) {
            if (iface.family === 'IPv4' && !iface.internal) {
                return iface.address;
            }
        }
    }
    return 'localhost';
}

// Track worker process
let workerProcess = null;
let workerOutput = [];
let workerStatus = 'stopped';
let lastRunTime = null;

// Function to update worker output and ensure status is correct
function updateWorkerOutput(output) {
  // Add the output to the worker output array
  if (Array.isArray(output)) {
    workerOutput.push(...output);
  } else {
    workerOutput.push(output);
  }

  // If we're receiving output, the worker must be running
  // This ensures the status stays in sync with the console output
  if (workerOutput.length > 0 && (workerProcess || workerStatus === 'running')) {
    workerStatus = 'running';
  }
}

// API endpoint to get worker status
app.get('/api/worker/status', (req, res) => {
  // Query the database for pending and future tasks
  const getPendingTasksCount = async () => {
    if (!supabase) return 0;

    try {
      const { count, error } = await supabase
        .from('t_task_queue')
        .select('*', { count: 'exact', head: true })
        .eq('status', 'pending')
        .lte('scheduled_at', new Date().toISOString());

      if (error) {
        console.error('[adminServer] Error counting pending tasks:', error);
        return 0;
      }

      return count || 0;
    } catch (err) {
      console.error('[adminServer] Exception counting pending tasks:', err);
      return 0;
    }
  };

  const getFutureTasksCount = async () => {
    if (!supabase) return 0;

    try {
      const { count, error } = await supabase
        .from('t_task_queue')
        .select('*', { count: 'exact', head: true })
        .eq('status', 'pending')
        .gt('scheduled_at', new Date().toISOString());

      if (error) {
        console.error('[adminServer] Error counting future tasks:', error);
        return 0;
      }

      return count || 0;
    } catch (err) {
      console.error('[adminServer] Exception counting future tasks:', err);
      return 0;
    }
  };

  // Execute both queries in parallel
  Promise.all([getPendingTasksCount(), getFutureTasksCount()])
    .then(([pendingTasksCount, futureTasksCount]) => {
      res.json({
        status: workerStatus,
        pendingTasksCount,
        futureTasksCount,
        lastRunTime,
        output: workerOutput
      });
    })
    .catch(err => {
      console.error('[adminServer] Error getting worker status:', err);
      res.status(500).json({ error: err.message });
    });
});

// API endpoint to get task statistics by type
app.get('/api/tasks/by-type', async (req, res) => {
  if (!supabase) {
    return res.status(500).json({ error: 'Supabase client not initialized' });
  }

  try {
    // Get current timestamp for comparison
    const now = new Date().toISOString();
    const thirtyMinutesAgo = new Date(Date.now() - 30 * 60 * 1000).toISOString();

    // Get all tasks
    const { data: allTasks, error: tasksError } = await supabase
      .from('t_task_queue')
      .select('task_type, status, scheduled_at, processed_at');

    if (tasksError) {
      console.error('[adminServer] Error getting tasks:', tasksError);
      return res.status(500).json({ error: tasksError.message });
    }

    // Process tasks into categories
    const pendingTasks = [];
    const futureTasks = [];
    const completedTasks = [];
    const errorTasks = [];

    allTasks.forEach(task => {
      if (task.status === 'pending' && new Date(task.scheduled_at) <= new Date(now)) {
        pendingTasks.push(task);
      } else if (task.status === 'pending' && new Date(task.scheduled_at) > new Date(now)) {
        futureTasks.push(task);
      } else if (task.status === 'complete' && task.processed_at && new Date(task.processed_at) > new Date(thirtyMinutesAgo)) {
        completedTasks.push(task);
      } else if (task.status === 'error' && task.processed_at && new Date(task.processed_at) > new Date(thirtyMinutesAgo)) {
        errorTasks.push(task);
      } else if (task.status === 'complete') {
        // Include all completed tasks if they don't have processed_at
        completedTasks.push(task);
      } else if (task.status === 'error') {
        // Include all error tasks if they don't have processed_at
        errorTasks.push(task);
      }
    });

    // Count tasks by type
    const pendingTasksByType = {};
    const futureTasksByType = {};
    const completedTasksByType = {};
    const errorTasksByType = {};

    pendingTasks.forEach(task => {
      pendingTasksByType[task.task_type] = (pendingTasksByType[task.task_type] || 0) + 1;
    });

    futureTasks.forEach(task => {
      futureTasksByType[task.task_type] = (futureTasksByType[task.task_type] || 0) + 1;
    });

    completedTasks.forEach(task => {
      completedTasksByType[task.task_type] = (completedTasksByType[task.task_type] || 0) + 1;
    });

    errorTasks.forEach(task => {
      errorTasksByType[task.task_type] = (errorTasksByType[task.task_type] || 0) + 1;
    });

    res.json({
      taskTypes: {
        pending: pendingTasksByType,
        future: futureTasksByType,
        completed: completedTasksByType,
        error: errorTasksByType
      }
    });
  } catch (err) {
    console.error('[adminServer] Exception getting tasks by type:', err);
    res.status(500).json({ error: err.message });
  }
});

// API endpoint to delete wrong Veeqo records
app.post('/api/delete-wrong-veeqo-records', (req, res) => {
  console.log('[adminServer] Running deleteWrongVeeqoRecords.js');

  // Get the limit parameter from the request body
  const limit = req.body.limit;
  console.log(`[adminServer] Limit parameter: ${limit}`);

  try {
    // Run the delete script with the limit parameter if provided
    const scriptArgs = ['deleteWrongVeeqoRecords.js'];
    if (limit) {
      scriptArgs.push(`--limit=${limit}`);
    }

    const deleteProcess = spawn('node', scriptArgs);

    let stdout = '';
    let stderr = '';
    let summary = null;

    deleteProcess.stdout.on('data', (data) => {
      const output = data.toString();
      stdout += output;
      console.log(`[deleteWrongVeeqoRecords.js] ${output.trim()}`);

      // Try to extract summary from the output
      const summaryMatch = output.match(/Total records processed: (\d+)[\s\S]*?Successful deletions: (\d+)[\s\S]*?Failed deletions: (\d+)/);

      if (summaryMatch) {
        summary = {
          totalProcessed: parseInt(summaryMatch[1]),
          successCount: parseInt(summaryMatch[2]),
          failureCount: parseInt(summaryMatch[3])
        };
      }
    });

    deleteProcess.stderr.on('data', (data) => {
      const output = data.toString();
      stderr += output;
      console.error(`[deleteWrongVeeqoRecords.js] ${output.trim()}`);
    });

    deleteProcess.on('close', (code) => {
      console.log(`[adminServer] deleteWrongVeeqoRecords.js exited with code ${code}`);

      if (code === 0) {
        res.json({
          success: true,
          message: 'Wrong Veeqo records deleted successfully',
          details: {
            stdout
          },
          summary: summary
        });
      } else {
        res.status(500).json({
          success: false,
          error: 'Failed to delete wrong Veeqo records',
          stdout,
          stderr
        });
      }
    });

    deleteProcess.on('error', (err) => {
      console.error('[adminServer] Error running delete script:', err);
      res.status(500).json({
        success: false,
        error: err.message
      });
    });
  } catch (err) {
    console.error(`[adminServer] Exception deleting wrong Veeqo records: ${err.message}`);
    res.status(500).json({
      success: false,
      error: err.message
    });
  }
});

// API endpoint to run the worker once
app.post('/api/worker/run-once', (req, res) => {
  console.log('[adminServer] Running worker once');

  try {
    // Clear previous output
    workerOutput = [];

    // Kill any existing worker process
    if (workerProcess) {
      console.log('[adminServer] Killing existing worker process');
      workerProcess.kill();
      workerProcess = null;
    }

    // Set the last run time
    lastRunTime = new Date().toISOString();

    // Spawn the worker process
    workerProcess = spawn('node', ['taskQueueWorker.js']);
    workerStatus = 'running';

    // Collect stdout
    workerProcess.stdout.on('data', (data) => {
      const output = data.toString();
      console.log(`[taskQueueWorker.js] ${output.trim()}`);
      updateWorkerOutput(output);
    });

    // Collect stderr
    workerProcess.stderr.on('data', (data) => {
      const output = data.toString();
      console.error(`[taskQueueWorker.js] ${output.trim()}`);
      updateWorkerOutput(`ERROR: ${output}`);
    });

    // Handle process completion
    workerProcess.on('close', (code) => {
      console.log(`[adminServer] Worker process exited with code ${code}`);
      workerStatus = 'stopped';
      workerProcess = null;
    });

    // Handle process error
    workerProcess.on('error', (err) => {
      console.error('[adminServer] Error spawning worker process:', err);
      updateWorkerOutput(`ERROR: Failed to start worker: ${err.message}`);
      workerStatus = 'stopped';
      workerProcess = null;

      res.status(500).json({
        success: false,
        error: err.message
      });
    });

    // Return success immediately, don't wait for process to complete
    res.json({
      success: true,
      message: 'Worker started successfully'
    });
  } catch (err) {
    console.error(`[adminServer] Exception running worker: ${err.message}`);
    res.status(500).json({
      success: false,
      error: err.message
    });
  }
});

// API endpoint to start the worker daemon
app.post('/api/worker/start-daemon', (req, res) => {
  console.log('[adminServer] Starting worker daemon');

  try {
    // Clear previous output
    workerOutput = [];

    // Kill any existing worker process
    if (workerProcess) {
      console.log('[adminServer] Killing existing worker process');
      workerProcess.kill();
      workerProcess = null;
    }

    // Set the last run time
    lastRunTime = new Date().toISOString();

    // Spawn the worker process in daemon mode
    workerProcess = spawn('node', ['runTaskQueueWorker.js']);
    workerStatus = 'running';

    // Collect stdout
    workerProcess.stdout.on('data', (data) => {
      const output = data.toString();
      console.log(`[runTaskQueueWorker.js] ${output.trim()}`);
      updateWorkerOutput(output);
    });

    // Collect stderr
    workerProcess.stderr.on('data', (data) => {
      const output = data.toString();
      console.error(`[runTaskQueueWorker.js] ${output.trim()}`);
      updateWorkerOutput(`ERROR: ${output}`);
    });

    // Handle process completion
    workerProcess.on('close', (code) => {
      console.log(`[adminServer] Worker daemon process exited with code ${code}`);
      workerStatus = 'stopped';
      workerProcess = null;
    });

    // Handle process error
    workerProcess.on('error', (err) => {
      console.error('[adminServer] Error spawning worker daemon process:', err);
      updateWorkerOutput(`ERROR: Failed to start worker daemon: ${err.message}`);
      workerStatus = 'stopped';
      workerProcess = null;

      res.status(500).json({
        success: false,
        error: err.message
      });
    });

    // Return success immediately, don't wait for process to complete
    res.json({
      success: true,
      message: 'Worker daemon started successfully'
    });
  } catch (err) {
    console.error(`[adminServer] Exception starting worker daemon: ${err.message}`);
    res.status(500).json({
      success: false,
      error: err.message
    });
  }
});

// API endpoint to stop the worker daemon
app.post('/api/worker/stop-daemon', (req, res) => {
  console.log('[adminServer] Stopping worker daemon');

  try {
    if (workerProcess) {
      console.log('[adminServer] Killing worker process');
      workerProcess.kill();
      workerProcess = null;
      workerStatus = 'stopped';
      updateWorkerOutput('Worker daemon stopped by user');

      res.json({
        success: true,
        message: 'Worker daemon stopped successfully'
      });
    } else {
      console.log('[adminServer] No worker process to stop');
      res.json({
        success: true,
        message: 'No worker daemon running'
      });
    }
  } catch (err) {
    console.error(`[adminServer] Exception stopping worker daemon: ${err.message}`);
    res.status(500).json({
      success: false,
      error: err.message
    });
  }
});

// API endpoint to import Veeqo sellables
app.post('/api/import-veeqo-sellables', async (req, res) => {
  console.log('[adminServer] Truncating imported_table_veeqo_sellables_export and running import_veeqo_sellables.js');

  try {
    if (!supabase) {
      return res.status(500).json({ error: 'Supabase client not initialized' });
    }

    // First, truncate the table
    const { error: truncateError } = await supabase.rpc('truncate_imported_table_veeqo_sellables_export');

    if (truncateError) {
      console.error(`[adminServer] Error truncating table: ${truncateError.message}`);
      return res.status(500).json({
        success: false,
        error: truncateError.message
      });
    }

    console.log('[adminServer] Table truncated successfully, running import script...');

    // Run the import script
    const importProcess = spawn('node', ['import_veeqo_sellables.js']);

    let stdout = '';
    let stderr = '';

    importProcess.stdout.on('data', (data) => {
      const output = data.toString();
      stdout += output;
      console.log(`[import_veeqo_sellables.js] ${output.trim()}`);
    });

    importProcess.stderr.on('data', (data) => {
      const output = data.toString();
      stderr += output;
      console.error(`[import_veeqo_sellables.js] ${output.trim()}`);
    });

    importProcess.on('close', (code) => {
      console.log(`[adminServer] import_veeqo_sellables.js exited with code ${code}`);

      if (code === 0) {
        res.json({
          success: true,
          message: 'Veeqo sellables imported successfully',
          details: {
            stdout
          }
        });
      } else {
        res.status(500).json({
          success: false,
          error: 'Failed to import Veeqo sellables',
          stdout,
          stderr
        });
      }
    });

    importProcess.on('error', (err) => {
      console.error('[adminServer] Error running import script:', err);
      res.status(500).json({
        success: false,
        error: err.message
      });
    });
  } catch (err) {
    console.error(`[adminServer] Exception importing Veeqo sellables: ${err.message}`);
    res.status(500).json({
      success: false,
      error: err.message
    });
  }
});

// API endpoint to import Shopify Matrixify export to imported_table_shopify_products_dz
app.post('/api/import-shopify-matrixify', (req, res) => {
  console.log('[adminServer] Running import_shopify_matrixify.js');

  try {
    // Run the import script
    const importProcess = spawn('node', ['import_shopify_matrixify.js']);

    let stdout = '';
    let stderr = '';

    importProcess.stdout.on('data', (data) => {
      const output = data.toString();
      stdout += output;
      console.log(`[import_shopify_matrixify.js] ${output.trim()}`);
    });

    importProcess.stderr.on('data', (data) => {
      const output = data.toString();
      stderr += output;
      console.error(`[import_shopify_matrixify.js] ${output.trim()}`);
    });

    importProcess.on('close', (code) => {
      console.log(`[adminServer] import_shopify_matrixify.js exited with code ${code}`);

      if (code === 0) {
        res.json({
          success: true,
          message: 'Shopify Matrixify export imported successfully to imported_table_shopify_products_dz',
          details: {
            stdout
          }
        });
      } else {
        res.status(500).json({
          success: false,
          error: 'Failed to import Shopify Matrixify export to imported_table_shopify_products_dz',
          stdout,
          stderr
        });
      }
    });

    importProcess.on('error', (err) => {
      console.error('[adminServer] Error running import script:', err);
      res.status(500).json({
        success: false,
        error: err.message
      });
    });
  } catch (err) {
    console.error(`[adminServer] Exception importing Shopify Matrixify export: ${err.message}`);
    res.status(500).json({
      success: false,
      error: err.message
    });
  }
});

// API endpoint to reconcile discs to Veeqo
app.post('/api/reconcile-d-to-veeqo', (req, res) => {
  console.log('[adminServer] Running reconcileDToVeeqo.js');

  try {
    // Run the reconcile script
    const reconcileProcess = spawn('node', ['reconcileDToVeeqo.js']);

    let stdout = '';
    let stderr = '';
    let summary = null;

    reconcileProcess.stdout.on('data', (data) => {
      const output = data.toString();
      stdout += output;
      console.log(`[reconcileDToVeeqo.js] ${output.trim()}`);

      // Try to extract summary from the output
      const summaryMatch = output.match(/Total records processed: (\d+)[\s\S]*?Successful updates: (\d+)[\s\S]*?Failed updates: (\d+)/);

      if (summaryMatch) {
        summary = {
          totalProcessed: parseInt(summaryMatch[1]),
          successCount: parseInt(summaryMatch[2]),
          failureCount: parseInt(summaryMatch[3])
        };
      }
    });

    reconcileProcess.stderr.on('data', (data) => {
      const output = data.toString();
      stderr += output;
      console.error(`[reconcileDToVeeqo.js] ${output.trim()}`);
    });

    reconcileProcess.on('close', (code) => {
      console.log(`[adminServer] reconcileDToVeeqo.js exited with code ${code}`);

      if (code === 0) {
        res.json({
          success: true,
          message: 'Disc to Veeqo reconciliation completed successfully',
          details: {
            stdout
          },
          summary: summary
        });
      } else {
        res.status(500).json({
          success: false,
          error: 'Failed to reconcile discs to Veeqo',
          stdout,
          stderr
        });
      }
    });

    reconcileProcess.on('error', (err) => {
      console.error('[adminServer] Error running reconcile script:', err);
      res.status(500).json({
        success: false,
        error: err.message
      });
    });
  } catch (err) {
    console.error(`[adminServer] Exception reconciling discs to Veeqo: ${err.message}`);
    res.status(500).json({
      success: false,
      error: err.message
    });
  }
});

// API endpoint to reconcile OSL stats into t_inv_osl
app.post('/api/reconcile-osl-stats', (req, res) => {
  console.log('[adminServer] Running reconcileOslStats.js');

  try {
    // Run the reconcile script
    const reconcileProcess = spawn('node', ['reconcileOslStats.js']);

    let stdout = '';
    let stderr = '';
    let summary = null;

    reconcileProcess.stdout.on('data', (data) => {
      const output = data.toString();
      stdout += output;
      console.log(`[reconcileOslStats.js] ${output.trim()}`);

      // Try to extract summary from the output
      const totalBatchesMatch = output.match(/Total batches: (\d+)/);
      const totalUpdatedMatch = output.match(/Total records updated: (\d+)/);

      if (totalBatchesMatch && totalUpdatedMatch) {
        summary = {
          totalBatches: parseInt(totalBatchesMatch[1]),
          totalUpdated: parseInt(totalUpdatedMatch[1])
        };
      }
    });

    reconcileProcess.stderr.on('data', (data) => {
      const output = data.toString();
      stderr += output;
      console.error(`[reconcileOslStats.js] ${output.trim()}`);
    });

    reconcileProcess.on('close', (code) => {
      console.log(`[adminServer] reconcileOslStats.js exited with code ${code}`);

      if (code === 0) {
        res.json({
          success: true,
          message: 'OSL stats reconciliation completed successfully',
          details: {
            stdout
          },
          summary: summary
        });
      } else {
        res.status(500).json({
          success: false,
          error: 'Failed to reconcile OSL stats',
          stdout,
          stderr
        });
      }
    });

    reconcileProcess.on('error', (err) => {
      console.error('[adminServer] Error running reconcile script:', err);
      res.status(500).json({
        success: false,
        error: err.message
      });
    });
  } catch (err) {
    console.error(`[adminServer] Exception reconciling OSL stats: ${err.message}`);
    res.status(500).json({
      success: false,
      error: err.message
    });
  }
});

// API endpoint to reconcile SDAsin stats into t_inv_sdasin
app.post('/api/reconcile-sdasin-stats', (req, res) => {
  console.log('[adminServer] Running reconcileSdasinStats.js');

  try {
    // Run the reconcile script
    const reconcileProcess = spawn('node', ['reconcileSdasinStats.js']);

    let stdout = '';
    let stderr = '';
    let summary = null;

    reconcileProcess.stdout.on('data', (data) => {
      const output = data.toString();
      stdout += output;
      console.log(`[reconcileSdasinStats.js] ${output.trim()}`);

      // Try to extract summary from the output
      const totalBatchesMatch = output.match(/Total batches: (\d+)/);
      const totalUpdatedMatch = output.match(/Total records updated: (\d+)/);

      if (totalBatchesMatch && totalUpdatedMatch) {
        summary = {
          totalBatches: parseInt(totalBatchesMatch[1]),
          totalUpdated: parseInt(totalUpdatedMatch[1])
        };
      }
    });

    reconcileProcess.stderr.on('data', (data) => {
      const output = data.toString();
      stderr += output;
      console.error(`[reconcileSdasinStats.js] ${output.trim()}`);
    });

    reconcileProcess.on('close', (code) => {
      console.log(`[adminServer] reconcileSdasinStats.js exited with code ${code}`);

      if (code === 0) {
        res.json({
          success: true,
          message: 'SDAsin stats reconciliation completed successfully',
          details: {
            stdout
          },
          summary: summary
        });
      } else {
        res.status(500).json({
          success: false,
          error: 'Failed to reconcile SDAsin stats',
          stdout,
          stderr
        });
      }
    });

    reconcileProcess.on('error', (err) => {
      console.error('[adminServer] Error running reconcile script:', err);
      res.status(500).json({
        success: false,
        error: err.message
      });
    });
  } catch (err) {
    console.error(`[adminServer] Exception reconciling SDAsin stats: ${err.message}`);
    res.status(500).json({
      success: false,
      error: err.message
    });
  }
});

// API endpoint to reconcile OSLs to Veeqo
app.post('/api/reconcile-osl-to-veeqo', async (req, res) => {
  console.log('[adminServer] Running reconcileOslToVeeqo.js');

  try {
    // Import the reconcileOslToVeeqo module
    const reconcileOslToVeeqo = (await import('./reconcileOslToVeeqo.js')).default;

    // Run the reconcile function
    const result = await reconcileOslToVeeqo();
    const { tasks, summary } = result;

    // Return the results
    res.json({
      success: true,
      message: `Successfully processed ${summary.totalProcessed} records (${summary.skippedRecords} skipped due to missing available_quantity) and enqueued ${summary.tasksEnqueued} tasks to update OSL quantities in Veeqo.`,
      count: tasks.length,
      tasks: tasks,
      summary: summary
    });
  } catch (err) {
    console.error(`[adminServer] Exception reconciling OSLs to Veeqo: ${err.message}`);
    res.status(500).json({
      success: false,
      error: err.message
    });
  }
});

// API endpoint to enqueue generate_osl_fields tasks for OSLs with null g_code
app.post('/api/enqueue-generate-osl-fields', async (req, res) => {
  console.log('[adminServer] Enqueueing generate_osl_fields tasks for OSLs with null g_code');

  try {
    if (!supabase) {
      return res.status(500).json({ error: 'Supabase client not initialized' });
    }

    // Get the batch size from the request body
    const batchSize = req.body.batchSize || 100;
    console.log(`[adminServer] Batch size: ${batchSize}`);

    // Call the function to enqueue tasks
    const { data, error } = await supabase.rpc(
      'fn_enqueue_generate_osl_fields_for_null_g_code',
      { batch_size: batchSize }
    );

    if (error) {
      console.error(`[adminServer] Error calling fn_enqueue_generate_osl_fields_for_null_g_code: ${error.message}`);
      return res.status(500).json({
        success: false,
        error: error.message
      });
    }

    // Return the results
    res.json({
      success: true,
      message: `Successfully enqueued ${data.length} tasks for OSLs with null g_code.`,
      count: data.length,
      tasks: data
    });
  } catch (err) {
    console.error(`[adminServer] Exception enqueueing generate_osl_fields tasks: ${err.message}`);
    res.status(500).json({
      success: false,
      error: err.message
    });
  }
});

// Try multiple ports if the default one is in use
function startServer(port) {
  const localIP = getLocalIPAddress();
  const server = app.listen(port, '0.0.0.0')
    .on('listening', () => {
      console.log(`[adminServer] Server running on port ${port} (LAN accessible)`);
      console.log(`[adminServer] Local access: http://localhost:${port}/admin.html`);
      console.log(`[adminServer] LAN access: http://${localIP}:${port}/admin.html`);
      console.log(`[adminServer] ToDo interface (LAN): http://${localIP}:${port}/ToDo.html`);
    })
    .on('error', (err) => {
      if (err.code === 'EADDRINUSE') {
        console.log(`[adminServer] Port ${port} is already in use, trying port ${port + 1}...`);
        server.close();
        startServer(port + 1);
      } else {
        console.error('[adminServer] Error starting server:', err);
      }
    });
}

// API endpoint for importing discs from Google Sheets
app.post('/api/import-discs-from-sheets', async (req, res) => {
  console.log('[adminServer] Import discs from Google Sheets request received');

  try {
    const { googleSheetsUrl, validateOnly } = req.body;

    if (!googleSheetsUrl) {
      return res.status(400).json({
        success: false,
        error: 'Google Sheets URL is required'
      });
    }

    console.log(`[adminServer] Processing Google Sheets import: ${googleSheetsUrl}, validateOnly: ${validateOnly}`);

    const result = await importDiscsFromGoogleSheets(googleSheetsUrl, validateOnly);

    res.json(result);

  } catch (err) {
    console.error(`[adminServer] Exception importing discs from Google Sheets: ${err.message}`);
    res.status(500).json({
      success: false,
      error: err.message,
      details: err.stack
    });
  }
});

// API endpoint for Google Sheets to lookup g_code by mps_id
app.get('/api/lookup-gcode/:mpsId', async (req, res) => {
  console.log('[adminServer] G-code lookup request received');

  try {
    const { mpsId } = req.params;

    if (!mpsId || isNaN(parseInt(mpsId))) {
      return res.status(400).json({
        success: false,
        error: 'Valid mps_id is required'
      });
    }

    console.log(`[adminServer] Looking up g_code for mps_id: ${mpsId}`);

    const { data, error } = await supabase
      .from('t_mps')
      .select('g_code')
      .eq('id', parseInt(mpsId))
      .single();

    if (error) {
      if (error.code === 'PGRST116') {
        // No rows returned
        return res.json({
          success: true,
          gCode: null,
          message: 'MPS ID not found'
        });
      }
      throw error;
    }

    res.json({
      success: true,
      gCode: data.g_code,
      mpsId: parseInt(mpsId)
    });

  } catch (err) {
    console.error(`[adminServer] Exception looking up g_code: ${err.message}`);
    res.status(500).json({
      success: false,
      error: err.message
    });
  }
});

// Register Informed API endpoints
registerInformedApiEndpoints(app);

// Discraft API endpoints
app.post('/api/discraft/import', (req, res) => {
  console.log('[adminServer] Running Discraft import');

  try {
    const importProcess = spawn('node', ['fullDiscraftImport.js']);
    let stdout = '';
    let stderr = '';
    let importResults = {};

    importProcess.stdout.on('data', (data) => {
      const output = data.toString();
      stdout += output;
      console.log(`[fullDiscraftImport.js] ${output.trim()}`);

      const resultsMatch = output.match(/Successfully imported (\d+) records/);
      if (resultsMatch) {
        importResults.totalProducts = parseInt(resultsMatch[1]);
      }

      const batchMatch = output.match(/Import batch ID: ([a-f0-9-]+)/);
      if (batchMatch) {
        importResults.batchId = batchMatch[1];
      }
    });

    importProcess.stderr.on('data', (data) => {
      const output = data.toString();
      stderr += output;
      console.error(`[fullDiscraftImport.js] ${output.trim()}`);
    });

    importProcess.on('close', (code) => {
      console.log(`[adminServer] fullDiscraftImport.js exited with code ${code}`);

      if (code === 0) {
        res.json({
          success: true,
          message: 'Discraft import completed successfully',
          totalProducts: importResults.totalProducts,
          batchId: importResults.batchId,
          details: { stdout }
        });
      } else {
        res.status(500).json({
          success: false,
          error: 'Failed to import Discraft catalog',
          stdout,
          stderr
        });
      }
    });

    importProcess.on('error', (err) => {
      console.error('[adminServer] Error running Discraft import:', err);
      res.status(500).json({
        success: false,
        error: err.message
      });
    });
  } catch (err) {
    console.error(`[adminServer] Exception running Discraft import: ${err.message}`);
    res.status(500).json({
      success: false,
      error: err.message
    });
  }
});

app.get('/api/discraft/status', async (req, res) => {
  console.log('[adminServer] Getting Discraft status');

  try {
    if (!supabase) {
      return res.status(500).json({ error: 'Supabase client not initialized' });
    }

    const { count: totalProducts, error: totalError } = await supabase
      .from('it_discraft_order_sheet_lines')
      .select('*', { count: 'exact', head: true });

    if (totalError) {
      console.error('[adminServer] Error getting total products:', totalError);
      return res.status(500).json({ error: totalError.message });
    }

    const { count: availableProducts, error: availableError } = await supabase
      .from('it_discraft_order_sheet_lines')
      .select('*', { count: 'exact', head: true })
      .eq('is_currently_available', true);

    if (availableError) {
      console.error('[adminServer] Error getting available products:', availableError);
      return res.status(500).json({ error: availableError.message });
    }

    const { count: outOfStockProducts, error: outOfStockError } = await supabase
      .from('it_discraft_order_sheet_lines')
      .select('*', { count: 'exact', head: true })
      .eq('is_currently_available', false);

    if (outOfStockError) {
      console.error('[adminServer] Error getting out of stock products:', outOfStockError);
      return res.status(500).json({ error: outOfStockError.message });
    }

    const { data: mappingKeys, error: mappingError } = await supabase
      .from('it_discraft_order_sheet_lines')
      .select('excel_mapping_key');

    if (mappingError) {
      console.error('[adminServer] Error getting mapping keys:', mappingError);
      return res.status(500).json({ error: mappingError.message });
    }

    const uniqueKeys = new Set(mappingKeys.map(row => row.excel_mapping_key)).size;

    const { data: lastImportData, error: lastImportError } = await supabase
      .from('it_discraft_order_sheet_lines')
      .select('created_at')
      .order('created_at', { ascending: false })
      .limit(1);

    let lastImport = 'Never';
    if (!lastImportError && lastImportData && lastImportData.length > 0) {
      lastImport = new Date(lastImportData[0].created_at).toLocaleString();
    }

    res.json({
      success: true,
      totalProducts: totalProducts || 0,
      availableProducts: availableProducts || 0,
      outOfStockProducts: outOfStockProducts || 0,
      uniqueKeys: uniqueKeys || 0,
      lastImport
    });

  } catch (err) {
    console.error('[adminServer] Exception getting Discraft status:', err);
    res.status(500).json({
      success: false,
      error: err.message
    });
  }
});

app.post('/api/discraft/calculate-mps', async (req, res) => {
  console.log('[adminServer] Calculating Discraft MPS IDs');
  try {
    // Get all Discraft products in chunks for resetting
    console.log('[adminServer] Resetting existing MPS IDs...');
    let allProducts = [];
    let offset = 0;
    const chunkSize = 1000;

    while (true) {
      const { data: productChunk, error: chunkError } = await supabase
        .from('it_discraft_order_sheet_lines')
        .select('id')
        .range(offset, offset + chunkSize - 1);

      if (chunkError) {
        console.error('[adminServer] Error getting products chunk:', chunkError);
        return res.status(500).json({ error: chunkError.message });
      }

      if (!productChunk || productChunk.length === 0) break;

      allProducts = allProducts.concat(productChunk);

      if (productChunk.length < chunkSize) break;
      offset += chunkSize;
    }

    // Reset all calculated_mps_id to NULL (with WHERE clause)
    for (const product of allProducts) {
      await supabase
        .from('it_discraft_order_sheet_lines')
        .update({ calculated_mps_id: null })
        .eq('id', product.id);
    }

    // Get all Discraft products in chunks
    console.log('[adminServer] Getting Discraft products...');
    let allDiscraftProducts = [];
    offset = 0;

    while (true) {
      const { data: productChunk, error: discraftError } = await supabase
        .from('it_discraft_order_sheet_lines')
        .select('id, plastic_name, mold_name, stamp_name')
        .range(offset, offset + chunkSize - 1);

      if (discraftError) {
        console.error('[adminServer] Error getting Discraft products:', discraftError);
        return res.status(500).json({ error: discraftError.message });
      }

      if (!productChunk || productChunk.length === 0) break;

      allDiscraftProducts = allDiscraftProducts.concat(productChunk);

      if (productChunk.length < chunkSize) break;
      offset += chunkSize;
    }

    // Get all Discraft MPS records
    console.log('[adminServer] Getting Discraft MPS records...');
    const { data: mpsRecords, error: mpsError } = await supabase
      .from('t_mps')
      .select(`id, t_plastics!inner(plastic, brand_id), t_molds!inner(mold, brand_id), t_stamps!inner(stamp)`)
      .eq('active', true)
      .eq('t_plastics.brand_id', 6)
      .eq('t_molds.brand_id', 6);

    if (mpsError) {
      console.error('[adminServer] Error getting MPS records:', mpsError);
      return res.status(500).json({ error: mpsError.message });
    }

    // Create lookup map
    const mpsMap = new Map();
    mpsRecords.forEach(mps => {
      const key = `${mps.t_plastics.plastic.trim()}|${mps.t_molds.mold.trim()}|${mps.t_stamps.stamp.trim()}`;
      mpsMap.set(key, mps.id);
    });

    // Match products to MPS records
    console.log('[adminServer] Matching products to MPS records...');
    let matchedCount = 0;

    for (const product of allDiscraftProducts) {
      const key = `${product.plastic_name.trim()}|${product.mold_name.trim()}|${product.stamp_name.trim()}`;
      const mpsId = mpsMap.get(key);

      if (mpsId) {
        await supabase
          .from('it_discraft_order_sheet_lines')
          .update({ calculated_mps_id: mpsId })
          .eq('id', product.id);
        matchedCount++;
      }
    }

    const failedCount = allDiscraftProducts.length - matchedCount;
    const successRate = ((matchedCount / allDiscraftProducts.length) * 100).toFixed(2);

    res.json({
      success: true,
      totalProducts: allDiscraftProducts.length,
      calculatedCount: matchedCount,
      failedCount: failedCount,
      successRate: parseFloat(successRate)
    });
  } catch (err) {
    console.error('[adminServer] Exception calculating MPS IDs:', err);
    res.status(500).json({ success: false, error: err.message });
  }
});

app.get('/api/discraft/analyze-matching', async (req, res) => {
  console.log('[adminServer] Analyzing Discraft matching');
  try {
    // Get total counts using count queries instead of loading all data
    const { count: totalProducts, error: totalError } = await supabase
      .from('it_discraft_order_sheet_lines')
      .select('*', { count: 'exact', head: true });

    if (totalError) throw totalError;

    const { count: withMpsId, error: withMpsError } = await supabase
      .from('it_discraft_order_sheet_lines')
      .select('*', { count: 'exact', head: true })
      .not('calculated_mps_id', 'is', null);

    if (withMpsError) throw withMpsError;

    const withoutMpsId = totalProducts - withMpsId;
    const mpsSuccessRate = ((withMpsId / totalProducts) * 100).toFixed(1);
    // Get unmatched products in chunks
    let allUnmatched = [];
    let offset = 0;
    const chunkSize = 1000;

    while (true) {
      const { data: unmatchedChunk, error: unmatchedError } = await supabase
        .from('it_discraft_order_sheet_lines')
        .select('plastic_name, mold_name, stamp_name')
        .is('calculated_mps_id', null)
        .range(offset, offset + chunkSize - 1);

      if (unmatchedError) throw unmatchedError;

      if (!unmatchedChunk || unmatchedChunk.length === 0) break;

      allUnmatched = allUnmatched.concat(unmatchedChunk);

      if (unmatchedChunk.length < chunkSize) break;
      offset += chunkSize;
    }

    const unmatchedGroups = {};
    allUnmatched.forEach(row => {
      const key = `${row.plastic_name}|${row.mold_name}|${row.stamp_name}`;
      unmatchedGroups[key] = (unmatchedGroups[key] || 0) + 1;
    });
    const topUnmatched = Object.entries(unmatchedGroups)
      .sort(([,a], [,b]) => b - a)
      .slice(0, 10)
      .map(([key, count]) => {
        const [plastic_name, mold_name, stamp_name] = key.split('|');
        return { plastic_name, mold_name, stamp_name, count };
      });
    res.json({ success: true, withMpsId, withoutMpsId, mpsSuccessRate, topUnmatched });
  } catch (err) {
    console.error('[adminServer] Exception analyzing matching:', err);
    res.status(500).json({ success: false, error: err.message });
  }
});

app.get('/api/discraft/find-osl-matches', async (req, res) => {
  console.log('[adminServer] Finding OSL to Discraft matches');
  try {
    if (!supabase) {
      return res.status(500).json({ error: 'Supabase client not initialized' });
    }
    const { data: matches, error: matchError } = await supabase.rpc('find_discraft_osl_matches');
    if (matchError) {
      console.error('[adminServer] Error finding OSL matches:', matchError);
      return res.status(500).json({ error: matchError.message });
    }
    const perfectMatches = matches.filter(m =>
      m.weight_status === 'COMPATIBLE' &&
      (m.color_status === 'ANY_COLOR_OK' || m.color_status === 'VARIES_OK' || m.color_status === 'COLOR_MATCH')
    ).length;
    const weightConflicts = matches.filter(m => m.weight_status === 'INCOMPATIBLE').length;
    const colorConflicts = matches.filter(m => m.color_status === 'COLOR_MISMATCH').length;
    const uniqueOslsWithMatches = new Set(matches.map(m => m.osl_id)).size;
    const oslMatchCounts = {};
    matches.forEach(m => {
      oslMatchCounts[m.osl_id] = (oslMatchCounts[m.osl_id] || 0) + 1;
    });
    const multipleMatches = Object.values(oslMatchCounts).filter(count => count > 1).length;
    const { count: totalDiscraftOsls, error: countError } = await supabase
      .from('t_order_sheet_lines')
      .select('*', { count: 'exact', head: true })
      .eq('vendor_id', 9);
    if (countError) throw countError;
    const noMatches = (totalDiscraftOsls || 0) - uniqueOslsWithMatches;
    res.json({
      success: true, perfectMatches, weightConflicts, colorConflicts, multipleMatches, noMatches,
      totalOsls: totalDiscraftOsls || 0, matchDetails: matches.slice(0, 20)
    });
  } catch (err) {
    console.error('[adminServer] Exception finding OSL matches:', err);
    res.status(500).json({ success: false, error: err.message });
  }
});

app.get('/api/discraft/review-unmatched', async (req, res) => {
  console.log('[adminServer] Getting unmatched products for review');
  try {
    const { data: unmatchedProducts, error: productsError } = await supabase
      .from('it_discraft_order_sheet_lines')
      .select('plastic_name, mold_name, stamp_name, vendor_description, min_weight, max_weight, is_currently_available')
      .is('calculated_mps_id', null)
      .eq('is_orderable', true)
      .limit(50);
    if (productsError) throw productsError;

    // Get matched MPS IDs first
    const { data: matchedMpsIds, error: matchedError } = await supabase
      .from('it_discraft_order_sheet_lines')
      .select('calculated_mps_id')
      .not('calculated_mps_id', 'is', null);

    if (matchedError) throw matchedError;

    const matchedIds = matchedMpsIds.map(row => row.calculated_mps_id);

    const { data: unmatchedOsls, error: oslsError } = await supabase
      .from('t_order_sheet_lines')
      .select(`id, mps_id, min_weight, max_weight, color_id, t_mps!inner(t_plastics!inner(plastic), t_molds!inner(mold), t_stamps!inner(stamp))`)
      .eq('vendor_id', 9)
      .not('mps_id', 'in', `(${matchedIds.length > 0 ? matchedIds.join(',') : '0'})`)
      .limit(50);
    if (oslsError) throw oslsError;
    res.json({
      success: true,
      unmatchedProducts: unmatchedProducts || [],
      unmatchedOsls: unmatchedOsls || []
    });
  } catch (err) {
    console.error('[adminServer] Exception getting unmatched products:', err);
    res.status(500).json({ success: false, error: err.message });
  }
});

app.get('/api/discraft/review-unmatched-osls', async (req, res) => {
  console.log('[adminServer] Getting unmatched Discraft OSLs');
  try {
    const showInactive = req.query.showInactive === 'true';

    // Get matched MPS IDs first
    const { data: matchedMpsIds, error: matchedError } = await supabase
      .from('it_discraft_order_sheet_lines')
      .select('calculated_mps_id')
      .not('calculated_mps_id', 'is', null);

    if (matchedError) throw matchedError;

    const matchedIds = matchedMpsIds.map(row => row.calculated_mps_id);

    // Build query for unmatched OSLs
    let oslQuery = supabase
      .from('t_order_sheet_lines')
      .select(`id, mps_id, min_weight, max_weight, color_id, t_colors(color), t_mps!inner(t_plastics!inner(plastic), t_molds!inner(mold), t_stamps!inner(stamp), active)`)
      .eq('vendor_id', 9)
      .not('mps_id', 'in', `(${matchedIds.length > 0 ? matchedIds.join(',') : '0'})`);

    // Filter by MPS active status if not showing inactive
    if (!showInactive) {
      oslQuery = oslQuery.eq('t_mps.active', true);
    }

    const { data: unmatchedOsls, error: oslsError } = await oslQuery.limit(100);
    if (oslsError) throw oslsError;

    // Get total count of unmatched OSLs
    let countQuery = supabase
      .from('t_order_sheet_lines')
      .select('*', { count: 'exact', head: true })
      .eq('vendor_id', 9)
      .not('mps_id', 'in', `(${matchedIds.length > 0 ? matchedIds.join(',') : '0'})`);

    if (!showInactive) {
      // Need to join with t_mps to filter by active status for count
      countQuery = supabase
        .from('t_order_sheet_lines')
        .select('*, t_mps!inner(active)', { count: 'exact', head: true })
        .eq('vendor_id', 9)
        .eq('t_mps.active', true)
        .not('mps_id', 'in', `(${matchedIds.length > 0 ? matchedIds.join(',') : '0'})`);
    }

    const { count: totalCount, error: countError } = await countQuery;
    if (countError) throw countError;

    const oslsWithColors = unmatchedOsls.map(osl => ({ ...osl, color_name: osl.t_colors ? osl.t_colors.color : null }));

    res.json({
      success: true,
      unmatchedOsls: oslsWithColors,
      totalUnmatchedOsls: totalCount || 0,
      showInactive: showInactive
    });
  } catch (err) {
    console.error('[adminServer] Exception getting unmatched OSLs:', err);
    res.status(500).json({ success: false, error: err.message });
  }
});

// API endpoint to mark MPS as inactive for Discraft
app.post('/api/discraft/mark-mps-inactive', async (req, res) => {
  console.log('[adminServer] Marking Discraft MPS as inactive');
  try {
    const { mpsId } = req.body;

    if (!mpsId) {
      return res.status(400).json({
        success: false,
        error: 'MPS ID is required'
      });
    }

    // Update the MPS record to set active = false
    const { data, error } = await supabase
      .from('t_mps')
      .update({ active: false })
      .eq('id', mpsId)
      .select();

    if (error) throw error;

    if (!data || data.length === 0) {
      return res.status(404).json({
        success: false,
        error: 'MPS record not found'
      });
    }

    console.log(`[adminServer] Successfully marked MPS ID ${mpsId} as inactive`);

    res.json({
      success: true,
      message: `MPS ID ${mpsId} marked as inactive successfully`,
      updatedRecord: data[0]
    });
  } catch (err) {
    console.error('[adminServer] Exception marking MPS as inactive:', err);
    res.status(500).json({ success: false, error: err.message });
  }
});

// API endpoint to mark MPS as active for Discraft
app.post('/api/discraft/mark-mps-active', async (req, res) => {
  console.log('[adminServer] Marking Discraft MPS as active');
  try {
    const { mpsId } = req.body;

    if (!mpsId) {
      return res.status(400).json({
        success: false,
        error: 'MPS ID is required'
      });
    }

    // Update the MPS record to set active = true
    const { data, error } = await supabase
      .from('t_mps')
      .update({ active: true })
      .eq('id', mpsId)
      .select();

    if (error) throw error;

    if (!data || data.length === 0) {
      return res.status(404).json({
        success: false,
        error: 'MPS record not found'
      });
    }

    console.log(`[adminServer] Successfully marked MPS ID ${mpsId} as active`);

    res.json({
      success: true,
      message: `MPS ID ${mpsId} marked as active successfully`,
      updatedRecord: data[0]
    });
  } catch (err) {
    console.error('[adminServer] Exception marking MPS as active:', err);
    res.status(500).json({ success: false, error: err.message });
  }
});

app.get('/api/discraft/review-parsing-issues', async (req, res) => {
  console.log('[adminServer] Analyzing Discraft parsing issues');
  try {
    const { data: unmatchedProducts, error: productsError } = await supabase
      .from('it_discraft_order_sheet_lines')
      .select('plastic_name, mold_name, stamp_name, min_weight, max_weight, is_currently_available')
      .is('calculated_mps_id', null)
      .eq('is_orderable', true)
      .limit(30);
    if (productsError) throw productsError;
    const { data: dbPlastics, error: plasticsError } = await supabase.from('t_plastics').select('plastic').eq('brand_id', 6);
    if (plasticsError) throw plasticsError;
    const { data: dbMolds, error: moldsError } = await supabase.from('t_molds').select('mold').eq('brand_id', 6);
    if (moldsError) throw moldsError;
    const vendorPlastics = [...new Set(unmatchedProducts.map(p => p.plastic_name))];
    const dbPlasticNames = dbPlastics.map(p => p.plastic);
    const plasticMismatches = vendorPlastics.map(vendorPlastic => {
      const suggestions = dbPlasticNames.filter(dbPlastic =>
        dbPlastic.toLowerCase().includes(vendorPlastic.toLowerCase()) ||
        vendorPlastic.toLowerCase().includes(dbPlastic.toLowerCase())
      );
      return { vendor_plastic: vendorPlastic, db_suggestions: suggestions };
    }).slice(0, 10);
    const vendorMolds = [...new Set(unmatchedProducts.map(p => p.mold_name))];
    const dbMoldNames = dbMolds.map(m => m.mold);
    const moldMismatches = vendorMolds.map(vendorMold => {
      const suggestions = dbMoldNames.filter(dbMold =>
        dbMold.toLowerCase().includes(vendorMold.toLowerCase()) ||
        vendorMold.toLowerCase().includes(dbMold.toLowerCase())
      );
      return { vendor_mold: vendorMold, db_suggestions: suggestions };
    }).slice(0, 10);
    res.json({ success: true, unmatchedVendorProducts: unmatchedProducts, plasticMismatches, moldMismatches });
  } catch (err) {
    console.error('[adminServer] Exception analyzing parsing issues:', err);
    res.status(500).json({ success: false, error: err.message });
  }
});

// API endpoint to get MPS review data
app.get('/api/mps-review', async (req, res) => {
  console.log('[adminServer] Getting MPS review data');

  try {
    if (!supabase) {
      return res.status(500).json({ error: 'Supabase client not initialized' });
    }

    // Query for active MPS records with no stock using a raw SQL query
    const { data, error } = await supabase.rpc('get_mps_review_data');

    if (error) {
      console.error('[adminServer] Error getting MPS review data:', error);
      return res.status(500).json({
        success: false,
        error: error.message
      });
    }

    // Data is already in the correct format from the function
    const records = data || [];

    console.log(`[adminServer] Retrieved ${records.length} MPS records for review`);

    res.json({
      success: true,
      records: records
    });
  } catch (err) {
    console.error('[adminServer] Exception getting MPS review data:', err);
    res.status(500).json({
      success: false,
      error: err.message
    });
  }
});

// API endpoint to mark MPS as inactive
app.post('/api/mps-mark-inactive', async (req, res) => {
  console.log('[adminServer] Marking MPS as inactive');

  try {
    const { mpsId } = req.body;

    if (!mpsId) {
      return res.status(400).json({
        success: false,
        error: 'MPS ID is required'
      });
    }

    if (!supabase) {
      return res.status(500).json({ error: 'Supabase client not initialized' });
    }

    // Update the MPS record to set active = false
    const { data, error } = await supabase
      .from('t_mps')
      .update({ active: false })
      .eq('id', mpsId)
      .select();

    if (error) {
      console.error('[adminServer] Error marking MPS as inactive:', error);
      return res.status(500).json({
        success: false,
        error: error.message
      });
    }

    if (!data || data.length === 0) {
      return res.status(404).json({
        success: false,
        error: 'MPS record not found'
      });
    }

    console.log(`[adminServer] Successfully marked MPS ID ${mpsId} as inactive`);

    res.json({
      success: true,
      message: `MPS ID ${mpsId} marked as inactive successfully`,
      updatedRecord: data[0]
    });
  } catch (err) {
    console.error('[adminServer] Exception marking MPS as inactive:', err);
    res.status(500).json({
      success: false,
      error: err.message
    });
  }
});

// Add this API endpoint to handle Amazon FBA import
app.post('/api/import-amazon-fba', async (req, res) => {
  console.log('[adminServer] Running Amazon FBA import');

  try {
    // Import the Amazon import function
    const { importAmazonInventoryLedger } = await import('./importAmazonInventoryLedger.js');

    // Run the import
    const result = await importAmazonInventoryLedger();

    console.log(`[adminServer] Amazon FBA import completed: ${result.message}`);

    return res.json({
      success: true,
      message: result.message,
      details: result.details,
      importCount: result.importCount
    });
  } catch (err) {
    console.error(`[adminServer] Exception running Amazon FBA import: ${err.message}`);
    res.status(500).json({
      success: false,
      error: err.message,
      details: err.stack
    });
  }
});

// Add this API endpoint to handle FBA carrying costs refresh
app.post('/api/refresh-fba-carrying-costs', async (req, res) => {
  console.log('[adminServer] Refreshing FBA carrying costs materialized view');

  try {
    const startTime = Date.now();

    // Since Supabase doesn't allow direct REFRESH MATERIALIZED VIEW commands,
    // we'll use a stored procedure approach or manual refresh
    console.log('[adminServer] Attempting to refresh materialized view via stored procedure');

    // Try to call a stored procedure that refreshes the materialized view
    const { data, error } = await supabase.rpc('refresh_mv_sdasin_avg_carrying_cost_fba');

    if (error) {
      console.log('[adminServer] Stored procedure not found, materialized view may need manual refresh');
      console.log('[adminServer] Error:', error.message);

      // For now, we'll just get the current count and inform the user
      const { count, error: countError } = await supabase
        .from('mv_sdasin_avg_carrying_cost_fba')
        .select('*', { count: 'exact', head: true });

      if (countError) {
        throw new Error(`Failed to access materialized view: ${countError.message}`);
      }

      const endTime = Date.now();
      const duration = `${((endTime - startTime) / 1000).toFixed(2)} seconds`;

      return res.json({
        success: false,
        error: 'Materialized view refresh requires database admin privileges',
        details: `The materialized view currently has ${count || 'unknown'} records. To refresh it, you need to run "REFRESH MATERIALIZED VIEW mv_sdasin_avg_carrying_cost_fba;" directly in your database admin panel.`,
        recordCount: count || 'Unknown',
        duration: duration
      });
    }

    // If the stored procedure worked, get the updated count
    const { count, error: countError } = await supabase
      .from('mv_sdasin_avg_carrying_cost_fba')
      .select('*', { count: 'exact', head: true });

    const recordCount = countError ? 'Unknown' : count;

    const endTime = Date.now();
    const duration = `${((endTime - startTime) / 1000).toFixed(2)} seconds`;

    console.log(`[adminServer] FBA carrying costs refresh completed in ${duration}`);

    return res.json({
      success: true,
      message: 'Materialized view refreshed successfully',
      recordCount: recordCount,
      duration: duration,
      details: `Refreshed mv_sdasin_avg_carrying_cost_fba with ${recordCount} records`
    });
  } catch (err) {
    console.error(`[adminServer] Exception refreshing FBA carrying costs: ${err.message}`);
    res.status(500).json({
      success: false,
      error: err.message,
      details: err.stack
    });
  }
});

// Add this API endpoint to handle FBA inventory report generation
app.post('/api/generate-fba-inventory-report', async (req, res) => {
  console.log('[adminServer] Generating FBA inventory value report');

  try {
    const startTime = Date.now();

    // Call the stored procedure to generate the report
    const { data, error } = await supabase.rpc('fn_upsert_monthly_fba_inventory_value');

    if (error) {
      console.log('[adminServer] Stored procedure error:', error.message);

      // If the function doesn't exist, provide helpful error message
      if (error.message.includes('function') && error.message.includes('does not exist')) {
        return res.json({
          success: false,
          error: 'FBA inventory report function not found',
          details: `The function fn_upsert_monthly_fba_inventory_value() does not exist. Please run the SQL from update_fba_inventory_function.sql in your Supabase SQL Editor to create it.`
        });
      }

      throw new Error(`Failed to generate FBA inventory report: ${error.message}`);
    }

    // Extract results from the function return
    const result = data && data.length > 0 ? data[0] : {};

    const endTime = Date.now();
    const totalDuration = `${((endTime - startTime) / 1000).toFixed(2)} seconds`;

    console.log(`[adminServer] FBA inventory report completed in ${totalDuration}`);
    console.log(`[adminServer] Report results:`, result);

    // Format the report month for display
    const reportMonth = result.report_month ?
      new Date(result.report_month).toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'long'
      }) : 'Unknown';

    // Format processing time
    const processingTime = result.processing_time || totalDuration;

    return res.json({
      success: true,
      message: 'FBA inventory report generated successfully',
      reportMonth: reportMonth,
      processedRecords: result.processed_records || 0,
      totalUnits: result.total_units || 0,
      totalValue: result.total_value || 0,
      weightedAvgCost: result.weighted_avg_cost || 0,
      processingTime: processingTime,
      details: `Generated report for ${reportMonth}\n` +
               `Processed ${result.processed_records || 0} records\n` +
               `Total FBA Units: ${(result.total_units || 0).toLocaleString()}\n` +
               `Total FBA Value: $${(result.total_value || 0).toLocaleString()}\n` +
               `Weighted Avg Cost: $${result.weighted_avg_cost || 0}\n` +
               `Processing Time: ${processingTime}`
    });
  } catch (err) {
    console.error(`[adminServer] Exception generating FBA inventory report: ${err.message}`);
    res.status(500).json({
      success: false,
      error: err.message,
      details: err.stack
    });
  }
});

// Add this API endpoint to handle historical FBA reports generation
app.post('/api/generate-historical-fba-reports', async (req, res) => {
  console.log('[adminServer] Generating historical FBA inventory value reports');

  try {
    const startTime = Date.now();

    // Call the stored procedure to generate historical reports
    const { data, error } = await supabase.rpc('fn_generate_historical_fba_inventory_values');

    if (error) {
      console.log('[adminServer] Historical reports stored procedure error:', error.message);

      // If the function doesn't exist, provide helpful error message
      if (error.message.includes('function') && error.message.includes('does not exist')) {
        return res.json({
          success: false,
          error: 'Historical FBA reports function not found',
          details: `The function fn_generate_historical_fba_inventory_values() does not exist. Please run the SQL from create_historical_fba_function.sql in your Supabase SQL Editor to create it.`
        });
      }

      throw new Error(`Failed to generate historical FBA reports: ${error.message}`);
    }

    // Extract results from the function return
    const result = data && data.length > 0 ? data[0] : {};

    const endTime = Date.now();
    const totalDuration = `${((endTime - startTime) / 1000).toFixed(2)} seconds`;

    console.log(`[adminServer] Historical FBA reports completed in ${totalDuration}`);
    console.log(`[adminServer] Historical results:`, result);

    // Format processing time
    const processingTime = result.processing_time || totalDuration;

    return res.json({
      success: true,
      message: 'Historical FBA reports generated successfully',
      monthsProcessed: result.months_processed || 0,
      monthsLocked: result.months_locked || 0,
      totalRecordsProcessed: result.total_records_processed || 0,
      processingTime: processingTime,
      details: result.details || 'No detailed information available'
    });
  } catch (err) {
    console.error(`[adminServer] Exception generating historical FBA reports: ${err.message}`);
    res.status(500).json({
      success: false,
      error: err.message,
      details: err.stack
    });
  }
});

// Add API endpoint to get FBA reports for lock management
app.get('/api/get-fba-reports', async (req, res) => {
  console.log('[adminServer] Getting FBA reports for lock management');

  try {
    // Get all FBA reports ordered by month
    const { data, error } = await supabase
      .from('rpt_amaz_monthly_fba_inventory_value')
      .select('*')
      .order('month', { ascending: false });

    if (error) {
      throw new Error(`Failed to get FBA reports: ${error.message}`);
    }

    console.log(`[adminServer] Retrieved ${data?.length || 0} FBA reports`);

    return res.json({
      success: true,
      reports: data || []
    });
  } catch (err) {
    console.error(`[adminServer] Exception getting FBA reports: ${err.message}`);
    res.status(500).json({
      success: false,
      error: err.message,
      details: err.stack
    });
  }
});

// Add API endpoint to lock an FBA report
app.post('/api/lock-fba-report', async (req, res) => {
  console.log('[adminServer] Locking FBA report');

  try {
    const { month } = req.body;

    if (!month) {
      return res.status(400).json({
        success: false,
        error: 'Month parameter is required'
      });
    }

    // Update the report to set locked_at timestamp
    const { data, error } = await supabase
      .from('rpt_amaz_monthly_fba_inventory_value')
      .update({
        locked_at: new Date().toISOString()
      })
      .eq('month', month)
      .select();

    if (error) {
      throw new Error(`Failed to lock FBA report: ${error.message}`);
    }

    if (!data || data.length === 0) {
      return res.status(404).json({
        success: false,
        error: 'Report not found for the specified month'
      });
    }

    console.log(`[adminServer] Successfully locked FBA report for month: ${month}`);

    return res.json({
      success: true,
      message: `Successfully locked report for ${month}`,
      report: data[0]
    });
  } catch (err) {
    console.error(`[adminServer] Exception locking FBA report: ${err.message}`);
    res.status(500).json({
      success: false,
      error: err.message,
      details: err.stack
    });
  }
});

// Add API endpoint to unlock an FBA report
app.post('/api/unlock-fba-report', async (req, res) => {
  console.log('[adminServer] Unlocking FBA report');

  try {
    const { month } = req.body;

    if (!month) {
      return res.status(400).json({
        success: false,
        error: 'Month parameter is required'
      });
    }

    // Update the report to clear locked_at timestamp
    const { data, error } = await supabase
      .from('rpt_amaz_monthly_fba_inventory_value')
      .update({
        locked_at: null
      })
      .eq('month', month)
      .select();

    if (error) {
      throw new Error(`Failed to unlock FBA report: ${error.message}`);
    }

    if (!data || data.length === 0) {
      return res.status(404).json({
        success: false,
        error: 'Report not found for the specified month'
      });
    }

    console.log(`[adminServer] Successfully unlocked FBA report for month: ${month}`);

    return res.json({
      success: true,
      message: `Successfully unlocked report for ${month}`,
      report: data[0]
    });
  } catch (err) {
    console.error(`[adminServer] Exception unlocking FBA report: ${err.message}`);
    res.status(500).json({
      success: false,
      error: err.message,
      details: err.stack
    });
  }
});

// Add API endpoint to test Amazon SP-API connection
app.post('/api/test-amazon-connection', async (req, res) => {
  console.log('[adminServer] Testing Amazon SP-API connection');

  try {
    // Import the Amazon client
    const { default: AmazonSpApiClient } = await import('./amazonSpApiClient.js');

    // Create client and test connection
    const amazonClient = new AmazonSpApiClient();
    const result = await amazonClient.testConnection();

    console.log(`[adminServer] Amazon connection test result:`, result);

    return res.json(result);
  } catch (err) {
    console.error(`[adminServer] Exception testing Amazon connection: ${err.message}`);
    res.status(500).json({
      success: false,
      error: err.message,
      details: err.stack
    });
  }
});

// Add API endpoint to enqueue Amazon listing deletion task
app.post('/api/delete-amazon-listing', async (req, res) => {
  console.log('[adminServer] Enqueueing Amazon listing deletion task');

  try {
    const { sku, marketplaceIds, reason } = req.body;

    if (!sku) {
      return res.status(400).json({
        success: false,
        error: 'SKU parameter is required'
      });
    }

    // Import the enqueue function
    const { enqueueDeleteAmazonListingTask } = await import('./processDeleteAmazonListingTask.js');

    // Enqueue the task
    const task = await enqueueDeleteAmazonListingTask(
      supabase,
      sku,
      marketplaceIds,
      reason
    );

    console.log(`[adminServer] Successfully enqueued Amazon listing deletion task ${task.id} for SKU: ${sku}`);

    return res.json({
      success: true,
      message: `Successfully enqueued Amazon listing deletion task for SKU: ${sku}`,
      taskId: task.id,
      sku: sku,
      marketplaceIds: marketplaceIds || ['ATVPDKIKX0DER'],
      reason: reason || 'Manual deletion via admin interface'
    });
  } catch (err) {
    console.error(`[adminServer] Exception enqueueing Amazon listing deletion: ${err.message}`);
    res.status(500).json({
      success: false,
      error: err.message,
      details: err.stack
    });
  }
});

// Add API endpoint to import Amazon Fulfilled Inventory Report
app.post('/api/import-fulfilled-inventory', async (req, res) => {
  console.log('[adminServer] Importing Amazon Fulfilled Inventory Report');

  try {
    // Import the function
    const { importAmazonFulfilledInventory } = await import('./importAmazonFulfilledInventory.js');

    // Call the import function with the Supabase client
    const result = await importAmazonFulfilledInventory(supabase);

    console.log(`[adminServer] Fulfilled inventory import completed: ${result.importCount} records imported`);

    return res.json({
      success: true,
      message: result.message,
      details: result.details,
      importCount: result.importCount,
      skippedCount: result.skippedCount
    });
  } catch (err) {
    console.error(`[adminServer] Exception importing fulfilled inventory: ${err.message}`);
    res.status(500).json({
      success: false,
      error: err.message,
      details: err.stack
    });
  }
});

// Innova API endpoints
app.post('/api/import-innova-data', async (req, res) => {
  console.log('[adminServer] Importing Innova data');

  try {
    // Import the function
    const { importInnovaOrderData } = await import('./importInnovaOrder.js');

    // Call the import function
    const result = await importInnovaOrderData();

    if (result.success) {
      console.log(`[adminServer] Innova import completed: ${result.totalRecords} records imported`);

      return res.json({
        success: true,
        message: 'Innova data imported successfully',
        totalRecords: result.totalRecords,
        batchId: result.batchId
      });
    } else {
      console.error(`[adminServer] Innova import failed: ${result.error}`);
      return res.status(500).json({
        success: false,
        error: result.error
      });
    }
  } catch (err) {
    console.error(`[adminServer] Exception importing Innova data: ${err.message}`);
    res.status(500).json({
      success: false,
      error: err.message
    });
  }
});

app.get('/api/view-innova-data', async (req, res) => {
  console.log('[adminServer] Viewing Innova data');

  try {
    if (!supabase) {
      return res.status(500).json({ error: 'Supabase client not initialized' });
    }

    // Get total count
    const { count, error: countError } = await supabase
      .from('it_innova_order_sheet_lines')
      .select('*', { count: 'exact', head: true });

    if (countError) {
      console.error(`[adminServer] Error counting Innova records: ${countError.message}`);
      return res.status(500).json({
        success: false,
        error: countError.message
      });
    }

    // Get sample data (first 10 records)
    const { data: sampleData, error: dataError } = await supabase
      .from('it_innova_order_sheet_lines')
      .select('*')
      .order('id')
      .limit(10);

    if (dataError) {
      console.error(`[adminServer] Error getting Innova sample data: ${dataError.message}`);
      return res.status(500).json({
        success: false,
        error: dataError.message
      });
    }

    console.log(`[adminServer] Retrieved ${sampleData.length} sample records from ${count} total records`);

    return res.json({
      success: true,
      totalCount: count,
      sampleData: sampleData
    });
  } catch (err) {
    console.error(`[adminServer] Exception viewing Innova data: ${err.message}`);
    res.status(500).json({
      success: false,
      error: err.message
    });
  }
});

// Innova validation endpoints
app.post('/api/validate-innova-matches', async (req, res) => {
  console.log('[adminServer] Validating Innova matches');

  try {
    // Import the validation function
    const { runValidation } = await import('./validateInnovaMatches.js');

    // Get auto-clear preference from request body (default: false for safety)
    const autoClearBrokenLinks = req.body?.autoClearBrokenLinks === true;
    console.log(`[adminServer] Auto-clear broken links: ${autoClearBrokenLinks}`);

    // Run the validation
    const result = await runValidation(autoClearBrokenLinks);

    if (result.success) {
      console.log(`[adminServer] Validation completed: ${result.totalMatches} matches processed`);

      return res.json({
        success: true,
        message: 'Validation completed successfully',
        totalMatches: result.totalMatches,
        validMatches: result.validMatches,
        questionableMatches: result.questionableMatches,
        brokenLinks: result.brokenLinks,
        autoCleared: result.autoCleared
      });
    } else {
      console.error(`[adminServer] Validation failed: ${result.error}`);
      return res.status(500).json({
        success: false,
        error: result.error
      });
    }
  } catch (err) {
    console.error(`[adminServer] Exception validating Innova matches: ${err.message}`);
    res.status(500).json({
      success: false,
      error: err.message
    });
  }
});

app.get('/api/view-validation-report', async (req, res) => {
  console.log('[adminServer] Viewing validation report');

  try {
    // Import the function to get validation results
    const { getValidationResults } = await import('./validateInnovaMatches.js');

    // Get the stored validation results
    const validationResults = getValidationResults();

    if (!validationResults) {
      return res.json({
        success: true,
        message: 'No validation results found. Run validation first.',
        reportData: []
      });
    }

    console.log(`[adminServer] Retrieved ${validationResults.length} validation records`);

    return res.json({
      success: true,
      reportData: validationResults.slice(0, 50) // Limit to first 50 for display
    });
  } catch (err) {
    console.error(`[adminServer] Exception viewing validation report: ${err.message}`);
    res.status(500).json({
      success: false,
      error: err.message
    });
  }
});

// Enhanced Innova matching endpoint
app.post('/api/enhanced-innova-matching', async (req, res) => {
  console.log('[adminServer] Running enhanced Innova matching');

  try {
    // Import the enhanced matching function
    const { runEnhancedMatching } = await import('./enhancedInnovaMatching.js');

    // Run the enhanced matching
    const result = await runEnhancedMatching();

    if (result.success) {
      console.log(`[adminServer] Enhanced matching completed: ${result.totalProcessed} OSLs processed`);
      console.log(`[adminServer] Perfect: ${result.perfectMatches}, Good: ${result.goodMatches}, Partial: ${result.partialMatches}, Poor: ${result.poorMatches}`);

      return res.json({
        success: true,
        message: 'Enhanced matching completed successfully',
        totalProcessed: result.totalProcessed,
        perfectMatches: result.perfectMatches,
        goodMatches: result.goodMatches,
        partialMatches: result.partialMatches,
        poorMatches: result.poorMatches,
        enhancedResults: result.enhancedResults
      });
    } else {
      console.error(`[adminServer] Enhanced matching failed: ${result.error}`);
      return res.status(500).json({
        success: false,
        error: result.error
      });
    }
  } catch (err) {
    console.error(`[adminServer] Exception in enhanced matching: ${err.message}`);
    res.status(500).json({
      success: false,
      error: err.message
    });
  }
});

// Match verification endpoints
app.post('/api/update-match-verification', async (req, res) => {
  console.log('[adminServer] Updating match verification');

  try {
    const { oslId, confirmed } = req.body;

    if (!oslId || typeof confirmed !== 'boolean') {
      return res.status(400).json({
        success: false,
        error: 'Missing required parameters: oslId and confirmed'
      });
    }

    if (confirmed) {
      // Confirm the match - set verification fields
      const { error } = await supabase
        .from('t_order_sheet_lines')
        .update({
          vendor_match_verified: true,
          vendor_match_verified_at: new Date().toISOString(),
          vendor_match_verified_by: 'admin_interface'
        })
        .eq('id', oslId)
        .eq('vendor_id', 2); // Safety: Only update Innova records

      if (error) {
        console.error(`[adminServer] Error confirming match for OSL ${oslId}:`, error);
        return res.status(500).json({
          success: false,
          error: error.message
        });
      }

      console.log(`[adminServer] Confirmed match for OSL ${oslId}`);
    } else {
      // Reject the match - clear vendor_internal_id and set verification
      const { error } = await supabase
        .from('t_order_sheet_lines')
        .update({
          vendor_internal_id: null,
          vendor_match_verified: false,
          vendor_match_verified_at: new Date().toISOString(),
          vendor_match_verified_by: 'admin_interface'
        })
        .eq('id', oslId)
        .eq('vendor_id', 2); // Safety: Only update Innova records

      if (error) {
        console.error(`[adminServer] Error rejecting match for OSL ${oslId}:`, error);
        return res.status(500).json({
          success: false,
          error: error.message
        });
      }

      console.log(`[adminServer] Rejected match for OSL ${oslId}`);
    }

    return res.json({
      success: true,
      message: confirmed ? 'Match confirmed' : 'Match rejected'
    });
  } catch (err) {
    console.error(`[adminServer] Exception updating match verification: ${err.message}`);
    res.status(500).json({
      success: false,
      error: err.message
    });
  }
});

app.post('/api/bulk-update-match-verification', async (req, res) => {
  console.log('[adminServer] Bulk updating match verification');

  try {
    const { oslIds, confirmed } = req.body;

    if (!Array.isArray(oslIds) || oslIds.length === 0 || typeof confirmed !== 'boolean') {
      return res.status(400).json({
        success: false,
        error: 'Missing required parameters: oslIds (array) and confirmed (boolean)'
      });
    }

    console.log(`[adminServer] Processing ${oslIds.length} OSLs, confirmed: ${confirmed}`);

    let updateData;
    if (confirmed) {
      // Confirm matches
      updateData = {
        vendor_match_verified: true,
        vendor_match_verified_at: new Date().toISOString(),
        vendor_match_verified_by: 'admin_interface'
      };
    } else {
      // Reject matches
      updateData = {
        vendor_internal_id: null,
        vendor_match_verified: false,
        vendor_match_verified_at: new Date().toISOString(),
        vendor_match_verified_by: 'admin_interface'
      };
    }

    const { error } = await supabase
      .from('t_order_sheet_lines')
      .update(updateData)
      .in('id', oslIds)
      .eq('vendor_id', 2); // Safety: Only update Innova records

    if (error) {
      console.error(`[adminServer] Error bulk updating matches:`, error);
      return res.status(500).json({
        success: false,
        error: error.message
      });
    }

    console.log(`[adminServer] Successfully bulk updated ${oslIds.length} matches`);

    return res.json({
      success: true,
      message: `Bulk ${confirmed ? 'confirmed' : 'rejected'} ${oslIds.length} matches`,
      updated: oslIds.length
    });
  } catch (err) {
    console.error(`[adminServer] Exception bulk updating match verification: ${err.message}`);
    res.status(500).json({
      success: false,
      error: err.message
    });
  }
});

// Enhanced validation report endpoint
app.get('/api/enhanced-validation-report', async (req, res) => {
  console.log('[adminServer] Viewing enhanced validation report');

  try {
    // Import the function to get enhanced validation results
    const { getEnhancedValidationResults } = await import('./enhancedInnovaMatching.js');

    // Get the stored enhanced validation results
    const validationResults = getEnhancedValidationResults();

    if (!validationResults) {
      return res.json({
        success: false,
        error: 'No enhanced validation results found. Please run Enhanced Matching first.'
      });
    }

    console.log(`[adminServer] Returning ${validationResults.length} enhanced validation results`);

    return res.json({
      success: true,
      reportData: validationResults // Return all results for enhanced view
    });
  } catch (err) {
    console.error(`[adminServer] Exception viewing enhanced validation report: ${err.message}`);
    res.status(500).json({
      success: false,
      error: err.message
    });
  }
});

// Phase 2 Innova matching endpoint
app.post('/api/phase2-innova-matching', async (req, res) => {
  console.log('[adminServer] Running Phase 2 Innova matching');

  try {
    const { skipCount = 0, batchSize = 300, maxCandidates = 2, minConfidence = 60 } = req.body;
    console.log(`[adminServer] Phase 2 matching with skip: ${skipCount}, batch: ${batchSize}, max: ${maxCandidates}, min: ${minConfidence}%`);

    // Import and run the Phase 2 matching function
    const { runPhase2Matching } = await import('./phase2InnovaMatching.js');

    const result = await runPhase2Matching(skipCount, batchSize, maxCandidates, minConfidence);

    if (result.success) {
      res.json({
        success: true,
        message: `Phase 2 matching completed successfully`,
        stats: result.stats,
        processed: result.processed,
        matches: result.matches
      });
    } else {
      res.status(500).json({
        success: false,
        error: result.error
      });
    }
  } catch (err) {
    console.error(`[adminServer] Exception in Phase 2 Innova matching: ${err.message}`);
    res.status(500).json({
      success: false,
      error: err.message
    });
  }
});

// Phase 2 validation report endpoint
app.get('/api/phase2-validation-report', async (req, res) => {
  console.log('[adminServer] Viewing Phase 2 validation report');

  try {
    // Import the function to get Phase 2 results
    const { getPhase2Results } = await import('./phase2InnovaMatching.js');

    // Get the stored Phase 2 results
    const validationResults = getPhase2Results();

    if (!validationResults) {
      return res.json({
        success: false,
        error: 'No Phase 2 results found. Please run Phase 2 Matching first.'
      });
    }

    console.log(`[adminServer] Returning ${validationResults.length} Phase 2 validation results`);

    return res.json({
      success: true,
      reportData: validationResults // Return all results for Phase 2 view
    });
  } catch (err) {
    console.error(`[adminServer] Exception viewing Phase 2 validation report: ${err.message}`);
    res.status(500).json({
      success: false,
      error: err.message
    });
  }
});

// Create vendor connection endpoint (for Phase 2)
app.post('/api/create-vendor-connection', async (req, res) => {
  console.log('[adminServer] Creating vendor connection');

  try {
    const { oslId, vendorInternalId } = req.body;

    if (!oslId || !vendorInternalId) {
      return res.status(400).json({
        success: false,
        error: 'Missing required parameters: oslId and vendorInternalId'
      });
    }

    // Update the OSL record to set vendor_internal_id
    const { data, error } = await supabase
      .from('t_order_sheet_lines')
      .update({
        vendor_internal_id: vendorInternalId,
        vendor_match_verified: true,
        vendor_match_verified_at: new Date().toISOString()
      })
      .eq('id', oslId)
      .eq('vendor_id', 2) // Only Innova records
      .select();

    if (error) {
      throw new Error(`Database error: ${error.message}`);
    }

    if (!data || data.length === 0) {
      return res.status(404).json({
        success: false,
        error: 'OSL record not found or not an Innova record'
      });
    }

    console.log(`[adminServer] Created connection: OSL ${oslId} → Innova ${vendorInternalId}`);

    res.json({
      success: true,
      message: `Connection created successfully`,
      oslId: oslId,
      vendorInternalId: vendorInternalId
    });
  } catch (err) {
    console.error(`[adminServer] Exception creating vendor connection: ${err.message}`);
    res.status(500).json({
      success: false,
      error: err.message
    });
  }
});

// Reject Phase 2 match endpoint
app.post('/api/reject-phase2-match', async (req, res) => {
  console.log('[adminServer] Rejecting Phase 2 match');

  try {
    const { oslId, innovaInternalId } = req.body;

    if (!oslId || !innovaInternalId) {
      return res.status(400).json({
        success: false,
        error: 'Missing required parameters: oslId and innovaInternalId'
      });
    }

    // Note: The t_vendor_match_rejections table should be created manually using:
    // create_vendor_match_rejections_table.sql
    // This ensures proper permissions and structure

    // Insert the rejection record
    const { data, error } = await supabase
      .from('t_vendor_match_rejections')
      .upsert({
        osl_id: oslId,
        vendor_id: 2, // Innova
        vendor_internal_id: innovaInternalId,
        rejected_at: new Date().toISOString(),
        rejected_by: 'admin_interface'
      })
      .select();

    if (error) {
      throw new Error(`Database error: ${error.message}`);
    }

    console.log(`[adminServer] Rejected match: OSL ${oslId} ↔ Innova ${innovaInternalId}`);

    res.json({
      success: true,
      message: `Match rejection recorded`,
      oslId: oslId,
      innovaInternalId: innovaInternalId
    });
  } catch (err) {
    console.error(`[adminServer] Exception rejecting Phase 2 match: ${err.message}`);
    res.status(500).json({
      success: false,
      error: err.message
    });
  }
});

// Phase 2 bulk connect endpoint (creates actual vendor connections)
app.post('/api/phase2-bulk-connect', async (req, res) => {
  console.log('[adminServer] Phase 2 bulk connecting matches');

  try {
    const { connections } = req.body;

    if (!Array.isArray(connections) || connections.length === 0) {
      return res.status(400).json({
        success: false,
        error: 'Missing required parameter: connections (array of {oslId, vendorInternalId})'
      });
    }

    console.log(`[adminServer] Processing ${connections.length} Phase 2 connections`);

    let successCount = 0;
    let errors = [];

    // Process each connection
    for (const connection of connections) {
      const { oslId, vendorInternalId } = connection;

      if (!oslId || !vendorInternalId) {
        errors.push(`Invalid connection: oslId=${oslId}, vendorInternalId=${vendorInternalId}`);
        continue;
      }

      try {
        const { error } = await supabase
          .from('t_order_sheet_lines')
          .update({
            vendor_internal_id: vendorInternalId,
            vendor_match_verified: true,
            vendor_match_verified_at: new Date().toISOString(),
            vendor_match_verified_by: 'phase2_bulk_connect'
          })
          .eq('id', oslId)
          .eq('vendor_id', 2); // Only Innova records

        if (error) {
          errors.push(`OSL ${oslId}: ${error.message}`);
        } else {
          successCount++;
        }
      } catch (err) {
        errors.push(`OSL ${oslId}: ${err.message}`);
      }
    }

    console.log(`[adminServer] Phase 2 bulk connect completed: ${successCount} success, ${errors.length} errors`);

    return res.json({
      success: true,
      message: `Bulk connected ${successCount} matches`,
      connected: successCount,
      errors: errors.length > 0 ? errors : undefined
    });
  } catch (err) {
    console.error(`[adminServer] Exception in Phase 2 bulk connect: ${err.message}`);
    res.status(500).json({
      success: false,
      error: err.message
    });
  }
});

// Update OSL weights to match Innova and create connection
app.post('/api/update-weights-and-connect', async (req, res) => {
  console.log('[adminServer] Updating OSL weights and creating connection');

  try {
    const { oslId, innovaInternalId, newMinWeight, newMaxWeight, originalWeights, innovaWeights } = req.body;

    if (!oslId || !innovaInternalId || !newMinWeight || !newMaxWeight) {
      return res.status(400).json({
        success: false,
        error: 'Missing required parameters: oslId, innovaInternalId, newMinWeight, newMaxWeight'
      });
    }

    console.log(`[adminServer] OSL ${oslId}: Updating weights from ${originalWeights} to ${newMinWeight}-${newMaxWeight} and connecting to Innova ${innovaInternalId}`);

    // Update the OSL record with new weights and vendor connection
    const { data, error } = await supabase
      .from('t_order_sheet_lines')
      .update({
        min_weight: newMinWeight,
        max_weight: newMaxWeight,
        vendor_internal_id: innovaInternalId,
        vendor_match_verified: true,
        vendor_match_verified_at: new Date().toISOString(),
        vendor_match_verified_by: 'update_weights_and_connect',
        updated_at: new Date().toISOString()
      })
      .eq('id', oslId)
      .eq('vendor_id', 2) // Only Innova records
      .select();

    if (error) {
      throw new Error(`Database error: ${error.message}`);
    }

    if (!data || data.length === 0) {
      return res.status(404).json({
        success: false,
        error: 'OSL record not found'
      });
    }

    console.log(`[adminServer] Successfully updated OSL ${oslId} weights and created connection`);

    res.json({
      success: true,
      message: `OSL ${oslId} weights updated to ${newMinWeight}-${newMaxWeight} and connected to Innova ${innovaInternalId}`,
      oslId: oslId,
      innovaInternalId: innovaInternalId,
      oldWeights: originalWeights,
      newWeights: `${newMinWeight}-${newMaxWeight}`
    });
  } catch (err) {
    console.error(`[adminServer] Exception updating weights and connecting: ${err.message}`);
    res.status(500).json({
      success: false,
      error: err.message
    });
  }
});

// Clear OSL from Phase 2 matching (mark as verified without vendor connection)
app.post('/api/clear-from-phase2', async (req, res) => {
  console.log('[adminServer] Clearing OSL from Phase 2 matching');

  try {
    const { oslId } = req.body;

    if (!oslId) {
      return res.status(400).json({
        success: false,
        error: 'Missing required parameter: oslId'
      });
    }

    console.log(`[adminServer] Clearing OSL ${oslId} from Phase 2 (marking as verified without connection)`);

    // Mark the OSL as verified but leave vendor_internal_id as null
    // This removes it from Phase 2 matching without creating a vendor connection
    const { data, error } = await supabase
      .from('t_order_sheet_lines')
      .update({
        vendor_match_verified: true,
        vendor_match_verified_at: new Date().toISOString(),
        vendor_match_verified_by: 'clear_from_phase2',
        updated_at: new Date().toISOString()
      })
      .eq('id', oslId)
      .eq('vendor_id', 2) // Only Innova records
      .select();

    if (error) {
      throw new Error(`Database error: ${error.message}`);
    }

    if (!data || data.length === 0) {
      return res.status(404).json({
        success: false,
        error: 'OSL record not found'
      });
    }

    console.log(`[adminServer] Successfully cleared OSL ${oslId} from Phase 2 matching`);

    res.json({
      success: true,
      message: `OSL ${oslId} cleared from Phase 2 matching (marked as verified without vendor connection)`,
      oslId: oslId
    });
  } catch (err) {
    console.error(`[adminServer] Exception clearing OSL from Phase 2: ${err.message}`);
    res.status(500).json({
      success: false,
      error: err.message
    });
  }
});

// Mark MPS as inactive endpoint
app.post('/api/mark-mps-inactive', async (req, res) => {
  console.log('[adminServer] Marking MPS as inactive');

  try {
    const { mpsId } = req.body;

    if (!mpsId) {
      return res.status(400).json({
        success: false,
        error: 'Missing required parameter: mpsId'
      });
    }

    // Update the MPS record to set active = false
    const { data, error } = await supabase
      .from('t_mps')
      .update({
        active: false,
        updated_at: new Date().toISOString()
      })
      .eq('id', mpsId)
      .select();

    if (error) {
      throw new Error(`Database error: ${error.message}`);
    }

    if (!data || data.length === 0) {
      return res.status(404).json({
        success: false,
        error: 'MPS record not found'
      });
    }

    console.log(`[adminServer] Marked MPS ${mpsId} as inactive`);

    res.json({
      success: true,
      message: `MPS ${mpsId} marked as inactive`,
      mpsId: mpsId
    });
  } catch (err) {
    console.error(`[adminServer] Exception marking MPS as inactive: ${err.message}`);
    res.status(500).json({
      success: false,
      error: err.message
    });
  }
});

// Start server
const PORT = process.env.PORT || 3001; // Always use port 3001 by default
startServer(PORT);
