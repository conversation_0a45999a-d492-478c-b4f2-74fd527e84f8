<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Task Queue Admin</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        header {
            background-color: #2c3e50;
            color: white;
            padding: 20px;
            border-radius: 5px;
            margin-bottom: 20px;
            text-align: center;
        }
        h1, h2, h3 {
            color: #2c3e50;
        }
        header h1 {
            color: white;
            margin: 0;
        }
        .card {
            background-color: white;
            border-radius: 5px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
            padding: 20px;
            margin-bottom: 20px;
        }
        .card-header {
            border-bottom: 1px solid #eee;
            padding-bottom: 10px;
            margin-bottom: 15px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .card-header h2 {
            margin: 0;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input, select {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        button {
            background-color: #3498db;
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
        }
        button:hover {
            background-color: #2980b9;
        }
        .start-worker-btn {
            background-color: #2ecc71;
            margin-left: 10px;
        }
        .start-worker-btn:hover {
            background-color: #27ae60;
        }
        .run-once-btn {
            background-color: #f39c12;
            margin-left: 10px;
        }
        .run-once-btn:hover {
            background-color: #d35400;
        }
        .run-daemon-btn {
            background-color: #2ecc71;
            margin-left: 10px;
        }
        .run-daemon-btn:hover {
            background-color: #27ae60;
        }
        .stop-worker-btn {
            background-color: #e74c3c;
            margin-left: 10px;
        }
        .stop-worker-btn:hover {
            background-color: #c0392b;
        }
        .output {
            background-color: #f8f9fa;
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 15px;
            margin-top: 15px;
            max-height: 300px;
            overflow-y: auto;
            font-family: monospace;
            white-space: pre-wrap;
        }
        .tooltip {
            position: relative;
            display: inline-block;
            cursor: help;
            margin-left: 5px;
        }
        .tooltip .tooltiptext {
            visibility: hidden;
            width: 300px;
            background-color: #555;
            color: #fff;
            text-align: left;
            border-radius: 6px;
            padding: 10px;
            position: absolute;
            z-index: 1;
            top: 125%;
            left: 50%;
            margin-left: -150px;
            opacity: 0;
            transition: opacity 0.3s;
            font-size: 14px;
            line-height: 1.4;
        }
        .tooltip:hover .tooltiptext {
            visibility: visible;
            opacity: 1;
        }
        .badge {
            display: inline-block;
            padding: 3px 7px;
            font-size: 12px;
            font-weight: bold;
            line-height: 1;
            color: #fff;
            text-align: center;
            white-space: nowrap;
            vertical-align: baseline;
            border-radius: 10px;
            background-color: #3498db;
        }
        .readme {
            background-color: white;
            border-radius: 5px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
            padding: 20px;
            margin-top: 30px;
        }
        .readme pre {
            background-color: #f8f9fa;
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 15px;
            overflow-x: auto;
        }
        .readme code {
            font-family: monospace;
        }
        .workflow-diagram {
            padding: 20px;
        }
        .workflow-steps {
            counter-reset: step;
            padding-left: 0;
        }
        .workflow-steps li {
            list-style-type: none;
            position: relative;
            margin-bottom: 30px;
            padding: 15px 20px 15px 70px;
            background-color: #f8f9fa;
            border-radius: 5px;
            border-left: 4px solid #3498db;
        }
        .workflow-steps li:before {
            counter-increment: step;
            content: counter(step);
            position: absolute;
            left: 20px;
            top: 15px;
            background-color: #3498db;
            color: white;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            text-align: center;
            line-height: 30px;
            font-weight: bold;
        }
        .workflow-steps li strong {
            display: block;
            margin-bottom: 10px;
            color: #2c3e50;
            font-size: 18px;
        }
        .workflow-steps li p {
            margin-top: 0;
        }
        .workflow-steps li code {
            background-color: #e9ecef;
            padding: 2px 5px;
            border-radius: 3px;
            font-family: monospace;
        }

        .task-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 15px;
        }
        .task-table th {
            background-color: #2c3e50;
            color: white;
            text-align: left;
            padding: 10px;
        }
        .task-table td {
            padding: 10px;
            border-bottom: 1px solid #ddd;
        }
        .task-table tr:nth-child(even) {
            background-color: #f8f9fa;
        }
        .task-table .task-name {
            font-weight: bold;
            width: 60%;
        }
        .task-table .count {
            text-align: center;
            font-weight: bold;
        }
        .task-table .count-zero {
            color: #95a5a6;
        }
        .task-table .count-nonzero {
            color: #2980b9;
        }
        .task-table .count-success {
            color: #27ae60;
        }
        .task-table .count-error {
            color: #c0392b;
        }
        .task-table .loading-message {
            text-align: center;
            padding: 20px;
            color: #7f8c8d;
            font-style: italic;
        }
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 5px;
        }
        .status-running {
            background-color: #2ecc71;
        }
        .status-stopped {
            background-color: #e74c3c;
        }
        #workerStatus, #workerTabStatus {
            font-weight: bold;
            margin-left: 10px;
        }
        .worker-controls {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }
        .worker-mode-section {
            flex: 1;
        }
        .worker-buttons {
            display: flex;
            gap: 10px;
        }
        .worker-status-section {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
            border-left: 4px solid #3498db;
        }
        .run-worker-btn {
            background-color: #2ecc71;
        }
        .run-worker-btn:hover {
            background-color: #27ae60;
        }
        .stop-worker-btn {
            background-color: #e74c3c;
        }
        .stop-worker-btn:hover {
            background-color: #c0392b;
        }
        .success-message {
            color: #2ecc71;
            font-weight: bold;
        }
        .failure-message {
            color: #e74c3c;
            font-weight: bold;
        }
        .console-log {
            max-height: 400px;
            overflow-y: auto;
            background-color: #1e1e1e;
            color: #f0f0f0;
            font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
            padding: 10px;
            border-radius: 5px;
            margin-top: 10px;
            white-space: pre-wrap;
            word-wrap: break-word;
            line-height: 1.4;
            font-size: 14px;
        }
        .console-log .log-entry {
            margin-bottom: 4px;
            border-bottom: 1px solid #333;
            padding-bottom: 4px;
        }
        .console-log .timestamp {
            color: #888;
            margin-right: 8px;
        }
        .console-log .task-id {
            color: #569cd6;
            font-weight: bold;
            margin-right: 8px;
        }
        .console-log .task-type {
            color: #4ec9b0;
            font-weight: bold;
            margin-right: 8px;
        }
        .console-log .success {
            color: #6a9955;
        }
        .console-log .error {
            color: #f14c4c;
        }
        .console-log .warning {
            color: #dcdcaa;
        }
        .console-log .info {
            color: #9cdcfe;
        }
        .console-log .processing {
            color: #ce9178;
        }
        .console-log .completed {
            color: #6a9955;
            font-weight: bold;
        }
        .console-controls {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin: 10px 0;
        }
        .clear-console-btn {
            background-color: #7f8c8d;
            color: white;
            border: none;
            padding: 5px 10px;
            border-radius: 3px;
            cursor: pointer;
        }
        .clear-console-btn:hover {
            background-color: #95a5a6;
        }
        .auto-scroll-label {
            display: flex;
            align-items: center;
            gap: 5px;
            cursor: pointer;
        }
        .tabs {
            display: flex;
            margin-bottom: 20px;
        }
        .tab {
            padding: 10px 20px;
            background-color: #ddd;
            border-radius: 5px 5px 0 0;
            margin-right: 5px;
            cursor: pointer;
        }
        .tab.active {
            background-color: white;
            border-bottom: 2px solid #3498db;
        }
        .tab-content {
            display: none;
        }
        .tab-content.active {
            display: block;
        }

        /* Enhanced Informed Tab Styles */
        .workflow-section {
            margin-bottom: 30px;
        }

        .step-group {
            margin: 25px 0;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 8px;
            border-left: 4px solid #3498db;
        }

        .step-group h3 {
            margin-top: 0;
            color: #2c3e50;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .button-row {
            display: flex;
            gap: 15px;
            flex-wrap: wrap;
            margin: 15px 0;
        }

        .button-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 15px 0;
        }

        .button-group {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 5px;
        }

        .step-btn {
            min-width: 180px;
            padding: 10px 16px;
            border-radius: 6px;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .step-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }

        .workflow-btn {
            background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
            color: white;
            border: none;
            padding: 12px 20px;
            border-radius: 6px;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .workflow-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(52, 152, 219, 0.3);
        }

        .info-box {
            margin: 15px 0;
            padding: 15px;
            border-radius: 6px;
            border-left: 4px solid;
        }

        .success-highlight {
            background: #e8f5e8;
            border-left-color: #27ae60;
            color: #27ae60;
        }

        .warning-highlight {
            background: #fff3cd;
            border-left-color: #f39c12;
            color: #856404;
        }

        .enhanced-output {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 6px;
            padding: 15px;
            margin-top: 15px;
            font-family: 'Courier New', monospace;
            font-size: 13px;
            line-height: 1.4;
            max-height: 400px;
            overflow-y: auto;
        }

        .step-output {
            background: #f8f9fa;
            border-left: 3px solid #3498db;
            padding: 10px;
            margin-top: 10px;
            border-radius: 0 4px 4px 0;
        }

        .output-section {
            margin-top: 20px;
        }

        .card-header.gradient-blue {
            background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
            color: white;
        }

        .card-header.gradient-green {
            background: linear-gradient(135deg, #27ae60 0%, #229954 100%);
            color: white;
        }

        .card-header.gradient-orange {
            background: linear-gradient(135deg, #e67e22 0%, #d35400 100%);
            color: white;
        }

        .card-header p {
            margin: 5px 0 0 0;
            opacity: 0.9;
            font-size: 14px;
        }

        .tooltip {
            position: relative;
            display: inline-block;
            cursor: help;
        }

        .tooltip .tooltiptext {
            visibility: hidden;
            width: 300px;
            background-color: #555;
            color: #fff;
            text-align: left;
            border-radius: 6px;
            padding: 10px;
            position: absolute;
            z-index: 1;
            bottom: 125%;
            left: 50%;
            margin-left: -150px;
            opacity: 0;
            transition: opacity 0.3s;
            font-size: 12px;
            line-height: 1.4;
        }

        .tooltip:hover .tooltiptext {
            visibility: visible;
            opacity: 1;
        }
    </style>
</head>
<body>
    <header>
        <h1>Task Queue Admin Dashboard</h1>
        <p>Manage and monitor your task queue system</p>
    </header>

    <div class="tabs">
        <div class="tab active" data-tab="dashboard">Dashboard</div>
        <div class="tab" data-tab="tasks">Tasks</div>
        <div class="tab" data-tab="reconcile">Reconcile</div>
        <div class="tab" data-tab="worker">Worker</div>
        <div class="tab" data-tab="flow">Workflow</div>
        <div class="tab" data-tab="informed">Informed</div>
        <div class="tab" data-tab="discraft">Discraft</div>
        <div class="tab" data-tab="innova">Innova</div>
        <div class="tab" data-tab="documentation">Documentation</div>
        <div class="tab" data-tab="amazon">Amazon FBA</div>
        <div class="tab" data-tab="todo">ToDo</div>
    </div>

    <div id="dashboard" class="tab-content active">
        <div class="card">
            <div class="card-header">
                <h2>System Overview</h2>
            </div>
            <div>
                <p><strong>Worker Status:</strong>
                    <span class="status-indicator status-stopped"></span>
                    <span id="workerStatus">Stopped</span>
                    <button id="refreshStatus">Refresh</button>
                    <button id="dashboardRunWorkerOnce" class="run-once-btn">Run Worker Once</button>
                    <button id="dashboardRunWorkerDaemon" class="run-daemon-btn">Run Worker as Daemon</button>
                </p>
                <p><strong>Pending Tasks:</strong> <span id="pendingTasksCount">Loading...</span></p>
                <p><strong>Future Scheduled Tasks:</strong> <span id="futureTasksCount">Loading...</span></p>
                <p><strong>Last Run:</strong> <span id="lastRunTime">Unknown</span></p>
            </div>

            <div class="card-header">
                <h3>Task Queue Status by Workflow Step</h3>
            </div>
            <div id="taskWorkflowTable">
                <table class="task-table">
                    <thead>
                        <tr>
                            <th>Task Type</th>
                            <th>Pending</th>
                            <th>Future</th>
                            <th>Completed (30m)</th>
                            <th>Errors (30m)</th>
                        </tr>
                    </thead>
                    <tbody id="taskTableBody">
                        <tr>
                            <td colspan="5" class="loading-message">Loading task statistics...</td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <div class="card-header">
                <h3>Live Console Log</h3>
                <div class="tooltip">ⓘ
                    <span class="tooltiptext">
                        Real-time output from the worker process.
                    </span>
                </div>
            </div>
            <div class="console-controls">
                <div>
                    <button id="clearConsoleBtn" class="clear-console-btn">Clear Console</button>
                    <select id="logFilterSelect" title="Filter log entries">
                        <option value="all">All Messages</option>
                        <option value="error">Errors Only</option>
                        <option value="success">Success Only</option>
                        <option value="processing">Processing Only</option>
                    </select>
                    <input type="text" id="taskTypeFilter" placeholder="Filter by task type..." style="width: 180px;">
                </div>
                <div>
                    <label class="auto-scroll-label">
                        <input type="checkbox" id="autoScrollCheckbox" checked>
                        Auto-scroll
                    </label>
                    <label class="auto-scroll-label" style="margin-left: 10px;">
                        <input type="checkbox" id="showTimestampsCheckbox" checked>
                        Show Timestamps
                    </label>
                </div>
            </div>
            <div id="dashboardConsoleLog" class="output console-log"></div>
        </div>
    </div>

    <div id="tasks" class="tab-content">
        <div class="card">
            <div class="card-header">
                <h2>Enqueue Image Verification Task</h2>
                <div class="tooltip">ⓘ
                    <span class="tooltiptext">
                        This tool creates a new task in the t_task_queue table to verify an image.
                        The task will be processed by the worker when its scheduled time is reached.
                    </span>
                </div>
            </div>
            <form id="enqueueForm">
                <div class="form-group">
                    <label for="imageId">Image ID <span class="badge">Required</span></label>
                    <input type="number" id="imageId" name="imageId" required placeholder="Enter the t_images.id value">
                    <div class="tooltip">ⓘ
                        <span class="tooltiptext">
                            The ID of the image record in the t_images table that you want to verify.
                            This will be used in the task payload as {"id": 123456}.
                        </span>
                    </div>
                </div>
                <div class="form-group">
                    <label for="delayMinutes">Delay (minutes)</label>
                    <input type="number" id="delayMinutes" name="delayMinutes" value="0" min="0" placeholder="0 = immediate execution">
                    <div class="tooltip">ⓘ
                        <span class="tooltiptext">
                            Number of minutes to delay task execution. Use 0 for immediate execution.
                            Tasks will only be processed when their scheduled time is reached.
                        </span>
                    </div>
                </div>
                <button type="submit">Enqueue Task</button>
            </form>
            <div id="enqueueOutput" class="output" style="display: none;"></div>
        </div>



        <div class="card">
            <div class="card-header">
                <h2>View Pending Tasks</h2>
                <div class="tooltip">ⓘ
                    <span class="tooltiptext">
                        View and manage tasks currently in the queue.
                    </span>
                </div>
            </div>
            <button id="viewTasksBtn">Refresh Task List</button>
            <div id="tasksOutput" class="output" style="display: none;"></div>
        </div>



        <div class="card">
            <div class="card-header">
                <h2>Import Discs from Google Sheets</h2>
                <div class="tooltip">ⓘ
                    <span class="tooltiptext">
                        Import disc data from a Google Sheets document into the t_discs table with validation and foreign key mapping.
                    </span>
                </div>
            </div>
            <div class="form-group" style="background-color: #f8f9fa; padding: 10px; border-left: 4px solid #3498db; margin-bottom: 15px;">
                <p><strong>Instructions:</strong></p>
                <ol>
                    <li>Make sure your Google Sheet is publicly viewable</li>
                    <li>The sheet should have columns: shipment_id, mps_id, color_modifier, color, weight_mfg, weight_scale, grade, description, location, notes, new_id</li>
                    <li>Column mapping: weight_scale → weight, weight_mfg → weight_mfg</li>
                    <li>The 'new_id' column will be populated with the new t_discs.id values after import</li>
                    <li><strong>Most fields are required - any blank required field will mark the row as invalid</strong></li>
                </ol>
                <p><strong>Required Fields:</strong> shipment_id, mps_id, color, weight_mfg, weight_scale, grade, description, location</p>
                <p><strong>Optional Fields:</strong> color_modifier, notes, new_id</p>
                <p><strong>Validation:</strong> shipment_id must exist in t_shipments, mps_id must exist in t_mps, color must match t_colors.color</p>
            </div>
            <form id="importDiscsForm">
                <div class="form-group">
                    <label for="googleSheetsUrl">Google Sheets URL <span class="badge">Required</span></label>
                    <input type="url" id="googleSheetsUrl" name="googleSheetsUrl" required
                           placeholder="https://docs.google.com/spreadsheets/d/YOUR_SHEET_ID/edit#gid=0"
                           style="width: 100%;">
                    <div class="tooltip">ⓘ
                        <span class="tooltiptext">
                            Paste the full Google Sheets URL. The sheet must be publicly viewable for the import to work.
                        </span>
                    </div>
                </div>
                <div class="form-group">
                    <label for="validateOnly">
                        <input type="checkbox" id="validateOnly" name="validateOnly">
                        Validate Only (don't import)
                    </label>
                    <div class="tooltip">ⓘ
                        <span class="tooltiptext">
                            Check this to only validate the data without importing. Use this to check for errors first.
                        </span>
                    </div>
                </div>
                <button type="submit">Import Discs from Google Sheets</button>
            </form>
            <div id="importDiscsOutput" class="output" style="display: none;"></div>
        </div>

        <div class="card">
            <div class="card-header">
                <h2>Delete Wrong Veeqo Records</h2>
                <div class="tooltip">ⓘ
                    <span class="tooltiptext">
                        Deletes Veeqo product variants with variant titles containing " (D#" through the Veeqo API.
                    </span>
                </div>
            </div>
            <div class="form-group" style="background-color: #f8f9fa; padding: 10px; border-left: 4px solid #e74c3c; margin-bottom: 15px;">
                <p><strong>Warning:</strong> This will permanently delete product variants in Veeqo that have variant titles containing " (D#".</p>
                <p>These are typically duplicate records that were created incorrectly and should be removed.</p>
                <p>This action cannot be undone. Please make sure you have a recent backup of your Veeqo data.</p>
            </div>
            <div class="form-group">
                <label for="deleteVeeqoLimit">Number of records to delete:</label>
                <select id="deleteVeeqoLimit" class="form-control">
                    <option value="1">Delete 1 record (test)</option>
                    <option value="10">Delete 10 records</option>
                    <option value="">Delete all records</option>
                </select>
            </div>
            <button id="deleteWrongVeeqoRecordsBtn" class="run-once-btn">Delete Wrong Veeqo Records</button>
            <div id="deleteWrongVeeqoRecordsOutput" class="output" style="display: none;"></div>
        </div>


    </div>

    <div id="reconcile" class="tab-content">
        <div class="card">
            <div class="card-header">
                <h2>Reconcile OSL Stats Into t_inv_osl</h2>
                <div class="tooltip">ⓘ
                    <span class="tooltiptext">
                        Updates t_inv_osl records with correct quantity counts from the calculated stats table.
                    </span>
                </div>
            </div>
            <div class="form-group" style="background-color: #f8f9fa; padding: 10px; border-left: 4px solid #3498db; margin-bottom: 15px;">
                <p><strong>Description:</strong> This process calls the <code>fn_reconcile_osl_inventory</code> function which updates <code>t_inv_osl</code> records with correct quantity counts from the <code>v_stats_by_osl</code> view.</p>
                <p>The function processes records in batches of 50 and will continue running until all discrepancies are resolved. These updates to <code>t_inv_osl</code> will trigger task queue entries to update Veeqo quantities.</p>
            </div>
            <button id="reconcileOslStatsBtn" class="run-once-btn">Reconcile OSL Stats</button>
            <div id="reconcileOslStatsOutput" class="output" style="display: none;"></div>
        </div>

        <div class="card">
            <div class="card-header">
                <h2>Reconcile SDAsin Stats Into t_inv_sdasin</h2>
                <div class="tooltip">ⓘ
                    <span class="tooltiptext">
                        Updates t_inv_sdasin records with correct quantity counts from the calculated stats table.
                    </span>
                </div>
            </div>
            <div class="form-group" style="background-color: #f8f9fa; padding: 10px; border-left: 4px solid #3498db; margin-bottom: 15px;">
                <p><strong>Description:</strong> This process calls the <code>fn_reconcile_sdasin_inventory</code> function which updates <code>t_inv_sdasin</code> records with correct quantity counts from the <code>v_stats_by_sdasin</code> view.</p>
                <p>The function processes records in batches of 50 and will continue running until all discrepancies are resolved. These updates to <code>t_inv_sdasin</code> will trigger task queue entries to update Veeqo quantities.</p>
            </div>
            <button id="reconcileSdasinStatsBtn" class="run-once-btn">Reconcile SDAsin Stats</button>
            <div id="reconcileSdasinStatsOutput" class="output" style="display: none;"></div>
        </div>

        <div class="card">
            <div class="card-header">
                <h2>Import Veeqo Sellables</h2>
                <div class="tooltip">ⓘ
                    <span class="tooltiptext">
                        Truncates the imported_table_veeqo_sellables_export table and runs the import_veeqo_sellables.js script.
                    </span>
                </div>
            </div>
            <div class="form-group" style="background-color: #f8f9fa; padding: 10px; border-left: 4px solid #e74c3c; margin-bottom: 15px;">
                <p><strong>IMPORTANT STEP:</strong> Before clicking the button below, you must manually download the CSV export from Veeqo and save it to:</p>
                <p><code>C:\Users\<USER>\supabase_project\data\external data\veeqo_sellables_export.csv</code></p>
                <p>To download the export from Veeqo:</p>
                <ol>
                    <li>Log in to Veeqo</li>
                    <li>Go to Products</li>
                    <li>Click "Export" button</li>
                    <li>Download the CSV file</li>
                    <li>Save it to the exact path specified above</li>
                </ol>
                <p>After saving the file to the correct location, click the button below to import the data.</p>
            </div>
            <button id="importVeeqoSellablesBtn" class="run-worker-btn">Import Veeqo Sellables</button>
            <div id="importVeeqoSellablesOutput" class="output" style="display: none;"></div>
        </div>

        <div class="card">
            <div class="card-header">
                <h2>Reconcile Discs to Veeqo</h2>
                <div class="tooltip">ⓘ
                    <span class="tooltiptext">
                        Runs the reconcileDToVeeqo.js script to update discs with inventory discrepancies in Veeqo.
                    </span>
                </div>
            </div>
            <div class="form-group" style="background-color: #f8f9fa; padding: 10px; border-left: 4px solid #f39c12; margin-bottom: 15px;">
                <p><strong>Important:</strong> Before running this script, please browse through the <code>v_reconcile_d_to_veeqo</code> view to review the discrepancies that will be processed.</p>
                <p>This script will update Veeqo quantities to 0 for sold discs that still show inventory in Veeqo.</p>
            </div>
            <button id="reconcileDToVeeqoBtn" class="run-once-btn">Reconcile Discs to Veeqo</button>
            <div id="reconcileDToVeeqoOutput" class="output" style="display: none;"></div>
        </div>

        <div class="card">
            <div class="card-header">
                <h2>Reconcile RPRO to Veeqo</h2>
                <div class="tooltip">ⓘ
                    <span class="tooltiptext">
                        Refreshes the reconcile_rpro_counts_to_veeqo table to compare RPRO and Veeqo quantities.
                    </span>
                </div>
            </div>
            <div class="form-group" style="background-color: #f8f9fa; padding: 10px; border-left: 4px solid #3498db; margin-bottom: 15px;">
                <p><strong>Description:</strong> This process refreshes the <code>reconcile_rpro_counts_to_veeqo</code> table that compares quantities between RPRO (ivqtylaw) and Veeqo (total_qty).</p>
                <p>After running this, you can browse the <code>v_reconcile_rpro_counts_to_veeqo</code> view to see the discrepancies.</p>
            </div>
            <button id="reconcileRproToVeeqoBtn" class="run-once-btn">Refresh RPRO to Veeqo Reconciliation</button>
            <div id="reconcileRproToVeeqoOutput" class="output" style="display: none;"></div>
        </div>

        <div class="card">
            <div class="card-header">
                <h2>Update Veeqo Quantities from RPRO</h2>
                <div class="tooltip">ⓘ
                    <span class="tooltiptext">
                        Updates Veeqo product quantities to match RPRO quantities for records with discrepancies.
                    </span>
                </div>
            </div>
            <div class="form-group" style="background-color: #f8f9fa; padding: 10px; border-left: 4px solid #e74c3c; margin-bottom: 15px;">
                <p><strong>Important:</strong> This process will update Veeqo product quantities based on RPRO quantities for all records with discrepancies in the <code>v_reconcile_rpro_counts_to_veeqo</code> view.</p>
                <p>The following rules will be applied:</p>
                <ul>
                    <li>If RPRO quantity is negative, Veeqo quantity will be set to 0</li>
                    <li>If RPRO quantity has decimals, it will be rounded down (Veeqo doesn't support decimal quantities)</li>
                    <li>Otherwise, Veeqo quantity will be set equal to RPRO quantity</li>
                </ul>
                <p><strong>Note:</strong> It's recommended to run the "Refresh RPRO to Veeqo Reconciliation" process first to ensure you're working with the latest data.</p>
            </div>
            <button id="updateVeeqoFromRproBtn" class="run-once-btn">Update Veeqo from RPRO</button>
            <div id="updateVeeqoFromRproOutput" class="output" style="display: none;"></div>
        </div>



        <div class="card">
            <div class="card-header">
                <h2>Reconcile OSLs to Veeqo</h2>
                <div class="tooltip">ⓘ
                    <span class="tooltiptext">
                        Finds OSLs with inventory discrepancies in Veeqo and enqueues tasks to update their quantities.
                    </span>
                </div>
            </div>
            <div class="form-group" style="background-color: #f8f9fa; padding: 10px; border-left: 4px solid #f39c12; margin-bottom: 15px;">
                <p><strong>Important:</strong> Before running this script, please browse through the <code>v_reconcile_osl_to_veeqo</code> view to review the discrepancies that will be processed.</p>
                <p>This will enqueue tasks to update OSL quantities in Veeqo to match our local quantities.</p>
            </div>
            <button id="reconcileOSLToVeeqoBtn" class="run-once-btn">Reconcile OSLs to Veeqo</button>
            <div id="reconcileOSLToVeeqoOutput" class="output" style="display: none;"></div>
        </div>

        <div class="card">
            <div class="card-header">
                <h2>Reconcile SDAsins to Veeqo</h2>
                <div class="tooltip">ⓘ
                    <span class="tooltiptext">
                        place holder
                    </span>
                </div>
            </div>
            <div class="form-group" style="background-color: #f8f9fa; padding: 10px; border-left: 4px solid #f39c12; margin-bottom: 15px;">
                <p>place holder</p>
            </div>
            <button id="placeholder" class="run-once-btn">place holder</button>
            <div id="placeholder" class="output" style="display: none;"></div>
        </div>


        <div class="card">
            <div class="card-header">
                <h2>Download Shopify Matrixify Export</h2>
                <div class="tooltip">ⓘ
                    <span class="tooltiptext">
                        Instructions for downloading the Matrixify export file from Shopify.
                    </span>
                </div>
            </div>
            <div class="form-group" style="background-color: #f8f9fa; padding: 10px; border-left: 4px solid #9b59b6; margin-bottom: 15px;">
                <p><strong>Important:</strong> Before proceeding with Shopify reconciliation, you need to download the latest Matrixify export file and save it to:</p>
                <p><code>C:\Users\<USER>\supabase_project\data\external data\shopify_matrixify_export_dg.csv</code></p>
                <p>To download the Matrixify export from Shopify:</p>
                <ol>
                    <li>Log in to your Shopify admin</li>
                    <li>Go to Apps > Matrixify</li>
                    <li>Select the "Export" tab</li>
                    <li>Choose the appropriate export template</li>
                    <li>Click "Export" and wait for the export to complete</li>
                    <li>Download the CSV file</li>
                    <li>Save it to the exact path specified above</li>
                </ol>
                <p>After saving the file to the correct location, you can proceed with the Shopify reconciliation tasks below.</p>
            </div>
            <button id="importShopifyMatrixifyBtn" class="run-once-btn">Import Shopify Matrixify Export to imported_table_shopify_products_dz</button>
            <div id="importShopifyMatrixifyOutput" class="output" style="display: none;"></div>
        </div>

        <div class="card">
            <div class="card-header">
                <h2>Reconcile Sold Discs on Shopify</h2>
                <div class="tooltip">ⓘ
                    <span class="tooltiptext">
                        Finds sold discs still showing on Shopify and enqueues tasks to set their quantities to 0.
                    </span>
                </div>
            </div>
            <div class="form-group" style="background-color: #f8f9fa; padding: 10px; border-left: 4px solid #e74c3c; margin-bottom: 15px;">
                <p><strong>Important:</strong> Before running this, please browse through the <code>v_reconcile_d_to_shopify</code> view to review the discs with the issue "Sold disc still showing up on Shopify."</p>
                <p>This will enqueue tasks to set the Shopify inventory to 0 for sold discs. You can then process these tasks one at a time from the task queue.</p>
            </div>
            <button id="enqueueSoldDiscsShopifyBtn" class="run-once-btn">Enqueue Sold Discs Shopify Tasks</button>
            <div id="enqueueSoldDiscsShopifyOutput" class="output" style="display: none;"></div>
        </div>

        <div class="card">
            <div class="card-header">
                <h2>Generate OSL Fields for Null G_Code</h2>
                <div class="tooltip">ⓘ
                    <span class="tooltiptext">
                        Finds OSLs with null g_code and enqueues tasks to generate the fields.
                    </span>
                </div>
            </div>
            <div class="form-group" style="background-color: #f8f9fa; padding: 10px; border-left: 4px solid #3498db; margin-bottom: 15px;">
                <p><strong>Info:</strong> This will find OSLs where g_code is null and enqueue tasks to generate the fields.</p>
                <p>Each task will be processed by the worker to update the OSL record with generated fields.</p>
            </div>
            <div class="form-group">
                <label for="oslBatchSize">Batch Size</label>
                <input type="number" id="oslBatchSize" name="oslBatchSize" value="100" min="1" max="1000" placeholder="Number of OSLs to process">
                <div class="tooltip">ⓘ
                    <span class="tooltiptext">
                        Maximum number of OSLs to process in one batch. Use a smaller number for quicker processing.
                    </span>
                </div>
            </div>
            <button id="enqueueGenerateOslFieldsBtn" class="run-once-btn">Enqueue Generate OSL Fields Tasks</button>
            <div id="enqueueGenerateOslFieldsOutput" class="output" style="display: none;"></div>
        </div>
    </div>

    <div id="worker" class="tab-content">
        <div class="card">
            <div class="card-header">
                <h2>Task Queue Worker</h2>
                <div class="tooltip">ⓘ
                    <span class="tooltiptext">
                        The worker processes pending tasks from the queue.
                        It can be run once or continuously as a daemon.
                    </span>
                </div>
            </div>
            <div class="worker-controls">
                <div class="worker-mode-section">
                    <div class="form-group">
                        <label for="workerMode">Worker Mode</label>
                        <select id="workerMode">
                            <option value="once">Run Once</option>
                            <option value="daemon">Run as Daemon</option>
                        </select>
                        <div class="tooltip">ⓘ
                            <span class="tooltiptext">
                                "Run Once" processes pending tasks once and exits.
                                "Run as Daemon" continuously checks for new tasks every 15 seconds, processing up to 100 tasks per run.
                            </span>
                        </div>
                    </div>
                </div>
                <div class="worker-buttons">
                    <button id="runWorkerBtn" class="run-worker-btn">Run Worker</button>
                    <button id="stopWorkerBtn" class="stop-worker-btn" style="display: none;">Stop Worker</button>
                </div>
            </div>
            <div class="worker-status-section">
                <p><strong>Worker Status:</strong>
                    <span class="status-indicator status-stopped"></span>
                    <span id="workerTabStatus">Stopped</span>
                </p>
                <p><strong>Last Run:</strong> <span id="workerTabLastRunTime">Unknown</span></p>
            </div>
            <div id="workerOutput" class="output" style="display: none;"></div>
        </div>
    </div>

    <div id="flow" class="tab-content">
        <div class="card">
            <div class="card-header">
                <h2>Task Queue Workflows</h2>
                <div class="tooltip">ⓘ
                    <span class="tooltiptext">
                        This section shows all the task types handled by the worker, including the disc publishing workflow and additional task types for field generation, matching, and inventory management.
                    </span>
                </div>
            </div>
            <div class="workflow-diagram">
                <h3>Workflow Steps</h3>
                <ol class="workflow-steps">
                    <li>
                        <strong>Image File Name Update</strong>
                        <p>When a disc's image_file_name is updated in t_discs, two tasks are created:</p>
                        <ul>
                            <li><code>verify_disc_image</code> - Verifies the new image</li>
                            <li><code>clear_disc_verification</code> - Clears previous verification data</li>
                        </ul>
                    </li>
                    <li>
                        <strong>Image Verification</strong>
                        <p>The worker processes the <code>verify_disc_image</code> task:</p>
                        <ul>
                            <li>Checks if the image is accessible</li>
                            <li>Updates t_discs.image_verified and related fields</li>
                            <li>If successful, triggers the next step</li>
                        </ul>
                    </li>
                    <li>
                        <strong>Readiness Check</strong>
                        <p>When image_verified is set to TRUE, a <code>check_if_disc_is_ready</code> task is created:</p>
                        <ul>
                            <li>Checks if all required fields are not null</li>
                            <li>Checks if ready_button is TRUE</li>
                            <li>Checks if image_verified is TRUE</li>
                            <li>Checks if shopify_uploaded_at is NULL</li>
                            <li>If all conditions are met, sets t_discs.ready_new to TRUE</li>
                        </ul>
                    </li>
                    <li>
                        <strong>Publishing Check</strong>
                        <p>When ready_new is set to TRUE, a <code>check_if_disc_ready_to_publish</code> task is created:</p>
                        <ul>
                            <li>Checks if the disc is in v_todo_discs</li>
                            <li>If not, creates a <code>publish_disc</code> task</li>
                            <li>If it is, updates t_discs with a note</li>
                        </ul>
                    </li>
                    <li>
                        <strong>Publishing to Shopify</strong>
                        <p>The worker processes the <code>publish_disc</code> task:</p>
                        <ul>
                            <li>Runs the publishProductDisc.js script</li>
                            <li>Updates t_discs.shopify_uploaded_at when successful</li>
                            <li>This completes the workflow</li>
                        </ul>
                    </li>
                </ol>

                <h3>Additional Task Types</h3>
                <ol class="workflow-steps">
                    <li>
                        <strong>Field Generation Tasks</strong>
                        <p>Tasks that generate and update fields in the database:</p>
                        <ul>
                            <li><code>generate_disc_title_pull_and_handle</code> - Updates t_discs.g_title, t_discs.g_pull, and t_discs.g_handle</li>
                            <li><code>generate_mps_fields</code> - Updates t_mps.g_handle, t_mps.g_flight_numbers, t_mps.g_code, t_mps.g_blurb_with_link, and t_mps.g_seo_metafield_description</li>
                        </ul>
                    </li>
                    <li>
                        <strong>Matching and Inventory Tasks</strong>
                        <p>Tasks that handle disc matching and inventory management:</p>
                        <ul>
                            <li><code>set_disc_carry_cost</code> - Calculates and updates t_discs.carrying_cost based on order cost and shipping multiplier</li>
                            <li><code>match_disc_to_asins</code> - Matches discs to SDASINs based on MPS, weight, and color</li>
                            <li><code>match_disc_to_osl</code> - Matches discs to order sheet lines and updates inventory counts</li>
                            <li><code>plastic_price_verified_work_through_mps_to_find_osls_and_discs_to_upload</code> - Finds MPS records with matching plastic_id and creates tasks for each one</li>
                            <li><code>mps_price_verified_try_upload_osls</code> - Creates tasks for already uploaded OSLs and schedules tasks for OSLs that need uploading</li>
                            <li><code>mps_price_verified_osl_uploaded_look_for_discs</code> - Sets ready_new to FALSE for discs with matching order_sheet_line_id that are ready to publish</li>
                            <li><code>toggle_osl_ready_button</code> - Toggles ready_button for OSLs to trigger the upload process</li>
                            <li><code>reconcile_clear_count_from_shopify_for_sold_disc</code> - Sets Shopify inventory to zero for sold discs still showing on Shopify</li>
                            <li><code>update_osl_after_publish</code> - Updates OSL record after successful publishing to Shopify</li>
                        </ul>
                    </li>
                    <li>
                        <strong>Legacy Image Tasks</strong>
                        <p>Tasks that handle image records in the t_images table:</p>
                        <ul>
                            <li><code>verify_t_images_image</code> - Verifies images in the t_images table</li>
                            <li><code>insert_new_t_images_record</code> - Creates a new record in the t_images table</li>
                            <li><code>delete_t_images_record</code> - Deletes a record from the t_images table</li>
                        </ul>
                    </li>
                </ol>
            </div>
        </div>
    </div>

    <div id="discraft" class="tab-content">
        <!-- Discraft Workflow Section -->
        <div class="card" style="border: 2px solid #9b59b6; background: linear-gradient(135deg, #f8f5ff 0%, #ffffff 100%);">
            <div class="card-header" style="background: linear-gradient(135deg, #9b59b6 0%, #8e44ad 100%); color: white;">
                <h2>🥏 Discraft Vendor Workflow</h2>
                <p>Automated vendor catalog import and order management</p>
            </div>
            <div class="card-body">
                <!-- Step 1: File Download Instructions -->
                <div class="step-group">
                    <h3>📁 Step 1: Download Vendor File</h3>
                    <div class="info-box" style="background: #e8f4fd; border-left-color: #3498db; padding: 15px;">
                        <h4>📋 Instructions:</h4>
                        <ol style="margin: 10px 0; padding-left: 20px;">
                            <li><strong>Download</strong> the latest Discraft order sheet from your vendor portal</li>
                            <li><strong>Save as:</strong> <code>discraftstock.xlsx</code></li>
                            <li><strong>Location:</strong> <code>C:\Users\<USER>\supabase_project\data\external data\discraftstock.xlsx</code></li>
                            <li><strong>Replace</strong> any existing file with the same name</li>
                        </ol>
                        <div style="background: #fff3cd; border: 1px solid #ffeaa7; border-radius: 4px; padding: 10px; margin-top: 10px;">
                            <strong>⚠️ Important:</strong> The file must be named exactly <code>discraftstock.xlsx</code> and placed in the correct folder for the import to work.
                        </div>
                    </div>
                </div>

                <!-- Step 2: Import & Calculate Matches -->
                <div class="step-group">
                    <h3>🔄 Step 2: Import & Calculate Matches</h3>
                    <div class="info-box" style="background: #e8f5e8; border-left-color: #27ae60; padding: 15px;">
                        <h4>📊 Import & Matching Process:</h4>
                        <ul style="margin: 10px 0; padding-left: 20px;">
                            <li><strong>Import Catalog:</strong> Parse vendor file and import products</li>
                            <li><strong>Calculate MPS IDs:</strong> Match products to your inventory</li>
                            <li><strong>Analyze Results:</strong> Review matching success and failures</li>
                        </ul>
                        <div class="button-row">
                            <button class="workflow-btn" onclick="runDiscraftImport()">
                                🚀 Run Full Import & Matching
                            </button>
                            <button class="workflow-btn" style="background: linear-gradient(135deg, #f39c12 0%, #e67e22 100%);" onclick="analyzeMatching()">
                                📊 Analyze Matching Results
                            </button>
                        </div>
                        <div id="discraftImportOutput" class="output" style="display: none;"></div>
                    </div>
                </div>

                <!-- Step 3: Review Parsing Issues -->
                <div class="step-group">
                    <h3>🔧 Step 3: Review Parsing Issues</h3>
                    <div class="info-box" style="background: #e3f2fd; border-left-color: #2196f3; padding: 15px;">
                        <h4>🔍 Identify Parsing Problems:</h4>
                        <p>Review vendor products that don't match your MPS records to identify parsing fixes needed.</p>
                        <div class="button-row">
                            <button class="workflow-btn" style="background: linear-gradient(135deg, #e91e63 0%, #c2185b 100%);" onclick="reviewParsingIssues()">
                                🔧 Review Parsing Issues
                            </button>
                            <button class="workflow-btn" style="background: linear-gradient(135deg, #9c27b0 0%, #7b1fa2 100%);" onclick="reviewUnmatchedOsls()">
                                📋 Review Unmatched OSLs
                            </button>
                        </div>
                        <div id="oslMatchingOutput" class="output" style="display: none;"></div>
                        <div id="unmatchedOslsTable" style="display: none; margin-top: 20px;">
                            <div style="margin-bottom: 15px;">
                                <label style="display: flex; align-items: center; gap: 8px; font-weight: bold;">
                                    <input type="checkbox" id="showInactiveMps" onchange="toggleInactiveMps()">
                                    Show Inactive MPS Records
                                </label>
                            </div>
                            <div id="tableRecordCount" style="margin-bottom: 10px; font-weight: bold; color: #666;"></div>
                            <div style="overflow: auto; max-height: 800px; border: 1px solid #ddd;">
                                <table id="unmatchedOslsTableContent" class="data-table" style="width: 100%; border-collapse: collapse;">
                                    <thead>
                                        <tr style="background: #f5f5f5; position: sticky; top: 0; z-index: 10;">
                                            <th style="border: 1px solid #ddd; padding: 8px; text-align: left; cursor: pointer; user-select: none;" onclick="sortTable(0)" title="Click to sort">
                                                OSL ID <span id="sort-0">⇅</span>
                                            </th>
                                            <th style="border: 1px solid #ddd; padding: 8px; text-align: left; cursor: pointer; user-select: none;" onclick="sortTable(1)" title="Click to sort">
                                                MPS ID <span id="sort-1">⇅</span>
                                            </th>
                                            <th style="border: 1px solid #ddd; padding: 8px; text-align: left; cursor: pointer; user-select: none;" onclick="sortTable(2)" title="Click to sort">
                                                Plastic <span id="sort-2">⇅</span>
                                            </th>
                                            <th style="border: 1px solid #ddd; padding: 8px; text-align: left; cursor: pointer; user-select: none;" onclick="sortTable(3)" title="Click to sort">
                                                Mold <span id="sort-3">⇅</span>
                                            </th>
                                            <th style="border: 1px solid #ddd; padding: 8px; text-align: left; cursor: pointer; user-select: none;" onclick="sortTable(4)" title="Click to sort">
                                                Stamp <span id="sort-4">⇅</span>
                                            </th>
                                            <th style="border: 1px solid #ddd; padding: 8px; text-align: left; cursor: pointer; user-select: none;" onclick="sortTable(5)" title="Click to sort">
                                                Weight <span id="sort-5">⇅</span>
                                            </th>
                                            <th style="border: 1px solid #ddd; padding: 8px; text-align: left; cursor: pointer; user-select: none;" onclick="sortTable(6)" title="Click to sort">
                                                Color <span id="sort-6">⇅</span>
                                            </th>
                                            <th style="border: 1px solid #ddd; padding: 8px; text-align: left; cursor: pointer; user-select: none;" onclick="sortTable(7)" title="Click to sort">
                                                MPS Active <span id="sort-7">⇅</span>
                                            </th>
                                            <th style="border: 1px solid #ddd; padding: 8px; text-align: center;">Action</th>
                                        </tr>
                                    </thead>
                                    <tbody id="unmatchedOslsTableBody">
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Step 4: Export Order Data -->
                <div class="step-group">
                    <h3>📤 Step 4: Export Order Data</h3>
                    <div class="info-box" style="background: #e8f5e8; border-left-color: #27ae60; padding: 15px;">
                        <h4>📊 Export Process:</h4>
                        <p>Export order quantities from <code>v_stats_by_osl_discraft.order</code> back into a copy of the Discraft spreadsheet.</p>
                        <ul style="margin: 10px 0; padding-left: 20px;">
                            <li><strong>Source Data:</strong> Uses calculated order quantities from the view</li>
                            <li><strong>Output File:</strong> Creates copy of original spreadsheet with today's date</li>
                            <li><strong>Format:</strong> <code>discraftstock_YYYY-MM-DD.xlsx</code></li>
                            <li><strong>Location:</strong> Same folder as original file</li>
                        </ul>
                        <div class="button-row">
                            <button class="workflow-btn" style="background: linear-gradient(135deg, #27ae60 0%, #229954 100%);" onclick="exportDiscraftOrders()">
                                📤 Export Order Data to Excel
                            </button>
                        </div>
                        <div id="discraftExportOutput" class="output" style="display: none;"></div>
                    </div>
                </div>

                <!-- Import Statistics -->
                <div class="step-group">
                    <h3>📈 Import Statistics</h3>
                    <div id="discraftStats" class="info-box" style="background: #f8f9fa; border-left-color: #6c757d; padding: 15px;">
                        <p>Import statistics will appear here after running the import...</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div id="informed" class="tab-content">
        <!-- Main Workflow Section -->
        <div class="card" style="border: 2px solid #e67e22; background: linear-gradient(135deg, #fff5f0 0%, #ffffff 100%);">
            <div class="card-header gradient-orange">
                <h2>🚀 Complete Informed Workflow</h2>
                <p>End-to-end automation for Informed Repricer management</p>
            </div>
            <div class="card-body">
                <div class="workflow-section">
                    <div class="info-box" style="background: #fff3cd; border-left-color: #f39c12; padding: 15px; margin-bottom: 20px;">
                        <h3>🔄 Full Automation Process</h3>
                        <p><strong>Complete 3-phase workflow:</strong> Download reports → Import data → Upload pricing</p>
                        <ul style="margin: 10px 0; padding-left: 20px;">
                            <li><strong>Phase 1:</strong> Download latest reports from Informed API</li>
                            <li><strong>Phase 2:</strong> Import reports into Supabase tables</li>
                            <li><strong>Phase 3:</strong> Generate and upload pricing data with REAL calculations</li>
                        </ul>
                        <p><strong>📅 Scheduled:</strong> Runs automatically at 6:30 AM, 12:30 PM, and 6:30 PM CST</p>
                    </div>

                    <div style="text-align: center; margin: 20px 0;">
                        <button id="runCompleteWorkflowBtn" class="run-worker-btn" style="background: linear-gradient(135deg, #e67e22 0%, #d35400 100%); font-size: 16px; padding: 12px 24px; border-radius: 8px; box-shadow: 0 4px 8px rgba(0,0,0,0.2);">
                            ⚡ Run Complete Workflow
                        </button>
                        <div class="tooltip">ⓘ
                            <span class="tooltiptext">
                                Runs the complete end-to-end workflow: Downloads reports from Informed, imports them to Supabase, then uploads pricing data back to Informed. This is the full 3-step process that should be scheduled 3 times per day.
                            </span>
                        </div>
                    </div>
                    <div id="runCompleteWorkflowOutput" class="enhanced-output" style="display: none;"></div>
                </div>
            </div>
        </div>

        <!-- Data Management Section -->
        <div class="card">
            <div class="card-header gradient-blue">
                <h2>📊 Data Management</h2>
                <p>Download and import Informed reports</p>
            </div>
            <div class="card-body">
                <div class="info-box" style="background: #e3f2fd; border-left-color: #2196f3; padding: 15px; margin-bottom: 20px;">
                    <h4>📋 Report Types</h4>
                    <ul style="margin: 10px 0; padding-left: 20px;">
                        <li><strong>All Fields</strong> → <code>it_infor_all_fields</code></li>
                        <li><strong>Competition Landscape</strong> → <code>it_infor_competition_landscape</code></li>
                        <li><strong>No Buy Box</strong> → <code>it_infor_no_buy_box</code></li>
                    </ul>
                </div>

                <div class="step-group">
                    <h3>📊 Report Status</h3>
                    <div id="informedReportStatus" class="form-group" style="background-color: #f8f9fa; padding: 10px; border-radius: 5px; margin-bottom: 15px;">
                        <p>Loading report status...</p>
                    </div>
                </div>

                <div class="step-group">
                    <h3>📥 Download & Import</h3>
                    <div class="button-row">
                        <div class="button-group">
                            <button id="downloadInformedReportsBtn" class="run-once-btn step-btn">📥 Download Reports</button>
                            <small>Get latest data from Informed</small>
                            <div class="form-group" style="margin-top: 10px;">
                                <label for="maxRetries">Max Retries:</label>
                                <input type="number" id="maxRetries" value="30" min="1" max="100" style="width: 60px;">
                                <label for="retryInterval" style="margin-left: 10px;">Retry Interval (seconds):</label>
                                <input type="number" id="retryInterval" value="10" min="5" max="60" style="width: 60px;">
                                <div class="tooltip" style="margin-left: 5px;">ⓘ
                                    <span class="tooltiptext">
                                        Max Retries: Maximum number of times to check if a report is ready.<br>
                                        Retry Interval: Time to wait between status checks in seconds.
                                    </span>
                                </div>
                            </div>
                        </div>

                        <div class="button-group">
                            <button id="importInformedReportsBtn" class="run-once-btn step-btn">📋 Import Reports</button>
                            <small>Process into Supabase tables</small>
                        </div>

                        <div class="button-group">
                            <button id="runFullInformedProcessBtn" class="run-worker-btn workflow-btn">🔄 Download + Import</button>
                            <small>Combined workflow</small>
                            <div class="form-group" style="margin-top: 10px;">
                                <label for="fullProcessMaxRetries">Max Retries:</label>
                                <input type="number" id="fullProcessMaxRetries" value="30" min="1" max="100" style="width: 60px;">
                                <label for="fullProcessRetryInterval" style="margin-left: 10px;">Retry Interval (seconds):</label>
                                <input type="number" id="fullProcessRetryInterval" value="10" min="5" max="60" style="width: 60px;">
                                <div class="tooltip" style="margin-left: 5px;">ⓘ
                                    <span class="tooltiptext">
                                        Max Retries: Maximum number of times to check if a report is ready.<br>
                                        Retry Interval: Time to wait between status checks in seconds.
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="output-section">
                    <div id="downloadInformedReportsOutput" class="step-output" style="display: none;"></div>
                    <div id="importInformedReportsOutput" class="step-output" style="display: none;"></div>
                    <div id="runFullInformedProcessOutput" class="step-output" style="display: none;"></div>
                </div>

                <div class="step-group">
                    <h3>⏰ Scheduler Status</h3>
                    <div id="informedSchedulerStatus" class="form-group" style="background-color: #f8f9fa; padding: 10px; border-radius: 5px; margin-bottom: 15px;">
                        <p>Loading scheduler status...</p>
                    </div>

                    <div class="button-row">
                        <button id="enableInformedSchedulerBtn" class="run-worker-btn">✅ Enable Scheduler</button>
                        <button id="disableInformedSchedulerBtn" class="stop-worker-btn">❌ Disable Scheduler</button>
                    </div>
                    <div id="informedSchedulerOutput" class="step-output" style="display: none;"></div>
                </div>
            </div>
        </div>

        <!-- Pricing Upload Section -->
        <div class="card">
            <div class="card-header gradient-green">
                <h2>💰 Pricing Upload</h2>
                <p>Generate and upload pricing data to Informed</p>
            </div>
            <div class="card-body">
                <div class="info-box success-highlight" style="margin-bottom: 20px;">
                    <h4>📈 Pricing Process Overview</h4>
                    <ol style="margin: 10px 0; padding-left: 20px;">
                        <li><strong>Truncate:</strong> Clear old pricing data from tu_informed table</li>
                        <li><strong>Fill:</strong> Populate with fresh data using REAL pricing calculations</li>
                        <li><strong>Export:</strong> Generate CSV file formatted for Informed API</li>
                        <li><strong>Upload:</strong> Send pricing data to Informed Repricer</li>
                    </ol>
                    <p style="margin: 10px 0 0 0; font-style: italic; color: #27ae60;">
                        ✅ Now using REAL MAP prices from your MPS and plastics data (no more hardcoded values!)
                    </p>
                </div>

                <div class="step-group">
                    <h3>🔧 Individual Steps</h3>
                    <div class="button-grid">
                        <div class="button-group">
                            <button id="truncateTuInformedBtn" class="run-once-btn step-btn">🗑️ 1. Truncate</button>
                            <small>Clear old data</small>
                        </div>

                        <div class="button-group">
                            <button id="fillTuInformedBtn" class="run-once-btn step-btn">📊 2. Fill Data</button>
                            <small>Real pricing calculations</small>
                        </div>

                        <div class="button-group">
                            <button id="exportCsvBtn" class="run-once-btn step-btn">📄 3. Export CSV</button>
                            <small>Generate upload file</small>
                        </div>

                        <div class="button-group">
                            <button id="uploadCsvBtn" class="run-once-btn step-btn">🚀 4. Upload</button>
                            <small>Send to Informed</small>
                        </div>
                    </div>
                </div>

                <div class="step-group" style="margin-top: 30px;">
                    <h3>⚡ Complete Upload Workflow</h3>
                    <div style="text-align: center;">
                        <button id="runUploadWorkflowBtn" class="run-worker-btn" style="background: linear-gradient(135deg, #27ae60 0%, #229954 100%); font-size: 16px; padding: 12px 24px; border-radius: 8px;">
                            🔄 Run All Upload Steps
                        </button>
                        <div class="tooltip">ⓘ
                            <span class="tooltiptext">
                                Runs all 4 upload steps in sequence: Truncate → Fill → Export → Upload. Uses optimized real pricing calculations.
                            </span>
                        </div>
                    </div>
                </div>

                <div class="output-section">
                    <div id="truncateTuInformedOutput" class="step-output" style="display: none;"></div>
                    <div id="fillTuInformedOutput" class="step-output" style="display: none;"></div>
                    <div id="exportCsvOutput" class="step-output" style="display: none;"></div>
                    <div id="uploadCsvOutput" class="step-output" style="display: none;"></div>
                    <div id="runUploadWorkflowOutput" class="step-output" style="display: none;"></div>
                </div>
            </div>
        </div>
    </div>

    <div id="innova" class="tab-content">
        <div class="card">
            <div class="card-header">
                <h2>📦 Innova Order Import</h2>
                <div class="tooltip">ⓘ
                    <span class="tooltiptext">
                        Import Innova order data from Excel files into the it_innova_order_sheet_lines table.
                    </span>
                </div>
            </div>
            <div class="form-group" style="background-color: #f8f9fa; padding: 10px; border-left: 4px solid #3498db; margin-bottom: 15px;">
                <p><strong>Instructions:</strong></p>
                <ol>
                    <li>Download the Innova order form Excel file</li>
                    <li>Save it as <code>innovaorderform.xlsx</code> in the <code>data/external data</code> folder</li>
                    <li>Click the "Import Innova Order Data" button below</li>
                    <li>All existing data will be replaced with the new import</li>
                </ol>
                <p><strong>Expected File Location:</strong> <code>C:\Users\<USER>\supabase_project\data\external data\innovaorderform.xlsx</code></p>
                <p><strong>Data Source:</strong> The import reads from the "Order_Table" sheet in the Excel file</p>
                <p><strong>⚠️ Important:</strong> This import will truncate and replace all existing data in the table</p>
            </div>

            <div class="step-group">
                <h3>📋 Import Process</h3>
                <div class="button-row">
                    <button id="importInnovaDataBtn" class="step-btn">1. Import Data (Truncate & Replace)</button>
                    <button id="viewInnovaDataBtn" class="step-btn">2. View Data</button>
                </div>
                <div class="info-box">
                    <p><strong>Step 1:</strong> Truncates existing data and imports fresh data from the Excel file</p>
                    <p><strong>Step 2:</strong> Shows a sample of the imported data for verification</p>
                    <p><strong>Note:</strong> The import will replace all existing data in the table</p>
                </div>
            </div>

            <div class="step-group">
                <h3>⚙️ Configuration</h3>
                <div class="button-row">
                    <button id="showPlasticMappingBtn" class="step-btn">🔧 Manage Plastic Mappings</button>
                </div>
                <div class="info-box">
                    <p><strong>Plastic Mappings:</strong> Configure how your plastic names map to Innova's plastic names</p>
                    <p><strong>Example:</strong> Your "KC Pro" = Innova's "Pro KC"</p>
                </div>
            </div>

            <div class="step-group">
                <h3>🔄 Phase 1: Validate Existing Matches</h3>
                <div class="button-row">
                    <button id="validateExistingMatchesBtn" class="step-btn">🔍 Basic Validation (String Similarity)</button>
                    <button id="enhancedMatchingBtn" class="step-btn">🚀 Enhanced Matching (Relational)</button>
                    <button id="validateWithClearBtn" class="step-btn">⚠️ Validate & Auto-Clear Broken Links</button>
                    <button id="viewValidationReportBtn" class="step-btn">📊 View Validation Report</button>
                </div>
                <div class="info-box">
                    <p><strong>🔍 Basic Validation:</strong> String similarity between g_code and description</p>
                    <p><strong>🚀 Enhanced Matching:</strong> Uses relational data (mold, plastic, weight) for precise matching</p>
                    <p><strong>⚠️ Auto-Clear:</strong> Clears broken links after validation</p>
                    <p><strong>Safety:</strong> Only processes vendor_id=2 (Innova) records</p>
                </div>
            </div>

            <div id="innovaOutput" class="output" style="display: none;"></div>

            <div id="validationTableContainer" style="display: none; margin-top: 20px; width: 100%; overflow-x: auto;">
                <h3>📊 Validation Results</h3>
                <div style="margin-bottom: 10px;">
                    <label>Filter by Status: </label>
                    <select id="statusFilter">
                        <option value="">All</option>
                        <option value="🟢 PERFECT_MATCH">🟢 Perfect Match</option>
                        <option value="🟡 GOOD_MATCH">🟡 Good Match</option>
                        <option value="🟠 PARTIAL_MATCH">🟠 Partial Match</option>
                        <option value="🔴 POOR_MATCH">🔴 Poor Match</option>
                        <option value="🔴 BROKEN_LINK">🔴 Broken Link</option>
                        <!-- Legacy options for basic validation -->
                        <option value="🟢 HIGH_CONFIDENCE">🟢 High Confidence</option>
                        <option value="🟡 MEDIUM_CONFIDENCE">🟡 Medium Confidence</option>
                        <option value="🟠 LOW_CONFIDENCE">🟠 Low Confidence</option>
                    </select>

                    <label style="margin-left: 20px;">Filter by Score: </label>
                    <select id="confidenceFilter">
                        <option value="">All Scores</option>
                        <option value="100">100% Only</option>
                        <option value="90+">90%+ Only</option>
                        <option value="80+">80%+ Only</option>
                        <option value="70+">70%+ Only</option>
                    </select>

                    <label style="margin-left: 20px;">Filter by OSL Stamp: </label>
                    <select id="stampFilter">
                        <option value="">All Stamps</option>
                        <option value="Stock">Stock</option>
                        <option value="Burst Logo Stock">Burst Logo Stock</option>
                        <option value="Circle Fade Stock">Circle Fade Stock</option>
                        <option value="Stock Character">Stock Character</option>
                        <option value="Character">Character</option>
                        <option value="15 Year Commemorative">15 Year Commemorative</option>
                        <option value="Proto">Proto</option>
                        <option value="Special Edition">Special Edition</option>
                    </select>

                    <label style="margin-left: 20px;">Min Score: </label>
                    <input type="number" id="similarityFilter" min="0" max="100" placeholder="0-100">

                    <button id="applyFilters" style="margin-left: 10px;">Apply Filters</button>
                    <button id="clearFilters" style="margin-left: 5px;">Clear</button>
                </div>

                <div style="max-height: 1800px; overflow-y: auto; border: 1px solid #ddd;">
                    <table id="validationTable" style="width: 100%; border-collapse: collapse; font-size: 12px;">
                        <thead style="background-color: #f5f5f5; position: sticky; top: 0;">
                            <tr id="tableHeaders">
                                <th style="border: 1px solid #ddd; padding: 8px; cursor: pointer;" data-sort="osl_id">OSL ID ↕</th>
                                <th style="border: 1px solid #ddd; padding: 8px; cursor: pointer;" data-sort="g_code">G-Code ↕</th>
                                <th style="border: 1px solid #ddd; padding: 8px; cursor: pointer;" data-sort="innova_description">Innova Description ↕</th>
                                <th style="border: 1px solid #ddd; padding: 8px; cursor: pointer;" data-sort="weight_match">Weight Match ↕</th>
                                <th style="border: 1px solid #ddd; padding: 8px; cursor: pointer;" data-sort="string_similarity">Similarity % ↕</th>
                                <th style="border: 1px solid #ddd; padding: 8px; cursor: pointer;" data-sort="status">Status ↕</th>
                                <th style="border: 1px solid #ddd; padding: 8px;">Actions</th>
                            </tr>
                        </thead>
                        <tbody id="validationTableBody">
                        </tbody>
                    </table>
                </div>

                <div style="margin-top: 10px; padding: 10px; background-color: #f9f9f9; border-radius: 5px;">
                    <div style="margin-bottom: 10px;">
                        <strong>Quick Select:</strong>
                        <button id="selectPerfectBtn" style="margin-left: 10px; background-color: #28a745; color: white; padding: 4px 8px; border: none; border-radius: 3px; font-size: 12px;">🟢 Perfect Matches</button>
                        <button id="selectGoodBtn" style="margin-left: 5px; background-color: #ffc107; color: black; padding: 4px 8px; border: none; border-radius: 3px; font-size: 12px;">🟡 Good Matches</button>
                        <button id="selectHighConfidenceBtn" style="margin-left: 5px; background-color: #17a2b8; color: white; padding: 4px 8px; border: none; border-radius: 3px; font-size: 12px;">High Confidence (70%+)</button>
                        <button id="selectNoneBtn" style="margin-left: 5px; background-color: #6c757d; color: white; padding: 4px 8px; border: none; border-radius: 3px; font-size: 12px;">Select None</button>
                    </div>
                    <div>
                        <strong>Bulk Actions:</strong>
                        <button id="confirmSelectedBtn" style="margin-left: 10px; background-color: #28a745; color: white; padding: 6px 12px; border: none; border-radius: 3px;">✅ Confirm Selected</button>
                        <button id="rejectSelectedBtn" style="margin-left: 5px; background-color: #dc3545; color: white; padding: 6px 12px; border: none; border-radius: 3px;">❌ Reject Selected</button>
                        <button id="phase2BulkConnectBtn" style="margin-left: 5px; background-color: #007bff; color: white; padding: 6px 12px; border: none; border-radius: 3px; display: none;">🔗 Connect Selected</button>
                        <button id="bulkMarkInactiveBtn" style="margin-left: 5px; background-color: #6c757d; color: white; padding: 6px 12px; border: none; border-radius: 3px;">🚫 Mark MPS Inactive</button>
                        <button id="bulkRejectMatchBtn" style="margin-left: 5px; background-color: #ffc107; color: black; padding: 6px 12px; border: none; border-radius: 3px;">❌ Not a Match</button>
                        <span id="selectedCount" style="margin-left: 20px; font-weight: bold;">0 of 0 selected</span>
                    </div>
                </div>
            </div>

            <div class="step-group">
                <h3>🔍 Phase 2: Find New Matches</h3>
                <div class="button-row">
                    <button id="phase2MatchingBtn" class="step-btn">🚀 Find New Matches (Unmatched OSLs)</button>
                    <button id="viewPhase2ReportBtn" class="step-btn">📊 View Phase 2 Report</button>
                </div>
                <div style="margin-top: 10px; padding: 10px; background-color: #f8f9fa; border-radius: 5px;">
                    <div style="margin-bottom: 10px;">
                        <label for="phase2SkipCount" style="font-weight: bold;">Skip Records:</label>
                        <input type="number" id="phase2SkipCount" min="0" max="10000" step="100" value="0" style="width: 80px; margin-left: 10px; margin-right: 20px;">

                        <label for="phase2BatchSize" style="font-weight: bold;">Batch Size:</label>
                        <input type="number" id="phase2BatchSize" min="50" max="1000" step="50" value="300" style="width: 80px; margin-left: 10px; margin-right: 20px;">

                        <label for="phase2MaxCandidates" style="font-weight: bold;">Max Candidates:</label>
                        <select id="phase2MaxCandidates" style="width: 60px; margin-left: 10px; margin-right: 20px;">
                            <option value="1">1</option>
                            <option value="2" selected>2</option>
                            <option value="3">3</option>
                        </select>

                        <label for="phase2MinConfidence" style="font-weight: bold;">Min Confidence:</label>
                        <input type="number" id="phase2MinConfidence" min="30" max="100" step="5" value="60" style="width: 60px; margin-left: 10px; margin-right: 10px;">%
                    </div>
                    <span style="font-size: 12px; color: #666;">
                        Skip: OSLs to skip. Batch: OSLs to process. Max Candidates: Show top 1, 2, or 3 matches per OSL. Min Confidence: Minimum score threshold (60% recommended).
                    </span>
                </div>
                <div class="info-box">
                    <p><strong>🔍 Phase 2 Matching:</strong> Finds potential matches for OSLs without vendor_internal_id</p>
                    <p><strong>📊 Configurable Candidates:</strong> Show 1, 2, or 3 best matches per OSL with configurable confidence threshold</p>
                    <p><strong>🎯 50%+ Confidence:</strong> Only shows matches above 50% confidence threshold</p>
                    <p><strong>✅ Same Workflow:</strong> Same validation table with confirm/reject buttons</p>
                    <p><strong>📈 Progress:</strong> Processes 300 OSLs at a time for maximum efficiency</p>
                </div>
            </div>

            <div id="phase2Output" class="output" style="display: none;"></div>
        </div>
    </div>

    <div id="documentation" class="tab-content">
        <div class="readme">
            <h2>Documentation</h2>
            <div id="readmeContent"></div>
        </div>
    </div>

    <div id="amazon" class="tab-content">
        <div class="container mt-4">
            <h1>Amazon FBA Data Management</h1>

            <div class="card mb-4">
                <div class="card-header">
                    <h2>Amazon SP-API Connection</h2>
                    <div class="tooltip">ⓘ
                        <span class="tooltiptext">
                            Test and manage your Amazon Selling Partner API connection for listing management.
                        </span>
                    </div>
                </div>
                <div class="card-body">
                    <div class="form-group" style="background-color: #f8f9fa; padding: 10px; border-left: 4px solid #17a2b8; margin-bottom: 15px;">
                        <p><strong>What this section does:</strong></p>
                        <ul>
                            <li>Tests your Amazon SP-API connection and credentials</li>
                            <li>Allows you to delete Amazon listings via the task queue</li>
                            <li>Manages Amazon marketplace operations</li>
                            <li>Integrates with your existing task queue system</li>
                        </ul>
                    </div>

                    <div class="form-group" style="background-color: #e7f3ff; padding: 10px; border-left: 4px solid #0066cc; margin-bottom: 15px;">
                        <p><strong>Prerequisites:</strong></p>
                        <ol>
                            <li>Amazon SP-API credentials configured in .env file</li>
                            <li>Valid Amazon Seller ID and marketplace configuration</li>
                            <li>Proper Login with Amazon (LWA) tokens</li>
                        </ol>
                    </div>

                    <div class="button-row">
                        <button id="test-amazon-connection" class="btn btn-info">
                            Test Amazon Connection
                        </button>
                    </div>
                    <div id="amazon-connection-result" class="mt-3"></div>

                    <hr class="my-4">

                    <h3>Delete Amazon Listing</h3>
                    <div class="form-group">
                        <label for="amazon-sku">SKU to Delete:</label>
                        <input type="text" id="amazon-sku" placeholder="Enter Amazon SKU" required>
                    </div>
                    <div class="form-group">
                        <label for="amazon-marketplace">Marketplace (optional):</label>
                        <select id="amazon-marketplace">
                            <option value="ATVPDKIKX0DER">US (ATVPDKIKX0DER)</option>
                            <option value="A2EUQ1WTGCTBG2">Canada (A2EUQ1WTGCTBG2)</option>
                            <option value="A1AM78C64UM0Y8">Mexico (A1AM78C64UM0Y8)</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="amazon-reason">Reason for Deletion (optional):</label>
                        <input type="text" id="amazon-reason" placeholder="e.g., Discontinued product, inventory error">
                    </div>
                    <button id="delete-amazon-listing" class="btn btn-danger">
                        Enqueue Listing Deletion
                    </button>
                    <div id="amazon-deletion-result" class="mt-3"></div>
                </div>
            </div>

            <div class="card mb-4">
                <div class="card-header">
                    <h2>Import Monthly Inventory Ledger Summary</h2>
                    <div class="tooltip">ⓘ
                        <span class="tooltiptext">
                            Imports Amazon Inventory Ledger Summary reports (.txt files) into the database.
                        </span>
                    </div>
                </div>
                <div class="card-body">
                    <div class="form-group" style="background-color: #f8f9fa; padding: 10px; border-left: 4px solid #17a2b8; margin-bottom: 15px;">
                        <p><strong>IMPORTANT:</strong> Before running the import, make sure you have:</p>
                        <ol>
                            <li>Downloaded the "Monthly Inventory Ledger Summary" report from Amazon Seller Central</li>
                            <li>Saved the file as "Amazon Inventory Ledger Summary YYYY-MM.txt" (e.g., "Amazon Inventory Ledger Summary 2025-04.txt")</li>
                            <li>Placed the file in: <code>C:\Users\<USER>\supabase_project\data\external data</code></li>
                        </ol>
                    </div>

                    <p><strong>What this process does:</strong></p>
                    <ul>
                        <li>Scans the data directory for all Amazon Inventory Ledger Summary files</li>
                        <li>Extracts the date (YYYY-MM) from each filename</li>
                        <li>Imports each file into the <code>it_amaz_monthly_inventory_ledger_summary</code> table</li>
                        <li>Logs the import in the task queue</li>
                    </ul>

                    <p><strong>Note:</strong> This process appends data to the existing table. It does not delete or replace existing records.</p>

                    <button id="run-amazon-import" class="btn btn-primary">
                        Run Amazon FBA Import
                    </button>
                    <div id="amazon-import-result" class="mt-3"></div>
                </div>
            </div>

            <div class="card mb-4">
                <div class="card-header">
                    <h2>Import Amazon Fulfilled Inventory Report</h2>
                    <div class="tooltip">ⓘ
                        <span class="tooltiptext">
                            Import the Amazon Fulfilled Inventory Report to get current FBA inventory quantities.
                        </span>
                    </div>
                </div>
                <div class="card-body">
                    <div class="form-group" style="background-color: #f8f9fa; padding: 10px; border-left: 4px solid #17a2b8; margin-bottom: 15px;">
                        <p><strong>What this import does:</strong></p>
                        <ul>
                            <li>Imports Amazon Fulfilled Inventory Report data (snapshot)</li>
                            <li>Shows current FBA inventory quantities by SKU</li>
                            <li>Truncates and replaces existing data (not historical)</li>
                            <li>Maps seller SKUs to ASINs and fulfillment channel SKUs</li>
                        </ul>
                    </div>

                    <div class="form-group" style="background-color: #fff3cd; padding: 10px; border-left: 4px solid #ffc107; margin-bottom: 15px;">
                        <p><strong>File Requirements:</strong></p>
                        <ul>
                            <li>File must be named: <code>Amazon Fulfilled Inventory Report.txt</code></li>
                            <li>Place file in: <code>data/external data/</code> folder</li>
                            <li>Tab-separated format with headers</li>
                            <li>Expected columns: seller-sku, fulfillment-channel-sku, asin, condition-type, Warehouse-Condition-code, Quantity Available</li>
                        </ul>
                    </div>

                    <p><strong>Note:</strong> This is a snapshot import that replaces all existing data. Use this to get current FBA inventory levels.</p>

                    <button id="import-fulfilled-inventory" class="btn btn-info">
                        Import Fulfilled Inventory Report
                    </button>
                    <div id="fulfilled-inventory-import-result" class="mt-3"></div>
                </div>
            </div>

            <div class="card mb-4">
                <div class="card-header">
                    <h2>Refresh FBA Carrying Cost Data</h2>
                    <div class="tooltip">ⓘ
                        <span class="tooltiptext">
                            Refreshes the materialized view that calculates average carrying costs for FBA inventory valuation.
                        </span>
                    </div>
                </div>
                <div class="card-body">
                    <div class="form-group" style="background-color: #f8f9fa; padding: 10px; border-left: 4px solid #28a745; margin-bottom: 15px;">
                        <p><strong>What this process does:</strong></p>
                        <ul>
                            <li>Refreshes the <code>mv_sdasin_avg_carrying_cost_fba</code> materialized view</li>
                            <li>Recalculates average carrying costs for all SDAINs based on current disc data</li>
                            <li>Uses priority logic: unsold discs → FBM sold discs → all sold discs → $5.00 default</li>
                            <li>Updates the cached data used for FBA inventory valuation reports</li>
                        </ul>
                    </div>

                    <p><strong>When to refresh:</strong></p>
                    <ul>
                        <li>After importing new disc data or updating carrying costs</li>
                        <li>Before generating monthly FBA inventory value reports</li>
                        <li>When you notice carrying cost calculations seem outdated</li>
                    </ul>

                    <p><strong>Note:</strong> This process may take a few minutes for large datasets as it recalculates costs for all SDAINs.</p>

                    <button id="refresh-fba-carrying-costs" class="btn btn-success">
                        Refresh FBA Carrying Costs
                    </button>
                    <div id="fba-carrying-costs-result" class="mt-3"></div>
                </div>
            </div>

            <div class="card mb-4">
                <div class="card-header">
                    <h2>Generate Monthly FBA Inventory Value Report</h2>
                    <div class="tooltip">ⓘ
                        <span class="tooltiptext">
                            Calculates and stores monthly FBA inventory valuation based on Amazon ledger data and carrying costs.
                        </span>
                    </div>
                </div>
                <div class="card-body">
                    <div class="form-group" style="background-color: #f8f9fa; padding: 10px; border-left: 4px solid #007bff; margin-bottom: 15px;">
                        <p><strong>What this process does:</strong></p>
                        <ul>
                            <li>Finds the most recent month from Amazon Inventory Ledger Summary data</li>
                            <li>Matches Amazon SKUs (MSKU) to your SDAINs via <code>fba_sku</code> field</li>
                            <li>Uses FBA carrying costs from the materialized view</li>
                            <li>Calculates total units, weighted average cost, and total inventory value</li>
                            <li>Stores results in <code>rpt_amaz_monthly_fba_inventory_value</code> table</li>
                            <li>Processes data in chunks of 1000 records to handle large datasets</li>
                        </ul>
                    </div>

                    <div class="form-group" style="background-color: #e7f3ff; padding: 10px; border-left: 4px solid #0066cc; margin-bottom: 15px;">
                        <p><strong>Prerequisites:</strong></p>
                        <ol>
                            <li>Amazon Inventory Ledger Summary data must be imported</li>
                            <li>FBA carrying costs materialized view should be refreshed</li>
                            <li>SDAINs must have <code>fba_sku</code> values that match Amazon MSKUs</li>
                        </ol>
                    </div>

                    <p><strong>When to run:</strong></p>
                    <ul>
                        <li>After importing new Amazon Inventory Ledger Summary data</li>
                        <li>At month-end to generate inventory valuation reports</li>
                        <li>When you need updated FBA inventory value calculations</li>
                    </ul>

                    <p><strong>Note:</strong> This process handles large datasets by processing records in chunks of 1000. Processing time depends on the amount of data.</p>

                    <button id="generate-fba-inventory-report" class="btn btn-primary">
                        Generate FBA Inventory Report
                    </button>
                    <div id="fba-inventory-report-result" class="mt-3"></div>
                </div>
            </div>

            <div class="card mb-4">
                <div class="card-header">
                    <h2>Generate Historical FBA Inventory Reports</h2>
                    <div class="tooltip">ⓘ
                        <span class="tooltiptext">
                            Generates FBA inventory value reports for ALL months that have data, not just the latest month.
                        </span>
                    </div>
                </div>
                <div class="card-body">
                    <div class="form-group" style="background-color: #f8f9fa; padding: 10px; border-left: 4px solid #6f42c1; margin-bottom: 15px;">
                        <p><strong>What this process does:</strong></p>
                        <ul>
                            <li>Finds ALL distinct months from Amazon Inventory Ledger Summary data</li>
                            <li>Generates a report for each month that has data</li>
                            <li>Updates existing reports or creates new ones as needed</li>
                            <li>Skips months that are locked (locked_at is not null)</li>
                            <li>Processes each month's data in chunks of 1000 records</li>
                            <li>Stores results in <code>rpt_amaz_monthly_fba_inventory_value</code> table</li>
                        </ul>
                    </div>

                    <div class="form-group" style="background-color: #fff3cd; padding: 10px; border-left: 4px solid #ffc107; margin-bottom: 15px;">
                        <p><strong>Important Notes:</strong></p>
                        <ul>
                            <li><strong>Locked Reports:</strong> Months with locked_at set will be skipped entirely</li>
                            <li><strong>Large Dataset:</strong> This processes ALL historical data, which may take several minutes</li>
                            <li><strong>Overwrites Data:</strong> Unlocked existing reports will be updated with current calculations</li>
                        </ul>
                    </div>

                    <div class="form-group" style="background-color: #e7f3ff; padding: 10px; border-left: 4px solid #0066cc; margin-bottom: 15px;">
                        <p><strong>Prerequisites:</strong></p>
                        <ol>
                            <li>Amazon Inventory Ledger Summary data must be imported for multiple months</li>
                            <li>FBA carrying costs materialized view should be refreshed</li>
                            <li>SDAINs must have <code>fba_sku</code> values that match Amazon MSKUs</li>
                        </ol>
                    </div>

                    <p><strong>When to run:</strong></p>
                    <ul>
                        <li>When you need to backfill historical FBA inventory valuations</li>
                        <li>After importing multiple months of Amazon data</li>
                        <li>When carrying costs have been updated and you want to recalculate all unlocked months</li>
                        <li>For comprehensive historical reporting and analysis</li>
                    </ul>

                    <p><strong>Note:</strong> This process can take several minutes depending on the amount of historical data. Progress will be logged during processing.</p>

                    <button id="generate-historical-fba-reports" class="btn btn-warning">
                        Generate Historical FBA Reports
                    </button>
                    <div id="historical-fba-reports-result" class="mt-3"></div>
                </div>
            </div>

            <div class="card mb-4">
                <div class="card-header">
                    <h2>Manage FBA Report Locks</h2>
                    <div class="tooltip">ⓘ
                        <span class="tooltiptext">
                            View, lock, and unlock monthly FBA inventory value reports to control which data can be modified.
                        </span>
                    </div>
                </div>
                <div class="card-body">
                    <div class="form-group" style="background-color: #f8f9fa; padding: 10px; border-left: 4px solid #dc3545; margin-bottom: 15px;">
                        <p><strong>What this interface does:</strong></p>
                        <ul>
                            <li>Shows all monthly FBA inventory value reports</li>
                            <li>Displays lock status and variance between original and new values</li>
                            <li>Allows you to lock reports to prevent future modifications</li>
                            <li>Allows you to unlock reports (with confirmation) to allow modifications</li>
                            <li>Shows when reports were locked and by whom (if available)</li>
                        </ul>
                    </div>

                    <div class="form-group" style="background-color: #fff3cd; padding: 10px; border-left: 4px solid #ffc107; margin-bottom: 15px;">
                        <p><strong>Lock Management:</strong></p>
                        <ul>
                            <li><strong>Lock:</strong> Prevents the main columns from being updated during report generation</li>
                            <li><strong>Unlock:</strong> Allows the main columns to be updated (requires confirmation)</li>
                            <li><strong>Variance Detection:</strong> Shows if _new columns differ from main columns</li>
                        </ul>
                    </div>

                    <div class="mb-3">
                        <button id="refresh-fba-locks" class="btn btn-info">
                            Refresh Report List
                        </button>
                    </div>

                    <div id="fba-locks-table-container">
                        <div class="alert alert-info">Click "Refresh Report List" to load the FBA reports.</div>
                    </div>
                </div>
            </div>
        </div>
    </div>



    <script>
        // Tab functionality
        document.querySelectorAll('.tab').forEach(tab => {
            tab.addEventListener('click', () => {
                const tabId = tab.getAttribute('data-tab');

                // Special handling for ToDo tab - open in new window
                if (tabId === 'todo') {
                    window.open('ToDo.html', '_blank');
                    return;
                }

                // Remove active class from all tabs and content
                document.querySelectorAll('.tab').forEach(t => t.classList.remove('active'));
                document.querySelectorAll('.tab-content').forEach(c => c.classList.remove('active'));

                // Add active class to clicked tab and corresponding content
                tab.classList.add('active');
                document.getElementById(tabId).classList.add('active');
            });
        });

        // Fetch README content
        fetch('README.md')
            .then(response => response.text())
            .then(text => {
                // Simple markdown to HTML conversion (very basic)
                let html = text
                    .replace(/^# (.*$)/gm, '<h1>$1</h1>')
                    .replace(/^## (.*$)/gm, '<h2>$1</h2>')
                    .replace(/^### (.*$)/gm, '<h3>$1</h3>')
                    .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
                    .replace(/\*(.*?)\*/g, '<em>$1</em>')
                    .replace(/```sql([\s\S]*?)```/g, '<pre><code class="language-sql">$1</code></pre>')
                    .replace(/```bash([\s\S]*?)```/g, '<pre><code class="language-bash">$1</code></pre>')
                    .replace(/```json([\s\S]*?)```/g, '<pre><code class="language-json">$1</code></pre>')
                    .replace(/```([\s\S]*?)```/g, '<pre><code>$1</code></pre>')
                    .replace(/^\d\. (.*$)/gm, '<ol><li>$1</li></ol>')
                    .replace(/<\/ol><ol>/g, '');

                document.getElementById('readmeContent').innerHTML = html;
            })
            .catch(error => {
                document.getElementById('readmeContent').innerHTML = '<p>Error loading README: ' + error.message + '</p>';
            });

        // Enqueue task form submission
        document.getElementById('enqueueForm').addEventListener('submit', function(e) {
            e.preventDefault();
            const imageId = document.getElementById('imageId').value;
            const delayMinutes = document.getElementById('delayMinutes').value || 0;

            const outputDiv = document.getElementById('enqueueOutput');
            outputDiv.style.display = 'block';
            outputDiv.innerHTML = 'Enqueueing task for image ID ' + imageId + ' with delay ' + delayMinutes + ' minutes...\n';

            // Call API to enqueue task
            fetch('/api/enqueue-task', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ imageId, delayMinutes }),
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    outputDiv.innerHTML += 'Task enqueued successfully!\n';
                    if (data.details && data.details.output) {
                        outputDiv.innerHTML += data.details.output + '\n';
                    }
                } else {
                    outputDiv.innerHTML += 'Error: ' + (data.error || 'Unknown error') + '\n';
                    if (data.stdout) outputDiv.innerHTML += 'Output: ' + data.stdout + '\n';
                    if (data.stderr) outputDiv.innerHTML += 'Error output: ' + data.stderr + '\n';
                }
            })
            .catch(error => {
                outputDiv.innerHTML += 'Error: ' + error.message + '\n';
                outputDiv.innerHTML += 'Make sure the adminServer.js is running.\n';
            });

            // Refresh task counts after a short delay
            setTimeout(refreshStatus, 2000);
        });



        // View tasks button
        document.getElementById('viewTasksBtn').addEventListener('click', function() {
            const outputDiv = document.getElementById('tasksOutput');
            outputDiv.style.display = 'block';
            outputDiv.innerHTML = 'Loading tasks...\n';

            // Call API to get tasks
            fetch('/api/tasks')
                .then(response => response.json())
                .then(data => {
                    outputDiv.innerHTML = '';

                    if (data.pendingTasks && data.pendingTasks.length > 0) {
                        outputDiv.innerHTML += 'Pending Tasks Ready to Process:\n\n';
                        outputDiv.innerHTML += JSON.stringify(data.pendingTasks, null, 2);
                        outputDiv.innerHTML += '\n\n';
                    } else {
                        outputDiv.innerHTML += 'No pending tasks ready to process.\n\n';
                    }

                    if (data.futureTasks && data.futureTasks.length > 0) {
                        outputDiv.innerHTML += 'Future Scheduled Tasks:\n\n';
                        outputDiv.innerHTML += JSON.stringify(data.futureTasks, null, 2);
                        outputDiv.innerHTML += '\n\n';
                    } else {
                        outputDiv.innerHTML += 'No future scheduled tasks.\n\n';
                    }

                    if (data.completedTasks && data.completedTasks.length > 0) {
                        outputDiv.innerHTML += 'Recently Completed Tasks:\n\n';

                        // Format completed tasks to highlight success messages
                        data.completedTasks.forEach(task => {
                            outputDiv.innerHTML += `Task ID: ${task.id}\n`;
                            outputDiv.innerHTML += `Processed At: ${task.processed_at}\n`;

                            if (task.result && task.result.message) {
                                const messageClass = task.result.message.includes('Success!') ? 'success-message' : 'failure-message';
                                outputDiv.innerHTML += `Result: <span class="${messageClass}">${task.result.message}</span>\n`;
                            } else if (task.result) {
                                outputDiv.innerHTML += `Result: ${JSON.stringify(task.result)}\n`;
                            }

                            outputDiv.innerHTML += '\n';
                        });
                    } else {
                        outputDiv.innerHTML += 'No recently completed tasks.\n';
                    }
                })
                .catch(error => {
                    outputDiv.innerHTML = 'Error loading tasks: ' + error.message + '\n';
                    outputDiv.innerHTML += 'Make sure the adminServer.js is running.\n';
                });
        });

        // Run worker button
        document.getElementById('runWorkerBtn').addEventListener('click', function() {
            const mode = document.getElementById('workerMode').value;
            const outputDiv = document.getElementById('workerOutput');
            outputDiv.style.display = 'block';
            outputDiv.innerHTML = ''; // Clear previous output

            if (mode === 'once') {
                outputDiv.innerHTML = 'Running worker once...\n';

                // Call API to run worker once
                fetch('/api/worker/run-once', {
                    method: 'POST'
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        outputDiv.innerHTML += 'Worker started successfully.\n';
                        outputDiv.innerHTML += 'The worker will process all available tasks and then exit.\n';
                    } else {
                        outputDiv.innerHTML += 'Error: ' + (data.error || 'Unknown error') + '\n';
                    }

                    // Refresh status after a short delay
                    setTimeout(refreshStatus, 1000);
                })
                .catch(error => {
                    outputDiv.innerHTML += 'Error: ' + error.message + '\n';
                    outputDiv.innerHTML += 'Make sure the adminServer.js is running.\n';
                });
            } else {
                outputDiv.innerHTML = 'Starting worker daemon...\n';
                outputDiv.innerHTML += 'The worker will run continuously, checking for new tasks every 15 seconds and processing up to 100 tasks per run.\n';

                // Call API to start worker daemon
                fetch('/api/worker/start-daemon', {
                    method: 'POST'
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        outputDiv.innerHTML += 'Worker daemon started successfully!\n';
                        outputDiv.innerHTML += 'The worker will continue running until stopped.\n';

                        // Update status indicators and buttons
                        updateWorkerStatusIndicators('running');

                        // Start polling for status updates
                        startStatusPolling();
                    } else {
                        outputDiv.innerHTML += 'Error: ' + (data.error || 'Unknown error') + '\n';
                    }
                })
                .catch(error => {
                    outputDiv.innerHTML += 'Error: ' + error.message + '\n';
                    outputDiv.innerHTML += 'Make sure the adminServer.js is running.\n';
                });
            }
        });

        // Stop worker button
        document.getElementById('stopWorkerBtn').addEventListener('click', function() {
            const outputDiv = document.getElementById('workerOutput');
            outputDiv.style.display = 'block';
            outputDiv.innerHTML += '\nStopping worker daemon...\n';

            // Call API to stop worker daemon
            fetch('/api/worker/stop-daemon', {
                method: 'POST'
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Update status indicators and buttons
                    updateWorkerStatusIndicators('stopped');

                    outputDiv.innerHTML += 'Worker daemon stopped successfully.\n';
                    outputDiv.innerHTML += 'All pending tasks will remain in the queue until the worker is started again.\n';

                    // Stop polling for status updates
                    stopStatusPolling();
                } else {
                    outputDiv.innerHTML += 'Error: ' + (data.error || 'Unknown error') + '\n';
                }
            })
            .catch(error => {
                outputDiv.innerHTML += 'Error: ' + error.message + '\n';
                outputDiv.innerHTML += 'Make sure the adminServer.js is running.\n';
            });
        });

        // Refresh status button
        document.getElementById('refreshStatus').addEventListener('click', refreshStatus);

        // Function to refresh status
        function refreshStatus() {
            // First, fetch worker status
            fetch('/api/worker/status')
                .then(response => response.json())
                .then(data => {
                    document.getElementById('pendingTasksCount').textContent = data.pendingTasksCount || 0;
                    document.getElementById('futureTasksCount').textContent = data.futureTasksCount || 0;

                    if (data.lastRunTime) {
                        const formattedTime = new Date(data.lastRunTime).toLocaleString();
                        document.getElementById('lastRunTime').textContent = formattedTime;
                        document.getElementById('workerTabLastRunTime').textContent = formattedTime;
                    } else {
                        document.getElementById('lastRunTime').textContent = 'Never';
                        document.getElementById('workerTabLastRunTime').textContent = 'Never';
                    }

                    // Update worker status indicators (both in dashboard and worker tab)
                    updateWorkerStatusIndicators(data.status);

                    // Update worker output if available
                    if (data.output && data.output.length > 0) {
                        // Show the output div in the worker tab
                        const outputDiv = document.getElementById('workerOutput');
                        outputDiv.style.display = 'block';

                        // Update both console logs
                        updateConsoleLog(data.output);
                    }
                })
                .catch(error => {
                    console.error('Error fetching worker status:', error);
                });

            // Then, directly fetch task type breakdown (don't wait for worker status)
            fetch('/api/tasks/by-type')
                .then(response => response.json())
                .then(data => {
                    if (!data.taskTypes) {
                        const tableBody = document.getElementById('taskTableBody');
                        tableBody.innerHTML = '<tr><td colspan="5" class="loading-message">No task data available.</td></tr>';
                        return;
                    }

                    // Update the workflow table
                    updateWorkflowTable(
                        data.taskTypes.pending || {},
                        data.taskTypes.future || {},
                        data.taskTypes.completed || {},
                        data.taskTypes.error || {}
                    );
                })
                .catch(error => {
                    console.error('Error fetching task type breakdown:', error);
                    const tableBody = document.getElementById('taskTableBody');
                    tableBody.innerHTML = '<tr><td colspan="5" class="loading-message">Error loading task statistics. Make sure the adminServer.js is running.</td></tr>';
                });
        }

        // Function to update worker status indicators
        function updateWorkerStatusIndicators(status) {
            const statusIndicators = document.querySelectorAll('.status-indicator');

            if (status === 'running') {
                // Update all status indicators
                statusIndicators.forEach(indicator => {
                    indicator.classList.remove('status-stopped');
                    indicator.classList.add('status-running');
                });

                // Update status text
                document.getElementById('workerStatus').textContent = 'Running';
                document.getElementById('workerTabStatus').textContent = 'Running';

                // Update buttons
                document.getElementById('runWorkerBtn').style.display = 'none';
                document.getElementById('stopWorkerBtn').style.display = 'inline-block';
                document.getElementById('dashboardRunWorkerOnce').style.display = 'none';
                document.getElementById('dashboardRunWorkerDaemon').style.display = 'none';
            } else {
                // Update all status indicators
                statusIndicators.forEach(indicator => {
                    indicator.classList.remove('status-running');
                    indicator.classList.add('status-stopped');
                });

                // Update status text
                document.getElementById('workerStatus').textContent = 'Stopped';
                document.getElementById('workerTabStatus').textContent = 'Stopped';

                // Update buttons
                document.getElementById('runWorkerBtn').style.display = 'inline-block';
                document.getElementById('stopWorkerBtn').style.display = 'none';
                document.getElementById('dashboardRunWorkerOnce').style.display = 'inline-block';
                document.getElementById('dashboardRunWorkerDaemon').style.display = 'inline-block';
            }
        }

        // Function to update console log
        function updateConsoleLog(output) {
            if (!output || output.length === 0) return;

            const dashboardConsoleLog = document.getElementById('dashboardConsoleLog');
            const workerOutput = document.getElementById('workerOutput');
            const showTimestamps = document.getElementById('showTimestampsCheckbox').checked;
            const filterType = document.getElementById('logFilterSelect').value;
            const taskTypeFilter = document.getElementById('taskTypeFilter').value.toLowerCase();

            // Parse and format the log entries
            let formattedOutput = '';

            output.forEach(line => {
                // Skip empty lines
                if (!line.trim()) return;

                // Add timestamp
                const timestamp = new Date().toLocaleTimeString();

                // Parse the line to extract task information
                let formattedLine = '';
                let logClass = '';

                // Check if this is a task-related log
                const taskIdMatch = line.match(/\[taskQueueWorker\.js\] Processing task (\d+) of type ([\w_]+)/);
                const taskCompletedMatch = line.match(/\[taskQueueWorker\.js\] Successfully (completed|updated|enqueued|matched|generated|processed)/);
                const taskErrorMatch = line.match(/\[taskQueueWorker\.js\] (Error|Exception|Failed)/);
                const taskProcessingMatch = line.match(/\[taskQueueWorker\.js\] (Processing|Fetching|Calculating|Finding|Updating)/);

                // Apply filters
                if (filterType !== 'all') {
                    if (filterType === 'error' && !taskErrorMatch) return;
                    if (filterType === 'success' && !taskCompletedMatch) return;
                    if (filterType === 'processing' && !taskProcessingMatch && !taskIdMatch) return;
                }

                // Apply task type filter
                if (taskTypeFilter && taskIdMatch) {
                    const taskType = taskIdMatch[2].toLowerCase();
                    if (!taskType.includes(taskTypeFilter)) return;
                }

                // Format based on content
                if (taskIdMatch) {
                    const taskId = taskIdMatch[1];
                    const taskType = taskIdMatch[2];

                    formattedLine = `<div class="log-entry">`;
                    if (showTimestamps) {
                        formattedLine += `<span class="timestamp">${timestamp}</span>`;
                    }
                    formattedLine += `<span class="task-id">Task #${taskId}</span>`;
                    formattedLine += `<span class="task-type">${taskType}</span>`;
                    formattedLine += `<span>Processing started</span></div>`;
                    logClass = 'processing';
                } else if (taskCompletedMatch) {
                    formattedLine = `<div class="log-entry completed">`;
                    if (showTimestamps) {
                        formattedLine += `<span class="timestamp">${timestamp}</span>`;
                    }
                    formattedLine += line.replace(/\[taskQueueWorker\.js\]/, '');
                    formattedLine += `</div>`;
                    logClass = 'success';
                } else if (taskErrorMatch) {
                    formattedLine = `<div class="log-entry error">`;
                    if (showTimestamps) {
                        formattedLine += `<span class="timestamp">${timestamp}</span>`;
                    }
                    formattedLine += line.replace(/\[taskQueueWorker\.js\]/, '');
                    formattedLine += `</div>`;
                    logClass = 'error';
                } else if (taskProcessingMatch) {
                    formattedLine = `<div class="log-entry">`;
                    if (showTimestamps) {
                        formattedLine += `<span class="timestamp">${timestamp}</span>`;
                    }
                    formattedLine += `<span class="processing">${line.replace(/\[taskQueueWorker\.js\]/, '')}</span></div>`;
                    logClass = 'info';
                } else if (line.includes('[taskQueueWorker.js]')) {
                    formattedLine = `<div class="log-entry">`;
                    if (showTimestamps) {
                        formattedLine += `<span class="timestamp">${timestamp}</span>`;
                    }
                    formattedLine += `<span class="info">${line.replace(/\[taskQueueWorker\.js\]/, '')}</span></div>`;
                    logClass = 'info';
                } else {
                    formattedLine = `<div class="log-entry">`;
                    if (showTimestamps) {
                        formattedLine += `<span class="timestamp">${timestamp}</span>`;
                    }
                    formattedLine += line;
                    formattedLine += `</div>`;
                }

                formattedOutput += formattedLine;
            });

            // Update both console logs
            dashboardConsoleLog.innerHTML = formattedOutput || '<div class="log-entry info">No log entries match the current filters</div>';

            // Auto-scroll if enabled
            if (document.getElementById('autoScrollCheckbox').checked) {
                dashboardConsoleLog.scrollTop = dashboardConsoleLog.scrollHeight;
                if (workerOutput.style.display !== 'none') {
                    workerOutput.scrollTop = workerOutput.scrollHeight;
                }
            }
        }

        // Function to update the workflow table
        function updateWorkflowTable(pendingTasks, futureTasks, completedTasks, errorTasks) {
            // Define the workflow order of task types
            const workflowOrder = [
                // Image verification workflow
                'clear_disc_verification',
                'verify_disc_image',
                'check_if_disc_is_ready',
                'check_if_disc_ready_to_publish',
                'publish_disc',
                'publish_product_disc',

                // Field generation tasks
                'generate_disc_title_pull_and_handle',
                'generate_mps_fields',

                // Matching and inventory tasks
                'set_disc_carry_cost',
                'match_disc_to_asins',
                'match_disc_to_osl',
                'mps_price_verified_try_upload_osls',
                'mps_price_verified_osl_uploaded_look_for_discs',
                'toggle_osl_ready_button',
                'plastic_price_verified_work_through_mps_to_find_osls_and_discs_to_upload',
                'reconcile_clear_count_from_shopify_for_sold_disc',
                'update_osl_after_publish',

                // Legacy image tasks
                'verify_t_images_image',
                'insert_new_t_images_record',
                'delete_t_images_record'
            ];

            const tableBody = document.getElementById('taskTableBody');
            tableBody.innerHTML = '';

            // Create a row for each task type in workflow order
            workflowOrder.forEach(taskType => {
                const pendingCount = pendingTasks[taskType] || 0;
                const futureCount = futureTasks[taskType] || 0;
                const completedCount = completedTasks[taskType] || 0;
                const errorCount = errorTasks[taskType] || 0;

                const row = document.createElement('tr');

                // Task name cell
                const nameCell = document.createElement('td');
                nameCell.className = 'task-name';
                nameCell.textContent = taskType;
                row.appendChild(nameCell);

                // Pending count cell
                const pendingCell = document.createElement('td');
                pendingCell.className = 'count ' + (pendingCount > 0 ? 'count-nonzero' : 'count-zero');
                pendingCell.textContent = pendingCount;
                row.appendChild(pendingCell);

                // Future count cell
                const futureCell = document.createElement('td');
                futureCell.className = 'count ' + (futureCount > 0 ? 'count-nonzero' : 'count-zero');
                futureCell.textContent = futureCount;
                row.appendChild(futureCell);

                // Completed count cell
                const completedCell = document.createElement('td');
                completedCell.className = 'count ' + (completedCount > 0 ? 'count-success' : 'count-zero');
                completedCell.textContent = completedCount;
                row.appendChild(completedCell);

                // Error count cell
                const errorCell = document.createElement('td');
                errorCell.className = 'count ' + (errorCount > 0 ? 'count-error' : 'count-zero');
                errorCell.textContent = errorCount;
                row.appendChild(errorCell);

                tableBody.appendChild(row);
            });

            // Show the table
            document.getElementById('taskWorkflowTable').style.display = 'block';
        }

        // Status polling
        let statusPollInterval = null;

        function startStatusPolling() {
            if (!statusPollInterval) {
                statusPollInterval = setInterval(refreshStatus, 5000);
            }
        }

        function stopStatusPolling() {
            if (statusPollInterval) {
                clearInterval(statusPollInterval);
                statusPollInterval = null;
            }
        }

        // Dashboard Run Worker Once button
        document.getElementById('dashboardRunWorkerOnce').addEventListener('click', function() {
            const outputDiv = document.getElementById('workerOutput');
            outputDiv.style.display = 'block';
            outputDiv.innerHTML = 'Running worker once from dashboard...';

            // Call API to run worker once
            fetch('/api/worker/run-once', {
                method: 'POST'
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    outputDiv.innerHTML += '\nWorker started successfully. Check the Worker tab for output.\n';

                    // Update last run time
                    document.getElementById('lastRunTime').textContent = new Date().toLocaleString();

                    // Refresh status after a short delay to allow worker to process tasks
                    setTimeout(refreshStatus, 2000);
                } else {
                    outputDiv.innerHTML += '\nError: ' + (data.error || 'Unknown error') + '\n';
                }
            })
            .catch(error => {
                outputDiv.innerHTML += '\nError: ' + error.message + '\n';
                outputDiv.innerHTML += 'Make sure the adminServer.js is running.\n';
            });
        });

        // Dashboard Run Worker as Daemon button
        document.getElementById('dashboardRunWorkerDaemon').addEventListener('click', function() {
            const outputDiv = document.getElementById('workerOutput');
            outputDiv.style.display = 'block';
            outputDiv.innerHTML = 'Starting worker daemon from dashboard...\n';
            outputDiv.innerHTML += 'The worker will run continuously, checking for new tasks every 15 seconds and processing up to 100 tasks per run.\n';

            // Call API to start worker daemon
            fetch('/api/worker/start-daemon', {
                method: 'POST'
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    outputDiv.innerHTML += 'Worker daemon started successfully!\n';
                    outputDiv.innerHTML += 'The worker will continue running until stopped. Check the Worker tab for live output.\n';

                    // Update status indicators and buttons
                    updateWorkerStatusIndicators('running');

                    // Start polling for status updates
                    startStatusPolling();
                } else {
                    outputDiv.innerHTML += 'Error: ' + (data.error || 'Unknown error') + '\n';
                }
            })
            .catch(error => {
                outputDiv.innerHTML += 'Error: ' + error.message + '\n';
                outputDiv.innerHTML += 'Make sure the adminServer.js is running.\n';
            });
        });

        // Initialize the page
        document.addEventListener('DOMContentLoaded', function() {
            // Initial status refresh
            refreshStatus();

            // Start polling for status updates
            startStatusPolling();
        });

        // Clear console button
        document.getElementById('clearConsoleBtn').addEventListener('click', function() {
            document.getElementById('dashboardConsoleLog').innerHTML = '';
        });

        // Log filter controls
        document.getElementById('logFilterSelect').addEventListener('change', function() {
            // Re-apply the current output with the new filter
            updateConsoleLog(workerOutput);
        });

        document.getElementById('taskTypeFilter').addEventListener('input', function() {
            // Re-apply the current output with the new filter
            updateConsoleLog(workerOutput);
        });

        document.getElementById('showTimestampsCheckbox').addEventListener('change', function() {
            // Re-apply the current output with the new timestamp setting
            updateConsoleLog(workerOutput);
        });

        // Import Veeqo Sellables button
        document.getElementById('importVeeqoSellablesBtn').addEventListener('click', function() {
            const outputDiv = document.getElementById('importVeeqoSellablesOutput');
            outputDiv.style.display = 'block';
            outputDiv.innerHTML = 'Truncating imported_table_veeqo_sellables_export table and running import_veeqo_sellables.js...';

            // Call API to import Veeqo sellables
            fetch('/api/import-veeqo-sellables', {
                method: 'POST'
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    outputDiv.innerHTML = 'Veeqo sellables imported successfully!\n';
                    if (data.details && data.details.stdout) {
                        outputDiv.innerHTML += data.details.stdout + '\n';
                    }
                } else {
                    outputDiv.innerHTML = 'Error: ' + (data.error || 'Unknown error') + '\n';
                    if (data.stdout) outputDiv.innerHTML += 'Output: ' + data.stdout + '\n';
                    if (data.stderr) outputDiv.innerHTML += 'Error output: ' + data.stderr + '\n';
                }
            })
            .catch(error => {
                outputDiv.innerHTML = 'Error: ' + error.message + '\n';
                outputDiv.innerHTML += 'Make sure the adminServer.js is running.\n';
            });
        });

        // Delete Wrong Veeqo Records button
        document.getElementById('deleteWrongVeeqoRecordsBtn').addEventListener('click', function() {
            const outputDiv = document.getElementById('deleteWrongVeeqoRecordsOutput');
            outputDiv.style.display = 'block';

            // Get the limit value
            const limitSelect = document.getElementById('deleteVeeqoLimit');
            const limit = limitSelect.value;

            // Update the message based on the limit
            let message = 'Identifying and deleting';
            if (limit === '1') {
                message += ' 1 Veeqo record';
            } else if (limit === '10') {
                message += ' 10 Veeqo records';
            } else {
                message += ' all Veeqo records';
            }
            message += ' with variant titles containing " (D#"...';

            outputDiv.innerHTML = message;

            // Confirm before proceeding
            let confirmMessage = 'This will permanently delete ';
            if (limit === '1') {
                confirmMessage += '1 Veeqo product variant';
            } else if (limit === '10') {
                confirmMessage += '10 Veeqo product variants';
            } else {
                confirmMessage += 'ALL Veeqo product variants';
            }
            confirmMessage += ' with variant titles containing " (D#". This action cannot be undone. Are you sure you want to proceed?';

            if (!confirm(confirmMessage)) {
                outputDiv.innerHTML = 'Operation cancelled by user.';
                return;
            }

            // Call API to delete wrong Veeqo records
            fetch('/api/delete-wrong-veeqo-records', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ limit: limit ? parseInt(limit) : null })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    outputDiv.innerHTML = 'Wrong Veeqo records deleted successfully!\n';
                    if (data.details && data.details.stdout) {
                        outputDiv.innerHTML += data.details.stdout + '\n';
                    }

                    if (data.summary) {
                        outputDiv.innerHTML += '\nDeletion Summary:\n';
                        outputDiv.innerHTML += `- Total records processed: ${data.summary.totalProcessed}\n`;
                        outputDiv.innerHTML += `- Successful deletions: ${data.summary.successCount}\n`;
                        outputDiv.innerHTML += `- Failed deletions: ${data.summary.failureCount}\n`;
                    }
                } else {
                    outputDiv.innerHTML = 'Error: ' + (data.error || 'Unknown error') + '\n';
                    if (data.stdout) outputDiv.innerHTML += 'Output: ' + data.stdout + '\n';
                    if (data.stderr) outputDiv.innerHTML += 'Error output: ' + data.stderr + '\n';
                }
            })
            .catch(error => {
                outputDiv.innerHTML = 'Error: ' + error.message + '\n';
                outputDiv.innerHTML += 'Make sure the adminServer.js is running.\n';
            });
        });

        // Reconcile Discs to Veeqo button
        document.getElementById('reconcileDToVeeqoBtn').addEventListener('click', function() {
            const outputDiv = document.getElementById('reconcileDToVeeqoOutput');
            outputDiv.style.display = 'block';
            outputDiv.innerHTML = 'Running reconcileDToVeeqo.js to update discs with inventory discrepancies...';

            // Call API to run reconcileDToVeeqo.js
            fetch('/api/reconcile-d-to-veeqo', {
                method: 'POST'
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    outputDiv.innerHTML = 'Disc to Veeqo reconciliation completed successfully!\n';
                    if (data.details && data.details.stdout) {
                        outputDiv.innerHTML += data.details.stdout + '\n';
                    }
                } else {
                    outputDiv.innerHTML = 'Error: ' + (data.error || 'Unknown error') + '\n';
                    if (data.stdout) outputDiv.innerHTML += 'Output: ' + data.stdout + '\n';
                    if (data.stderr) outputDiv.innerHTML += 'Error output: ' + data.stderr + '\n';
                }
            })
            .catch(error => {
                outputDiv.innerHTML = 'Error: ' + error.message + '\n';
                outputDiv.innerHTML += 'Make sure the adminServer.js is running.\n';
            });
        });

        // Enqueue Sold Discs Shopify Tasks button
        document.getElementById('enqueueSoldDiscsShopifyBtn').addEventListener('click', function() {
            const outputDiv = document.getElementById('enqueueSoldDiscsShopifyOutput');
            outputDiv.style.display = 'block';
            outputDiv.innerHTML = 'Finding sold discs still showing on Shopify and enqueueing tasks...';

            // Call API to enqueue tasks
            fetch('/api/enqueue-sold-discs-shopify-tasks', {
                method: 'POST'
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    outputDiv.innerHTML = data.message + '\n';

                    if (data.count > 0) {
                        outputDiv.innerHTML += '\nEnqueued tasks:\n';
                        data.tasks.forEach(task => {
                            outputDiv.innerHTML += `- Task ID: ${task.id}, Disc ID: ${task.disc_id}\n`;
                        });
                        outputDiv.innerHTML += '\nYou can now process these tasks one at a time from the task queue.\n';
                    }
                } else {
                    outputDiv.innerHTML = 'Error: ' + (data.error || 'Unknown error') + '\n';
                }
            })
            .catch(error => {
                outputDiv.innerHTML = 'Error: ' + error.message + '\n';
                outputDiv.innerHTML += 'Make sure the adminServer.js is running.\n';
            });
        });

        // Reconcile RPRO to Veeqo button
        document.getElementById('reconcileRproToVeeqoBtn').addEventListener('click', function() {
            const outputDiv = document.getElementById('reconcileRproToVeeqoOutput');
            outputDiv.style.display = 'block';
            outputDiv.innerHTML = 'Refreshing RPRO to Veeqo reconciliation data...';

            // Call API to refresh reconciliation data
            fetch('/api/reconcile-rpro-to-veeqo', {
                method: 'POST'
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    outputDiv.innerHTML = 'RPRO to Veeqo reconciliation completed successfully!\n';
                    if (data.details && data.details.stdout) {
                        outputDiv.innerHTML += data.details.stdout + '\n';
                    }

                    if (data.stats) {
                        outputDiv.innerHTML += '\nReconciliation Statistics:\n';
                        outputDiv.innerHTML += `- Total records: ${data.stats.totalCount}\n`;
                        outputDiv.innerHTML += `- Records with discrepancies: ${data.stats.discrepancyCount}\n`;
                        outputDiv.innerHTML += `- Records where RPRO has more: ${data.stats.rproMoreCount}\n`;
                        outputDiv.innerHTML += `- Records where Veeqo has more: ${data.stats.veeqoMoreCount}\n`;
                        outputDiv.innerHTML += `- Records with matching quantities: ${data.stats.matchingCount}\n`;
                    }
                } else {
                    outputDiv.innerHTML = 'Error: ' + (data.error || 'Unknown error') + '\n';
                    if (data.stdout) outputDiv.innerHTML += 'Output: ' + data.stdout + '\n';
                    if (data.stderr) outputDiv.innerHTML += 'Error output: ' + data.stderr + '\n';
                }
            })
            .catch(error => {
                outputDiv.innerHTML = 'Error: ' + error.message + '\n';
                outputDiv.innerHTML += 'Make sure the adminServer.js is running.\n';
            });
        });

        // Update Veeqo from RPRO button
        document.getElementById('updateVeeqoFromRproBtn').addEventListener('click', function() {
            const outputDiv = document.getElementById('updateVeeqoFromRproOutput');
            outputDiv.style.display = 'block';
            outputDiv.innerHTML = 'Updating Veeqo quantities from RPRO data...';

            // Confirm before proceeding
            if (!confirm('This will update Veeqo quantities to match RPRO quantities for all records with discrepancies. Are you sure you want to proceed?')) {
                outputDiv.innerHTML = 'Operation cancelled by user.';
                return;
            }

            // Call API to update Veeqo quantities
            fetch('/api/update-veeqo-from-rpro', {
                method: 'POST'
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    outputDiv.innerHTML = 'Veeqo quantities updated successfully!\n';
                    if (data.details && data.details.stdout) {
                        outputDiv.innerHTML += data.details.stdout + '\n';
                    }

                    if (data.summary) {
                        outputDiv.innerHTML += '\nUpdate Summary:\n';
                        outputDiv.innerHTML += `- Total records processed: ${data.summary.totalProcessed}\n`;
                        outputDiv.innerHTML += `- Successful updates: ${data.summary.successCount}\n`;
                        outputDiv.innerHTML += `- Failed updates: ${data.summary.failureCount}\n`;
                    }
                } else {
                    outputDiv.innerHTML = 'Error: ' + (data.error || 'Unknown error') + '\n';
                    if (data.stdout) outputDiv.innerHTML += 'Output: ' + data.stdout + '\n';
                    if (data.stderr) outputDiv.innerHTML += 'Error output: ' + data.stderr + '\n';
                }
            })
            .catch(error => {
                outputDiv.innerHTML = 'Error: ' + error.message + '\n';
                outputDiv.innerHTML += 'Make sure the adminServer.js is running.\n';
            });
        });

        // Reconcile OSL Stats button
        document.getElementById('reconcileOslStatsBtn').addEventListener('click', function() {
            const outputDiv = document.getElementById('reconcileOslStatsOutput');
            outputDiv.style.display = 'block';
            outputDiv.innerHTML = 'Updating t_inv_osl records with correct quantity counts from v_stats_by_osl...';

            // Call API to reconcile OSL stats
            fetch('/api/reconcile-osl-stats', {
                method: 'POST'
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    outputDiv.innerHTML = 'OSL stats reconciliation completed successfully!\n';
                    if (data.details && data.details.stdout) {
                        outputDiv.innerHTML += data.details.stdout + '\n';
                    }

                    if (data.summary) {
                        outputDiv.innerHTML += '\nReconciliation Summary:\n';
                        outputDiv.innerHTML += `- Total batches: ${data.summary.totalBatches}\n`;
                        outputDiv.innerHTML += `- Total records updated: ${data.summary.totalUpdated}\n`;
                    }
                } else {
                    outputDiv.innerHTML = 'Error: ' + (data.error || 'Unknown error') + '\n';
                    if (data.stdout) outputDiv.innerHTML += 'Output: ' + data.stdout + '\n';
                    if (data.stderr) outputDiv.innerHTML += 'Error output: ' + data.stderr + '\n';
                }
            })
            .catch(error => {
                outputDiv.innerHTML = 'Error: ' + error.message + '\n';
                outputDiv.innerHTML += 'Make sure the adminServer.js is running.\n';
            });
        });

        // Reconcile SDAsin Stats button
        document.getElementById('reconcileSdasinStatsBtn').addEventListener('click', function() {
            const outputDiv = document.getElementById('reconcileSdasinStatsOutput');
            outputDiv.style.display = 'block';
            outputDiv.innerHTML = 'Updating t_inv_sdasin records with correct quantity counts from v_stats_by_sdasin...';

            // Call API to reconcile SDAsin stats
            fetch('/api/reconcile-sdasin-stats', {
                method: 'POST'
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    outputDiv.innerHTML = 'SDAsin stats reconciliation completed successfully!\n';
                    if (data.details && data.details.stdout) {
                        outputDiv.innerHTML += data.details.stdout + '\n';
                    }

                    if (data.summary) {
                        outputDiv.innerHTML += '\nReconciliation Summary:\n';
                        outputDiv.innerHTML += `- Total batches: ${data.summary.totalBatches}\n`;
                        outputDiv.innerHTML += `- Total records updated: ${data.summary.totalUpdated}\n`;
                    }
                } else {
                    outputDiv.innerHTML = 'Error: ' + (data.error || 'Unknown error') + '\n';
                    if (data.stdout) outputDiv.innerHTML += 'Output: ' + data.stdout + '\n';
                    if (data.stderr) outputDiv.innerHTML += 'Error output: ' + data.stderr + '\n';
                }
            })
            .catch(error => {
                outputDiv.innerHTML = 'Error: ' + error.message + '\n';
                outputDiv.innerHTML += 'Make sure the adminServer.js is running.\n';
            });
        });

        // Import Shopify Matrixify Export button
        document.getElementById('importShopifyMatrixifyBtn').addEventListener('click', function() {
            const outputDiv = document.getElementById('importShopifyMatrixifyOutput');
            outputDiv.style.display = 'block';
            outputDiv.innerHTML = 'Importing Shopify Matrixify export data to imported_table_shopify_products_dz...';

            // Call API to import Shopify Matrixify export
            fetch('/api/import-shopify-matrixify', {
                method: 'POST'
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    outputDiv.innerHTML = 'Shopify Matrixify export imported successfully to imported_table_shopify_products_dz.<br>';
                    if (data.details && data.details.stdout) {
                        outputDiv.innerHTML += '<pre>' + data.details.stdout + '</pre>';
                    }
                } else {
                    outputDiv.innerHTML = 'Error: ' + data.error + '<br>';
                    if (data.stdout) {
                        outputDiv.innerHTML += '<pre>' + data.stdout + '</pre>';
                    }
                    if (data.stderr) {
                        outputDiv.innerHTML += '<pre class="error">' + data.stderr + '</pre>';
                    }
                }
            })
            .catch(error => {
                outputDiv.innerHTML = 'Error: ' + error.message + '\n';
                outputDiv.innerHTML += 'Make sure the adminServer.js is running.\n';
            });
        });

        // Reconcile OSLs to Veeqo button
        document.getElementById('reconcileOSLToVeeqoBtn').addEventListener('click', function() {
            const outputDiv = document.getElementById('reconcileOSLToVeeqoOutput');
            outputDiv.style.display = 'block';
            outputDiv.innerHTML = 'Finding OSLs with inventory discrepancies in Veeqo and enqueueing tasks...';

            // Call API to enqueue tasks
            fetch('/api/reconcile-osl-to-veeqo', {
                method: 'POST'
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    outputDiv.innerHTML = data.message + '\n';

                    if (data.summary) {
                        outputDiv.innerHTML += '\nReconciliation Summary:\n';
                        outputDiv.innerHTML += `- Total records processed: ${data.summary.totalProcessed}\n`;
                        outputDiv.innerHTML += `- Records skipped (missing available_quantity): ${data.summary.skippedRecords}\n`;
                        outputDiv.innerHTML += `- Tasks enqueued: ${data.summary.tasksEnqueued}\n`;
                    }

                    if (data.count > 0) {
                        outputDiv.innerHTML += '\nEnqueued tasks:\n';
                        // Limit to showing first 20 tasks to avoid overwhelming the UI
                        const tasksToShow = data.tasks.slice(0, 20);
                        tasksToShow.forEach(task => {
                            outputDiv.innerHTML += `- Task ID: ${task.id}, OSL ID: ${task.osl_id}\n`;
                        });

                        if (data.tasks.length > 20) {
                            outputDiv.innerHTML += `... and ${data.tasks.length - 20} more tasks\n`;
                        }

                        outputDiv.innerHTML += '\nYou can now process these tasks one at a time from the task queue.\n';
                    }
                } else {
                    outputDiv.innerHTML = 'Error: ' + (data.error || 'Unknown error') + '\n';
                }
            })
            .catch(error => {
                outputDiv.innerHTML = 'Error: ' + error.message + '\n';
                outputDiv.innerHTML += 'Make sure the adminServer.js is running.\n';
            });
        });

        // Import Discs from Google Sheets form
        document.getElementById('importDiscsForm').addEventListener('submit', function(e) {
            e.preventDefault();

            const googleSheetsUrl = document.getElementById('googleSheetsUrl').value;
            const validateOnly = document.getElementById('validateOnly').checked;
            const outputDiv = document.getElementById('importDiscsOutput');
            const submitButton = e.target.querySelector('button[type="submit"]');

            // Disable the submit button to prevent multiple submissions
            submitButton.disabled = true;
            const originalButtonText = submitButton.innerHTML;
            submitButton.innerHTML = validateOnly ? 'Validating...' : 'Importing...';

            outputDiv.style.display = 'block';
            outputDiv.innerHTML = validateOnly ? 'Validating disc data from Google Sheets...' : 'Importing disc data from Google Sheets...';

            // Call API to import discs
            fetch('/api/import-discs-from-sheets', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    googleSheetsUrl: googleSheetsUrl,
                    validateOnly: validateOnly
                }),
            })
            .then(response => response.json())
            .then(data => {
                // Re-enable the submit button
                submitButton.disabled = false;
                submitButton.innerHTML = originalButtonText;

                if (data.success) {
                    outputDiv.innerHTML = data.message + '<br>';

                    if (data.validationResults) {
                        outputDiv.innerHTML += '<h4>Validation Results:</h4>';
                        outputDiv.innerHTML += `<p>Total records: ${data.validationResults.totalRecords}</p>`;
                        outputDiv.innerHTML += `<p>Valid records: ${data.validationResults.validRecords}</p>`;
                        outputDiv.innerHTML += `<p>Invalid records: ${data.validationResults.invalidRecords}</p>`;
                        outputDiv.innerHTML += `<p>Empty records (skipped): ${data.validationResults.emptyRecords || 0}</p>`;

                        if (data.validationResults.errors && data.validationResults.errors.length > 0) {
                            outputDiv.innerHTML += '<h5>Validation Errors:</h5><ul>';
                            data.validationResults.errors.forEach(error => {
                                outputDiv.innerHTML += `<li>Row ${error.row}: ${error.message}</li>`;
                            });
                            outputDiv.innerHTML += '</ul>';
                        }
                    }

                    if (data.importResults && !validateOnly) {
                        outputDiv.innerHTML += '<h4>Import Results:</h4>';
                        outputDiv.innerHTML += `<p>Records imported: ${data.importResults.recordsImported}</p>`;

                        if (data.importResults.orderedResults && data.importResults.orderedResults.length > 0) {
                            outputDiv.innerHTML += '<h5>New IDs in Original Sheet Order (copy these back to your Google Sheet):</h5>';
                            outputDiv.innerHTML += '<textarea rows="15" cols="80" readonly>';
                            outputDiv.innerHTML += 'Row\tStatus\tNew ID\n';
                            outputDiv.innerHTML += '---\t------\t------\n';
                            data.importResults.orderedResults.forEach(result => {
                                const status = result.status === 'valid' ? 'IMPORTED' :
                                             result.status === 'invalid' ? 'INVALID' : 'EMPTY';
                                const newId = result.newId || 'N/A';
                                outputDiv.innerHTML += `${result.originalRow}\t${status}\t${newId}\n`;
                            });
                            outputDiv.innerHTML += '</textarea>';

                            outputDiv.innerHTML += '<h5>Quick Copy - Just the IDs (for new_id column):</h5>';
                            outputDiv.innerHTML += '<textarea rows="10" cols="20" readonly>';
                            data.importResults.newIds.forEach(id => {
                                outputDiv.innerHTML += `${id || ''}\n`;
                            });
                            outputDiv.innerHTML += '</textarea>';
                        }
                    }
                } else {
                    outputDiv.innerHTML = 'Error: ' + (data.error || 'Unknown error') + '<br>';
                    if (data.details) {
                        outputDiv.innerHTML += 'Details: ' + data.details + '<br>';
                    }
                }
            })
            .catch(error => {
                // Re-enable the submit button on error
                submitButton.disabled = false;
                submitButton.innerHTML = originalButtonText;

                outputDiv.innerHTML = 'Error: ' + error.message + '<br>';
                outputDiv.innerHTML += 'Make sure the adminServer.js is running.<br>';
            });
        });



        // Generate OSL Fields for Null G_Code button
        document.getElementById('enqueueGenerateOslFieldsBtn').addEventListener('click', function() {
            const batchSize = document.getElementById('oslBatchSize').value || 100;
            const outputDiv = document.getElementById('enqueueGenerateOslFieldsOutput');
            outputDiv.style.display = 'block';
            outputDiv.innerHTML = `Finding OSLs with null g_code and enqueueing tasks (batch size: ${batchSize})...`;

            // Call API to enqueue tasks
            fetch('/api/enqueue-generate-osl-fields', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ batchSize }),
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    outputDiv.innerHTML = data.message + '\n';

                    if (data.count > 0) {
                        outputDiv.innerHTML += '\nEnqueued tasks:\n';
                        // Limit to showing first 20 tasks to avoid overwhelming the UI
                        const tasksToShow = data.tasks.slice(0, 20);
                        tasksToShow.forEach(task => {
                            outputDiv.innerHTML += `- Task ID: ${task.task_id}, OSL ID: ${task.osl_id}\n`;
                        });

                        if (data.tasks.length > 20) {
                            outputDiv.innerHTML += `... and ${data.tasks.length - 20} more tasks\n`;
                        }

                        outputDiv.innerHTML += '\nYou can now process these tasks using the task queue worker.\n';
                    } else {
                        outputDiv.innerHTML += '\nNo OSLs with null g_code were found.\n';
                    }
                } else {
                    outputDiv.innerHTML = 'Error: ' + (data.error || 'Unknown error') + '\n';
                }
            })
            .catch(error => {
                outputDiv.innerHTML = 'Error: ' + error.message + '\n';
                outputDiv.innerHTML += 'Make sure the adminServer.js is running.\n';
            });
        });

        // No need for this anymore as we call refreshStatus() on DOMContentLoaded

        // Informed tab functionality

        // Function to refresh Informed report status
        function refreshInformedStatus() {
            const statusDiv = document.getElementById('informedReportStatus');

            // Call API to get report status
            fetch('/api/informed/status')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        let statusHtml = '<table class="task-table" style="width: 100%;">';
                        statusHtml += '<thead><tr><th>Report</th><th>Last Updated</th><th>Record Count</th><th>Status</th></tr></thead>';
                        statusHtml += '<tbody>';

                        // Add row for each report
                        data.reports.forEach(report => {
                            const statusClass = report.status === 'OK' ? 'success-message' : 'failure-message';
                            const formattedDate = report.lastUpdated ? new Date(report.lastUpdated).toLocaleString() : 'Never';

                            statusHtml += `<tr>
                                <td>${report.name}</td>
                                <td>${formattedDate}</td>
                                <td>${report.recordCount || 0}</td>
                                <td class="${statusClass}">${report.status}</td>
                            </tr>`;
                        });

                        statusHtml += '</tbody></table>';
                        statusDiv.innerHTML = statusHtml;
                    } else {
                        statusDiv.innerHTML = `<p class="failure-message">Error: ${data.error || 'Failed to get report status'}</p>`;
                    }
                })
                .catch(error => {
                    statusDiv.innerHTML = `<p class="failure-message">Error: ${error.message}</p>
                        <p>Make sure the adminServer.js is running.</p>`;
                });

            // Call API to get scheduler status
            const schedulerStatusDiv = document.getElementById('informedSchedulerStatus');

            fetch('/api/informed/scheduler-status')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        let statusHtml = '<p>';

                        if (data.enabled) {
                            statusHtml += '<span class="success-message">✓ Scheduler is enabled</span><br>';
                            statusHtml += `Next run: ${new Date(data.nextRun).toLocaleString()}<br>`;
                            statusHtml += `Schedule: ${data.schedule}`;
                        } else {
                            statusHtml += '<span class="failure-message">✗ Scheduler is disabled</span>';
                        }

                        statusHtml += '</p>';
                        schedulerStatusDiv.innerHTML = statusHtml;

                        // Update button visibility
                        document.getElementById('enableInformedSchedulerBtn').style.display = data.enabled ? 'none' : 'inline-block';
                        document.getElementById('disableInformedSchedulerBtn').style.display = data.enabled ? 'inline-block' : 'none';
                    } else {
                        schedulerStatusDiv.innerHTML = `<p class="failure-message">Error: ${data.error || 'Failed to get scheduler status'}</p>`;
                    }
                })
                .catch(error => {
                    schedulerStatusDiv.innerHTML = `<p class="failure-message">Error: ${error.message}</p>
                        <p>Make sure the adminServer.js is running.</p>`;
                });
        }

        // Download Informed Reports button
        document.getElementById('downloadInformedReportsBtn').addEventListener('click', function() {
            const outputDiv = document.getElementById('downloadInformedReportsOutput');
            const maxRetries = parseInt(document.getElementById('maxRetries').value) || 30;
            const retryInterval = parseInt(document.getElementById('retryInterval').value) || 10;

            outputDiv.style.display = 'block';
            outputDiv.innerHTML = `Downloading reports from Informed Repricer (max retries: ${maxRetries}, retry interval: ${retryInterval}s)...<br>`;
            outputDiv.innerHTML += 'This may take several minutes as we wait for reports to be generated...<br>';
            outputDiv.innerHTML += '<div id="downloadProgress">Requesting reports...</div>';

            // Create a progress indicator
            const progressDiv = document.getElementById('downloadProgress');
            let dots = 0;
            const progressInterval = setInterval(() => {
                dots = (dots + 1) % 4;
                const dotsStr = '.'.repeat(dots);
                progressDiv.innerHTML = `Waiting for reports to be generated${dotsStr}`;
            }, 500);

            // Disable the button during download
            const downloadBtn = document.getElementById('downloadInformedReportsBtn');
            downloadBtn.disabled = true;
            downloadBtn.innerHTML = 'Downloading...';

            // Call API to download reports
            fetch('/api/informed/download-reports', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    maxRetries: maxRetries,
                    retryInterval: retryInterval * 1000 // Convert to milliseconds
                })
            })
            .then(response => response.json())
            .then(data => {
                // Clear the progress indicator
                clearInterval(progressInterval);

                // Re-enable the button
                downloadBtn.disabled = false;
                downloadBtn.innerHTML = 'Download Reports from Informed';

                if (data.success) {
                    outputDiv.innerHTML = 'Reports downloaded successfully!<br>';

                    if (data.reports) {
                        outputDiv.innerHTML += '<ul>';
                        data.reports.forEach(report => {
                            if (report.success) {
                                outputDiv.innerHTML += `<li>${report.report}: Success</li>`;
                            } else {
                                outputDiv.innerHTML += `<li>${report.report}: Failed - ${report.error}</li>`;
                            }
                        });
                        outputDiv.innerHTML += '</ul>';
                    }

                    // Refresh status after download
                    refreshInformedStatus();
                } else {
                    outputDiv.innerHTML = `Error: ${data.error || 'Failed to download reports'}<br>`;
                    if (data.details) {
                        outputDiv.innerHTML += `Details: ${data.details}<br>`;
                    }
                }
            })
            .catch(error => {
                // Clear the progress indicator
                clearInterval(progressInterval);

                // Re-enable the button
                downloadBtn.disabled = false;
                downloadBtn.innerHTML = 'Download Reports from Informed';

                outputDiv.innerHTML = `Error: ${error.message}<br>`;
                outputDiv.innerHTML += 'Make sure the adminServer.js is running.<br>';
            });
        });

        // Import Informed Reports button
        document.getElementById('importInformedReportsBtn').addEventListener('click', function() {
            const outputDiv = document.getElementById('importInformedReportsOutput');
            outputDiv.style.display = 'block';
            outputDiv.innerHTML = 'Importing reports to Supabase...';

            // Call API to import reports
            fetch('/api/informed/import-reports', {
                method: 'POST'
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    outputDiv.innerHTML = 'Reports imported successfully!<br>';

                    if (data.reports) {
                        outputDiv.innerHTML += '<ul>';
                        data.reports.forEach(report => {
                            outputDiv.innerHTML += `<li>${report.name}: ${report.recordCount} records imported</li>`;
                        });
                        outputDiv.innerHTML += '</ul>';
                    }

                    // Refresh status after import
                    refreshInformedStatus();
                } else {
                    outputDiv.innerHTML = `Error: ${data.error || 'Failed to import reports'}<br>`;
                    if (data.details) {
                        outputDiv.innerHTML += `Details: ${data.details}<br>`;
                    }
                }
            })
            .catch(error => {
                outputDiv.innerHTML = `Error: ${error.message}<br>`;
                outputDiv.innerHTML += 'Make sure the adminServer.js is running.<br>';
            });
        });

        // Run Full Informed Process button
        document.getElementById('runFullInformedProcessBtn').addEventListener('click', function() {
            const outputDiv = document.getElementById('runFullInformedProcessOutput');
            const maxRetries = parseInt(document.getElementById('fullProcessMaxRetries').value) || 30;
            const retryInterval = parseInt(document.getElementById('fullProcessRetryInterval').value) || 10;

            outputDiv.style.display = 'block';
            outputDiv.innerHTML = `Running full Informed process (max retries: ${maxRetries}, retry interval: ${retryInterval}s)...<br>`;
            outputDiv.innerHTML += 'This may take several minutes as we wait for reports to be generated...<br>';
            outputDiv.innerHTML += '<div id="fullProcessProgress">Requesting reports...</div>';

            // Create a progress indicator
            const progressDiv = document.getElementById('fullProcessProgress');
            let dots = 0;
            const progressInterval = setInterval(() => {
                dots = (dots + 1) % 4;
                const dotsStr = '.'.repeat(dots);
                progressDiv.innerHTML = `Waiting for reports to be generated${dotsStr}`;
            }, 500);

            // Disable the button during processing
            const processBtn = document.getElementById('runFullInformedProcessBtn');
            processBtn.disabled = true;
            processBtn.innerHTML = 'Running...';

            // Call API to run full process
            fetch('/api/informed/run-full-process', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    maxRetries: maxRetries,
                    retryInterval: retryInterval * 1000 // Convert to milliseconds
                })
            })
            .then(response => response.json())
            .then(data => {
                // Clear the progress indicator
                clearInterval(progressInterval);

                // Re-enable the button
                processBtn.disabled = false;
                processBtn.innerHTML = 'Run Full Process (Download & Import)';

                if (data.success) {
                    outputDiv.innerHTML = 'Full process completed successfully!<br>';

                    if (data.reports) {
                        outputDiv.innerHTML += '<h4>Download Results:</h4><ul>';
                        data.reports.forEach(report => {
                            outputDiv.innerHTML += `<li>${report.report}: ${report.downloadStatus}</li>`;
                            if (report.downloadError) {
                                outputDiv.innerHTML += `<ul><li class="error">Error: ${report.downloadError}</li></ul>`;
                            }
                        });
                        outputDiv.innerHTML += '</ul>';

                        outputDiv.innerHTML += '<h4>Import Results:</h4><ul>';
                        data.reports.forEach(report => {
                            outputDiv.innerHTML += `<li>${report.report}: ${report.importStatus} (${report.recordCount} records)</li>`;
                            if (report.importError) {
                                outputDiv.innerHTML += `<ul><li class="error">Error: ${report.importError}</li></ul>`;
                            }
                        });
                        outputDiv.innerHTML += '</ul>';
                    }

                    // Refresh status after full process
                    refreshInformedStatus();
                } else {
                    outputDiv.innerHTML = `Error: ${data.error || 'Failed to run full process'}<br>`;
                    if (data.details) {
                        outputDiv.innerHTML += `Details: ${data.details}<br>`;
                    }
                }
            })
            .catch(error => {
                // Clear the progress indicator
                clearInterval(progressInterval);

                // Re-enable the button
                processBtn.disabled = false;
                processBtn.innerHTML = 'Run Full Process (Download & Import)';

                outputDiv.innerHTML = `Error: ${error.message}<br>`;
                outputDiv.innerHTML += 'Make sure the adminServer.js is running.<br>';
            });
        });

        // Run Complete Workflow button (Download + Import + Upload)
        document.getElementById('runCompleteWorkflowBtn').addEventListener('click', function() {
            const outputDiv = document.getElementById('runCompleteWorkflowOutput');

            // Get retry parameters from the same inputs as individual download button
            const maxRetries = parseInt(document.getElementById('maxRetries').value) || 30;
            const retryInterval = parseInt(document.getElementById('retryInterval').value) || 10;

            outputDiv.style.display = 'block';
            outputDiv.innerHTML = 'Running complete end-to-end workflow...<br>';
            outputDiv.innerHTML += 'This will download reports, import them, then upload pricing data to Informed.<br>';
            outputDiv.innerHTML += `Using retry parameters: max retries=${maxRetries}, retry interval=${retryInterval}s<br><br>`;

            // Disable the button during processing
            const workflowBtn = document.getElementById('runCompleteWorkflowBtn');
            workflowBtn.disabled = true;
            workflowBtn.innerHTML = 'Running Complete Workflow...';

            // Call API to run complete workflow (same as individual buttons)
            fetch('/api/informed/run-complete-workflow', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    maxRetries: maxRetries,
                    retryInterval: retryInterval * 1000 // Convert to milliseconds
                })
            })
            .then(response => response.json())
            .then(data => {
                // Re-enable the button
                workflowBtn.disabled = false;
                workflowBtn.innerHTML = '⚡ Run Complete Workflow';

                if (data.success) {
                    outputDiv.innerHTML = 'Complete end-to-end workflow completed successfully!<br><br>';

                    if (data.results) {
                        // Show results for each phase
                        if (data.results.download) {
                            outputDiv.innerHTML += `<strong>Phase 1 - Download Reports:</strong> ${data.results.download.success ? 'Success' : 'Failed'}<br>`;
                        }

                        if (data.results.import) {
                            outputDiv.innerHTML += `<strong>Phase 2 - Import Reports:</strong> ${data.results.import.success ? 'Success' : 'Failed'}<br>`;
                        }

                        if (data.results.upload) {
                            outputDiv.innerHTML += `<strong>Phase 3 - Upload Pricing:</strong> ${data.results.upload.success ? 'Success' : 'Failed'}<br>`;
                            if (data.results.upload.results && data.results.upload.results.upload && data.results.upload.results.upload.result) {
                                outputDiv.innerHTML += `Upload result: ${JSON.stringify(data.results.upload.results.upload.result)}<br>`;
                            }
                        }
                    }

                    // Refresh status after complete workflow
                    refreshInformedStatus();
                } else {
                    outputDiv.innerHTML = `Error: ${data.error || 'Failed to run complete workflow'}<br>`;

                    if (data.results) {
                        outputDiv.innerHTML += '<br><strong>Phase Results:</strong><br>';
                        Object.keys(data.results).forEach(phase => {
                            if (data.results[phase]) {
                                outputDiv.innerHTML += `${phase}: ${data.results[phase].success ? 'Success' : 'Failed'}`;
                                if (data.results[phase].error) {
                                    outputDiv.innerHTML += ` - ${data.results[phase].error}`;
                                }
                                outputDiv.innerHTML += '<br>';
                            }
                        });
                    }
                }
            })
            .catch(error => {
                // Re-enable the button
                workflowBtn.disabled = false;
                workflowBtn.innerHTML = '⚡ Run Complete Workflow';

                outputDiv.innerHTML = `Error: ${error.message}<br>`;
                outputDiv.innerHTML += 'Make sure the adminServer.js is running.<br>';
            });
        });

        // Enable Informed Scheduler button
        document.getElementById('enableInformedSchedulerBtn').addEventListener('click', function() {
            const outputDiv = document.getElementById('informedSchedulerOutput');
            outputDiv.style.display = 'block';
            outputDiv.innerHTML = 'Enabling Informed scheduler...';

            // Call API to enable scheduler
            fetch('/api/informed/enable-scheduler', {
                method: 'POST'
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    outputDiv.innerHTML = 'Scheduler enabled successfully!<br>';
                    outputDiv.innerHTML += `Next run: ${new Date(data.nextRun).toLocaleString()}<br>`;
                    outputDiv.innerHTML += `Schedule: ${data.schedule}`;

                    // Refresh status after enabling
                    refreshInformedStatus();
                } else {
                    outputDiv.innerHTML = `Error: ${data.error || 'Failed to enable scheduler'}<br>`;
                }
            })
            .catch(error => {
                outputDiv.innerHTML = `Error: ${error.message}<br>`;
                outputDiv.innerHTML += 'Make sure the adminServer.js is running.<br>';
            });
        });

        // Disable Informed Scheduler button
        document.getElementById('disableInformedSchedulerBtn').addEventListener('click', function() {
            const outputDiv = document.getElementById('informedSchedulerOutput');
            outputDiv.style.display = 'block';
            outputDiv.innerHTML = 'Disabling Informed scheduler...';

            // Call API to disable scheduler
            fetch('/api/informed/disable-scheduler', {
                method: 'POST'
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    outputDiv.innerHTML = 'Scheduler disabled successfully!<br>';

                    // Refresh status after disabling
                    refreshInformedStatus();
                } else {
                    outputDiv.innerHTML = `Error: ${data.error || 'Failed to disable scheduler'}<br>`;
                }
            })
            .catch(error => {
                outputDiv.innerHTML = `Error: ${error.message}<br>`;
                outputDiv.innerHTML += 'Make sure the adminServer.js is running.<br>';
            });
        });

        // Truncate tu_informed button
        document.getElementById('truncateTuInformedBtn').addEventListener('click', function() {
            const outputDiv = document.getElementById('truncateTuInformedOutput');
            outputDiv.style.display = 'block';
            outputDiv.innerHTML = 'Truncating tu_informed table...';

            // Call API to truncate tu_informed
            fetch('/api/informed/truncate-tu-informed', {
                method: 'POST'
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    outputDiv.innerHTML = `tu_informed truncated successfully!<br>`;
                    outputDiv.innerHTML += `Deleted ${data.deletedCount || 0} records.<br>`;
                } else {
                    outputDiv.innerHTML = `Error: ${data.error || 'Failed to truncate tu_informed'}<br>`;
                }
            })
            .catch(error => {
                outputDiv.innerHTML = `Error: ${error.message}<br>`;
                outputDiv.innerHTML += 'Make sure the adminServer.js is running.<br>';
            });
        });

        // Fill tu_informed button
        document.getElementById('fillTuInformedBtn').addEventListener('click', function() {
            const outputDiv = document.getElementById('fillTuInformedOutput');
            outputDiv.style.display = 'block';
            outputDiv.innerHTML = 'Filling tu_informed from v_informed_upload...';

            // Call API to fill tu_informed
            fetch('/api/informed/fill-tu-informed', {
                method: 'POST'
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    outputDiv.innerHTML = `tu_informed filled successfully!<br>`;
                    outputDiv.innerHTML += `Inserted ${data.recordCount || 0} records.<br>`;
                } else {
                    outputDiv.innerHTML = `Error: ${data.error || 'Failed to fill tu_informed'}<br>`;
                }
            })
            .catch(error => {
                outputDiv.innerHTML = `Error: ${error.message}<br>`;
                outputDiv.innerHTML += 'Make sure the adminServer.js is running.<br>';
            });
        });

        // Export CSV button
        document.getElementById('exportCsvBtn').addEventListener('click', function() {
            const outputDiv = document.getElementById('exportCsvOutput');
            outputDiv.style.display = 'block';
            outputDiv.innerHTML = 'Generating CSV export from tu_informed...';

            // Call API to export CSV
            fetch('/api/informed/export-csv', {
                method: 'POST'
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    outputDiv.innerHTML = `CSV export generated successfully!<br>`;
                    const lines = data.csvContent.split('\n').length - 1;
                    outputDiv.innerHTML += `Generated CSV with ${lines} data rows.<br>`;

                    // Store CSV content for upload step
                    window.lastGeneratedCsv = data.csvContent;

                    // Show preview of first few lines
                    const previewLines = data.csvContent.split('\n').slice(0, 5);
                    outputDiv.innerHTML += '<br><strong>Preview:</strong><br>';
                    outputDiv.innerHTML += '<pre style="background-color: #f8f9fa; padding: 10px; border-radius: 3px; max-height: 150px; overflow-y: auto;">';
                    outputDiv.innerHTML += previewLines.join('\n');
                    if (lines > 4) {
                        outputDiv.innerHTML += '\n... and ' + (lines - 4) + ' more rows';
                    }
                    outputDiv.innerHTML += '</pre>';
                } else {
                    outputDiv.innerHTML = `Error: ${data.error || 'Failed to export CSV'}<br>`;
                }
            })
            .catch(error => {
                outputDiv.innerHTML = `Error: ${error.message}<br>`;
                outputDiv.innerHTML += 'Make sure the adminServer.js is running.<br>';
            });
        });

        // Upload CSV button
        document.getElementById('uploadCsvBtn').addEventListener('click', function() {
            const outputDiv = document.getElementById('uploadCsvOutput');
            outputDiv.style.display = 'block';

            // Check if we have CSV content from the export step
            if (!window.lastGeneratedCsv) {
                outputDiv.innerHTML = 'Error: No CSV content available. Please run "Export CSV" first.<br>';
                return;
            }

            outputDiv.innerHTML = 'Uploading CSV to Informed Repricer...';

            // Call API to upload CSV
            fetch('/api/informed/upload-csv', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    csvContent: window.lastGeneratedCsv
                })
            })
            .then(response => {
                if (!response.ok) {
                    return response.text().then(text => {
                        throw new Error(`HTTP ${response.status}: ${text}`);
                    });
                }
                return response.json();
            })
            .then(data => {
                if (data.success) {
                    outputDiv.innerHTML = `CSV uploaded to Informed successfully!<br>`;
                    if (data.result) {
                        outputDiv.innerHTML += `Upload result: ${JSON.stringify(data.result)}<br>`;
                    }
                } else {
                    outputDiv.innerHTML = `Error: ${data.error || 'Failed to upload CSV'}<br>`;
                }
            })
            .catch(error => {
                outputDiv.innerHTML = `Error: ${error.message}<br>`;
                outputDiv.innerHTML += 'Make sure the adminServer.js is running.<br>';
            });
        });

        // Run Upload Workflow button
        document.getElementById('runUploadWorkflowBtn').addEventListener('click', function() {
            const outputDiv = document.getElementById('runUploadWorkflowOutput');
            outputDiv.style.display = 'block';
            outputDiv.innerHTML = 'Running complete upload workflow...<br>';
            outputDiv.innerHTML += 'This will truncate tu_informed, fill it with fresh data, generate CSV, and upload to Informed.<br><br>';

            // Disable the button during processing
            const workflowBtn = document.getElementById('runUploadWorkflowBtn');
            workflowBtn.disabled = true;
            workflowBtn.innerHTML = 'Running Workflow...';

            // Call API to run complete workflow
            fetch('/api/informed/run-upload-workflow', {
                method: 'POST'
            })
            .then(response => response.json())
            .then(data => {
                // Re-enable the button
                workflowBtn.disabled = false;
                workflowBtn.innerHTML = 'Run Complete Upload Workflow';

                if (data.success) {
                    outputDiv.innerHTML = 'Complete upload workflow completed successfully!<br><br>';

                    if (data.results) {
                        // Show results for each step
                        if (data.results.truncate) {
                            outputDiv.innerHTML += `<strong>Step 1 - Truncate:</strong> ${data.results.truncate.success ? 'Success' : 'Failed'}<br>`;
                            if (data.results.truncate.deletedCount !== undefined) {
                                outputDiv.innerHTML += `Deleted ${data.results.truncate.deletedCount} records.<br>`;
                            }
                        }

                        if (data.results.fill) {
                            outputDiv.innerHTML += `<strong>Step 2 - Fill:</strong> ${data.results.fill.success ? 'Success' : 'Failed'}<br>`;
                            if (data.results.fill.recordCount !== undefined) {
                                outputDiv.innerHTML += `Inserted ${data.results.fill.recordCount} records.<br>`;
                            }
                        }

                        if (data.results.export) {
                            outputDiv.innerHTML += `<strong>Step 3 - Export:</strong> ${data.results.export.success ? 'Success' : 'Failed'}<br>`;
                            if (data.results.export.csvContent) {
                                const lines = data.results.export.csvContent.split('\n').length - 1;
                                outputDiv.innerHTML += `Generated CSV with ${lines} data rows.<br>`;
                            }
                        }

                        if (data.results.upload) {
                            outputDiv.innerHTML += `<strong>Step 4 - Upload:</strong> ${data.results.upload.success ? 'Success' : 'Failed'}<br>`;
                            if (data.results.upload.result) {
                                outputDiv.innerHTML += `Upload result: ${JSON.stringify(data.results.upload.result)}<br>`;
                            }
                        }
                    }
                } else {
                    outputDiv.innerHTML = `Error: ${data.error || 'Failed to run upload workflow'}<br>`;

                    if (data.results) {
                        outputDiv.innerHTML += '<br><strong>Step Results:</strong><br>';
                        Object.keys(data.results).forEach(step => {
                            if (data.results[step]) {
                                outputDiv.innerHTML += `${step}: ${data.results[step].success ? 'Success' : 'Failed'}`;
                                if (data.results[step].error) {
                                    outputDiv.innerHTML += ` - ${data.results[step].error}`;
                                }
                                outputDiv.innerHTML += '<br>';
                            }
                        });
                    }
                }
            })
            .catch(error => {
                // Re-enable the button
                workflowBtn.disabled = false;
                workflowBtn.innerHTML = 'Run Complete Upload Workflow';

                outputDiv.innerHTML = `Error: ${error.message}<br>`;
                outputDiv.innerHTML += 'Make sure the adminServer.js is running.<br>';
            });
        });

        // Amazon FBA Import button
        document.getElementById('run-amazon-import').addEventListener('click', function() {
            const outputDiv = document.getElementById('amazon-import-result');
            const button = document.getElementById('run-amazon-import');

            // Disable button and show loading
            button.disabled = true;
            button.innerHTML = 'Running import...';
            outputDiv.innerHTML = '<div class="alert alert-info">Import in progress. This may take a few minutes...</div>';

            // Call API to import Amazon FBA data
            fetch('/api/import-amazon-fba', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                }
            })
            .then(response => response.json())
            .then(data => {
                // Re-enable button
                button.disabled = false;
                button.innerHTML = 'Run Amazon FBA Import';

                if (data.success) {
                    outputDiv.innerHTML = `
                        <div class="alert alert-success">
                            <h4>Import Successful!</h4>
                            <p>Imported ${data.importCount || 0} records into the database.</p>
                            <pre class="mt-3" style="max-height: 200px; overflow-y: auto;">${data.details || ''}</pre>
                        </div>
                    `;
                } else {
                    outputDiv.innerHTML = `
                        <div class="alert alert-danger">
                            <h4>Import Failed</h4>
                            <p>${data.error || 'Unknown error occurred'}</p>
                            <pre class="mt-3" style="max-height: 200px; overflow-y: auto;">${data.details || ''}</pre>
                        </div>
                    `;
                }
            })
            .catch(error => {
                // Re-enable button
                button.disabled = false;
                button.innerHTML = 'Run Amazon FBA Import';

                outputDiv.innerHTML = `
                    <div class="alert alert-danger">
                        <h4>Import Failed</h4>
                        <p>${error.message}</p>
                        <p>Make sure the adminServer.js is running.</p>
                    </div>
                `;
            });
        });

        // FBA Carrying Costs Refresh button
        document.getElementById('refresh-fba-carrying-costs').addEventListener('click', function() {
            const outputDiv = document.getElementById('fba-carrying-costs-result');
            const button = document.getElementById('refresh-fba-carrying-costs');

            // Disable button and show loading
            button.disabled = true;
            button.innerHTML = 'Refreshing...';
            outputDiv.innerHTML = '<div class="alert alert-info">Refreshing materialized view. This may take a few minutes...</div>';

            // Call API to refresh FBA carrying costs
            fetch('/api/refresh-fba-carrying-costs', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                }
            })
            .then(response => response.json())
            .then(data => {
                // Re-enable button
                button.disabled = false;
                button.innerHTML = 'Refresh FBA Carrying Costs';

                if (data.success) {
                    outputDiv.innerHTML = `
                        <div class="alert alert-success">
                            <h4>Refresh Successful!</h4>
                            <p>Materialized view refreshed successfully.</p>
                            <p><strong>Records processed:</strong> ${data.recordCount || 'Unknown'}</p>
                            <p><strong>Duration:</strong> ${data.duration || 'Unknown'}</p>
                            <pre class="mt-3" style="max-height: 200px; overflow-y: auto;">${data.details || ''}</pre>
                        </div>
                    `;
                } else {
                    outputDiv.innerHTML = `
                        <div class="alert alert-danger">
                            <h4>Refresh Failed</h4>
                            <p>${data.error || 'Unknown error occurred'}</p>
                            <pre class="mt-3" style="max-height: 200px; overflow-y: auto;">${data.details || ''}</pre>
                        </div>
                    `;
                }
            })
            .catch(error => {
                // Re-enable button
                button.disabled = false;
                button.innerHTML = 'Refresh FBA Carrying Costs';

                outputDiv.innerHTML = `
                    <div class="alert alert-danger">
                        <h4>Refresh Failed</h4>
                        <p>${error.message}</p>
                        <p>Make sure the adminServer.js is running.</p>
                    </div>
                `;
            });
        });

        // FBA Inventory Report Generation button
        document.getElementById('generate-fba-inventory-report').addEventListener('click', function() {
            const outputDiv = document.getElementById('fba-inventory-report-result');
            const button = document.getElementById('generate-fba-inventory-report');

            // Disable button and show loading
            button.disabled = true;
            button.innerHTML = 'Generating report...';
            outputDiv.innerHTML = '<div class="alert alert-info">Generating FBA inventory value report. This may take several minutes for large datasets...</div>';

            // Call API to generate FBA inventory report
            fetch('/api/generate-fba-inventory-report', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                }
            })
            .then(response => response.json())
            .then(data => {
                // Re-enable button
                button.disabled = false;
                button.innerHTML = 'Generate FBA Inventory Report';

                if (data.success) {
                    outputDiv.innerHTML = `
                        <div class="alert alert-success">
                            <h4>Report Generated Successfully!</h4>
                            <div class="row">
                                <div class="col-md-6">
                                    <p><strong>Report Month:</strong> ${data.reportMonth || 'Unknown'}</p>
                                    <p><strong>Records Processed:</strong> ${data.processedRecords || 'Unknown'}</p>
                                    <p><strong>Processing Time:</strong> ${data.processingTime || 'Unknown'}</p>
                                </div>
                                <div class="col-md-6">
                                    <p><strong>Total Units:</strong> ${data.totalUnits?.toLocaleString() || 'Unknown'}</p>
                                    <p><strong>Total Value:</strong> $${data.totalValue?.toLocaleString() || 'Unknown'}</p>
                                    <p><strong>Avg Unit Cost:</strong> $${data.weightedAvgCost || 'Unknown'}</p>
                                </div>
                            </div>
                            <pre class="mt-3" style="max-height: 200px; overflow-y: auto;">${data.details || ''}</pre>
                        </div>
                    `;
                } else {
                    outputDiv.innerHTML = `
                        <div class="alert alert-danger">
                            <h4>Report Generation Failed</h4>
                            <p>${data.error || 'Unknown error occurred'}</p>
                            <pre class="mt-3" style="max-height: 200px; overflow-y: auto;">${data.details || ''}</pre>
                        </div>
                    `;
                }
            })
            .catch(error => {
                // Re-enable button
                button.disabled = false;
                button.innerHTML = 'Generate FBA Inventory Report';

                outputDiv.innerHTML = `
                    <div class="alert alert-danger">
                        <h4>Report Generation Failed</h4>
                        <p>${error.message}</p>
                        <p>Make sure the adminServer.js is running.</p>
                    </div>
                `;
            });
        });

        // Historical FBA Reports Generation button
        document.getElementById('generate-historical-fba-reports').addEventListener('click', function() {
            const outputDiv = document.getElementById('historical-fba-reports-result');
            const button = document.getElementById('generate-historical-fba-reports');

            // Disable button and show loading
            button.disabled = true;
            button.innerHTML = 'Generating historical reports...';
            outputDiv.innerHTML = '<div class="alert alert-info">Generating historical FBA inventory value reports for all months. This may take several minutes for large datasets...</div>';

            // Call API to generate historical FBA reports
            fetch('/api/generate-historical-fba-reports', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                }
            })
            .then(response => response.json())
            .then(data => {
                // Re-enable button
                button.disabled = false;
                button.innerHTML = 'Generate Historical FBA Reports';

                if (data.success) {
                    outputDiv.innerHTML = `
                        <div class="alert alert-success">
                            <h4>Historical Reports Generated Successfully!</h4>
                            <div class="row">
                                <div class="col-md-6">
                                    <p><strong>Months Processed:</strong> ${data.monthsProcessed || 0}</p>
                                    <p><strong>Months Locked (updated _new columns):</strong> ${data.monthsLocked || 0}</p>
                                    <p><strong>Total Records Processed:</strong> ${data.totalRecordsProcessed?.toLocaleString() || 'Unknown'}</p>
                                </div>
                                <div class="col-md-6">
                                    <p><strong>Processing Time:</strong> ${data.processingTime || 'Unknown'}</p>
                                    <p><strong>Status:</strong> Complete</p>
                                </div>
                            </div>
                            <div class="mt-3">
                                <h5>Month-by-Month Details:</h5>
                                <pre style="max-height: 300px; overflow-y: auto; background-color: #f8f9fa; padding: 10px; border-radius: 4px;">${data.details || 'No details available'}</pre>
                            </div>
                        </div>
                    `;
                } else {
                    outputDiv.innerHTML = `
                        <div class="alert alert-danger">
                            <h4>Historical Reports Generation Failed</h4>
                            <p>${data.error || 'Unknown error occurred'}</p>
                            <pre class="mt-3" style="max-height: 200px; overflow-y: auto;">${data.details || ''}</pre>
                        </div>
                    `;
                }
            })
            .catch(error => {
                // Re-enable button
                button.disabled = false;
                button.innerHTML = 'Generate Historical FBA Reports';

                outputDiv.innerHTML = `
                    <div class="alert alert-danger">
                        <h4>Historical Reports Generation Failed</h4>
                        <p>${error.message}</p>
                        <p>Make sure the adminServer.js is running.</p>
                    </div>
                `;
            });
        });

        // Import Amazon Fulfilled Inventory Report button
        document.getElementById('import-fulfilled-inventory').addEventListener('click', function() {
            const outputDiv = document.getElementById('fulfilled-inventory-import-result');
            const button = document.getElementById('import-fulfilled-inventory');

            // Disable button and show loading
            button.disabled = true;
            button.innerHTML = 'Importing...';
            outputDiv.innerHTML = '<div class="alert alert-info">Importing Amazon Fulfilled Inventory Report. This will truncate and replace existing data...</div>';

            // Call API to import fulfilled inventory
            fetch('/api/import-fulfilled-inventory', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                }
            })
            .then(response => response.json())
            .then(data => {
                // Re-enable button
                button.disabled = false;
                button.innerHTML = 'Import Fulfilled Inventory Report';

                if (data.success) {
                    outputDiv.innerHTML = `
                        <div class="alert alert-success">
                            <h4>Import Successful!</h4>
                            <p><strong>Records Imported:</strong> ${data.importCount?.toLocaleString() || 'Unknown'}</p>
                            <p><strong>Records Skipped:</strong> ${data.skippedCount || 0}</p>
                            <p><strong>Import Type:</strong> Truncate and replace</p>
                            <div class="mt-3">
                                <h5>Details:</h5>
                                <pre style="background-color: #f8f9fa; padding: 10px; border-radius: 4px;">${data.details || 'No details available'}</pre>
                            </div>
                        </div>
                    `;
                } else {
                    outputDiv.innerHTML = `
                        <div class="alert alert-danger">
                            <h4>Import Failed</h4>
                            <p>${data.error || 'Unknown error occurred'}</p>
                            <pre class="mt-3" style="max-height: 200px; overflow-y: auto;">${data.details || ''}</pre>
                        </div>
                    `;
                }
            })
            .catch(error => {
                // Re-enable button
                button.disabled = false;
                button.innerHTML = 'Import Fulfilled Inventory Report';

                outputDiv.innerHTML = `
                    <div class="alert alert-danger">
                        <h4>Import Failed</h4>
                        <p>${error.message}</p>
                        <p>Make sure the adminServer.js is running and the file exists in the correct location.</p>
                    </div>
                `;
            });
        });

        // FBA Report Locks Management
        document.getElementById('refresh-fba-locks').addEventListener('click', function() {
            refreshFbaLocksList();
        });

        function refreshFbaLocksList() {
            const container = document.getElementById('fba-locks-table-container');
            container.innerHTML = '<div class="alert alert-info">Loading FBA reports...</div>';

            fetch('/api/get-fba-reports', {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json'
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    displayFbaReportsTable(data.reports);
                } else {
                    container.innerHTML = `
                        <div class="alert alert-danger">
                            <h4>Failed to Load Reports</h4>
                            <p>${data.error || 'Unknown error occurred'}</p>
                        </div>
                    `;
                }
            })
            .catch(error => {
                container.innerHTML = `
                    <div class="alert alert-danger">
                        <h4>Failed to Load Reports</h4>
                        <p>${error.message}</p>
                        <p>Make sure the adminServer.js is running.</p>
                    </div>
                `;
            });
        }

        function displayFbaReportsTable(reports) {
            const container = document.getElementById('fba-locks-table-container');

            if (!reports || reports.length === 0) {
                container.innerHTML = '<div class="alert alert-warning">No FBA reports found.</div>';
                return;
            }

            let tableHtml = `
                <style>
                    .fba-table {
                        border-collapse: collapse;
                    }
                    .fba-table th, .fba-table td {
                        border: 1px solid #dee2e6 !important;
                        padding: 8px !important;
                        white-space: nowrap;
                    }
                    .fba-table th {
                        white-space: normal;
                        word-wrap: break-word;
                    }
                </style>
                <div class="table-responsive">
                    <table class="table table-striped table-hover table-bordered fba-table">
                        <thead class="table-dark">
                            <tr>
                                <th style="text-align: right;">Month</th>
                                <th style="text-align: right;">Units</th>
                                <th style="text-align: right;">Avg<br>Cost</th>
                                <th style="text-align: right;">EOM<br>Value</th>
                                <th style="text-align: right;">Units<br>(New)</th>
                                <th style="text-align: right;">Avg Cost<br>(New)</th>
                                <th style="text-align: right;">EOM Value<br>(New)</th>
                                <th style="text-align: center;">🔒</th>
                                <th style="text-align: right;">Locked<br>At</th>
                                <th style="text-align: center;">Actions</th>
                            </tr>
                        </thead>
                        <tbody>
            `;

            reports.forEach(report => {
                const monthDisplay = new Date(report.month).toLocaleDateString('en-US', {
                    year: 'numeric',
                    month: 'long'
                });

                const isLocked = report.locked_at !== null;

                // Format main values
                const units = (report.total_ending_units || 0).toLocaleString();
                const avgCost = '$' + (report.avg_unit_cost || 0).toFixed(2);
                const eomValue = '$' + (report.sum_of_extended_values || 0).toLocaleString();

                // Format new values (show if they exist, otherwise show dash)
                const unitsNew = report.total_ending_units_new !== null ?
                    report.total_ending_units_new.toLocaleString() : '-';
                const avgCostNew = report.avg_unit_cost_new !== null ?
                    '$' + report.avg_unit_cost_new.toFixed(2) : '-';
                const eomValueNew = report.sum_of_extended_values_new !== null ?
                    '$' + report.sum_of_extended_values_new.toLocaleString() : '-';

                // Lock status icon only
                const lockIcon = isLocked ? '🔒' : '🔓';

                const lockedAtDisplay = isLocked ?
                    new Date(report.locked_at).toLocaleDateString('en-US', {
                        year: 'numeric',
                        month: 'short',
                        day: 'numeric'
                    }) :
                    '-';

                const actionButton = isLocked ?
                    `<button class="btn btn-sm btn-outline-warning" onclick="unlockFbaReport('${report.month}', '${monthDisplay}')">🔓 Unlock</button>` :
                    `<button class="btn btn-sm btn-outline-danger" onclick="lockFbaReport('${report.month}', '${monthDisplay}')">🔒 Lock</button>`;

                tableHtml += `
                    <tr>
                        <td style="text-align: right;"><strong>${monthDisplay}</strong></td>
                        <td style="text-align: right;">${units}</td>
                        <td style="text-align: right;">${avgCost}</td>
                        <td style="text-align: right;">${eomValue}</td>
                        <td style="text-align: right;">${unitsNew}</td>
                        <td style="text-align: right;">${avgCostNew}</td>
                        <td style="text-align: right;">${eomValueNew}</td>
                        <td style="text-align: center;">${lockIcon}</td>
                        <td style="text-align: right;">${lockedAtDisplay}</td>
                        <td style="text-align: center;">${actionButton}</td>
                    </tr>
                `;
            });

            tableHtml += `
                        </tbody>
                    </table>
                </div>
            `;

            container.innerHTML = tableHtml;
        }

        function lockFbaReport(month, monthDisplay) {
            fetch('/api/lock-fba-report', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ month: month })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    refreshFbaLocksList(); // Refresh the table to show updated lock status
                } else {
                    alert(`Failed to lock report: ${data.error}`);
                }
            })
            .catch(error => {
                alert(`Error locking report: ${error.message}`);
            });
        }

        function unlockFbaReport(month, monthDisplay) {
            if (!confirm(`⚠️ WARNING: Are you sure you want to UNLOCK the report for ${monthDisplay}?\n\nThis will allow the main columns to be overwritten during future report generations.\n\nAny locked historical data will become modifiable again.`)) {
                return;
            }

            // Double confirmation for unlock
            if (!confirm(`🚨 FINAL CONFIRMATION 🚨\n\nUnlocking ${monthDisplay} will make this historical data modifiable.\n\nAre you absolutely sure you want to proceed?`)) {
                return;
            }

            fetch('/api/unlock-fba-report', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ month: month })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert(`Successfully unlocked report for ${monthDisplay}`);
                    refreshFbaLocksList(); // Refresh the table
                } else {
                    alert(`Failed to unlock report: ${data.error}`);
                }
            })
            .catch(error => {
                alert(`Error unlocking report: ${error.message}`);
            });
        }

        // Amazon SP-API Connection Test
        document.getElementById('test-amazon-connection').addEventListener('click', function() {
            const button = this;
            const resultDiv = document.getElementById('amazon-connection-result');

            button.disabled = true;
            button.innerHTML = 'Testing Connection...';
            resultDiv.innerHTML = '<div class="alert alert-info">Testing Amazon SP-API connection...</div>';

            fetch('/api/test-amazon-connection', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                }
            })
            .then(response => response.json())
            .then(data => {
                button.disabled = false;
                button.innerHTML = 'Test Amazon Connection';

                if (data.success) {
                    resultDiv.innerHTML = `
                        <div class="alert alert-success">
                            <h4>✅ Connection Successful!</h4>
                            <p>${data.message}</p>
                            ${data.marketplaces ? `<p>Found ${data.marketplaces} marketplace(s)</p>` : ''}
                        </div>
                    `;
                } else {
                    resultDiv.innerHTML = `
                        <div class="alert alert-danger">
                            <h4>❌ Connection Failed</h4>
                            <p>${data.message || data.error}</p>
                            <details>
                                <summary>Error Details</summary>
                                <pre>${data.details || 'No additional details available'}</pre>
                            </details>
                        </div>
                    `;
                }
            })
            .catch(error => {
                button.disabled = false;
                button.innerHTML = 'Test Amazon Connection';
                resultDiv.innerHTML = `
                    <div class="alert alert-danger">
                        <h4>❌ Request Failed</h4>
                        <p>Error: ${error.message}</p>
                        <p>Make sure the adminServer.js is running.</p>
                    </div>
                `;
            });
        });

        // Amazon Listing Deletion
        document.getElementById('delete-amazon-listing').addEventListener('click', function() {
            const button = this;
            const resultDiv = document.getElementById('amazon-deletion-result');
            const sku = document.getElementById('amazon-sku').value.trim();
            const marketplace = document.getElementById('amazon-marketplace').value;
            const reason = document.getElementById('amazon-reason').value.trim();

            if (!sku) {
                resultDiv.innerHTML = '<div class="alert alert-warning">Please enter a SKU to delete.</div>';
                return;
            }

            // Confirmation dialog
            if (!confirm(`⚠️ WARNING: Are you sure you want to delete the Amazon listing for SKU "${sku}"?\n\nThis action will enqueue a deletion task and cannot be easily undone.\n\nMarketplace: ${marketplace}\nReason: ${reason || 'No reason provided'}`)) {
                return;
            }

            button.disabled = true;
            button.innerHTML = 'Enqueueing Deletion...';
            resultDiv.innerHTML = '<div class="alert alert-info">Enqueueing Amazon listing deletion task...</div>';

            const requestData = {
                sku: sku,
                marketplaceIds: [marketplace],
                reason: reason || 'Manual deletion via admin interface'
            };

            fetch('/api/delete-amazon-listing', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(requestData)
            })
            .then(response => response.json())
            .then(data => {
                button.disabled = false;
                button.innerHTML = 'Enqueue Listing Deletion';

                if (data.success) {
                    resultDiv.innerHTML = `
                        <div class="alert alert-success">
                            <h4>✅ Task Enqueued Successfully!</h4>
                            <p>${data.message}</p>
                            <p><strong>Task ID:</strong> ${data.taskId}</p>
                            <p><strong>SKU:</strong> ${data.sku}</p>
                            <p><strong>Marketplace:</strong> ${data.marketplaceIds.join(', ')}</p>
                            <p><strong>Reason:</strong> ${data.reason}</p>
                            <p><em>The task will be processed by the worker daemon. Check the Worker tab for progress.</em></p>
                        </div>
                    `;

                    // Clear the form
                    document.getElementById('amazon-sku').value = '';
                    document.getElementById('amazon-reason').value = '';
                } else {
                    resultDiv.innerHTML = `
                        <div class="alert alert-danger">
                            <h4>❌ Failed to Enqueue Task</h4>
                            <p>${data.message || data.error}</p>
                            <details>
                                <summary>Error Details</summary>
                                <pre>${data.details || 'No additional details available'}</pre>
                            </details>
                        </div>
                    `;
                }
            })
            .catch(error => {
                button.disabled = false;
                button.innerHTML = 'Enqueue Listing Deletion';
                resultDiv.innerHTML = `
                    <div class="alert alert-danger">
                        <h4>❌ Request Failed</h4>
                        <p>Error: ${error.message}</p>
                        <p>Make sure the adminServer.js is running.</p>
                    </div>
                `;
            });
        });

        // Innova tab event handlers
        document.getElementById('importInnovaDataBtn').addEventListener('click', function() {
            const outputDiv = document.getElementById('innovaOutput');
            outputDiv.style.display = 'block';
            outputDiv.innerHTML = 'Starting Innova data import...\n';

            fetch('/api/import-innova-data', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    outputDiv.innerHTML += 'Import completed successfully!\n';
                    outputDiv.innerHTML += `Total records imported: ${data.totalRecords}\n`;
                    outputDiv.innerHTML += `Batch ID: ${data.batchId}\n`;
                } else {
                    outputDiv.innerHTML += 'Import failed: ' + (data.error || 'Unknown error') + '\n';
                }
            })
            .catch(error => {
                outputDiv.innerHTML += 'Error: ' + error.message + '\n';
                outputDiv.innerHTML += 'Make sure the adminServer.js is running.\n';
            });
        });

        document.getElementById('viewInnovaDataBtn').addEventListener('click', function() {
            const outputDiv = document.getElementById('innovaOutput');
            outputDiv.style.display = 'block';
            outputDiv.innerHTML = 'Loading Innova data sample...\n';

            fetch('/api/view-innova-data')
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    outputDiv.innerHTML = 'Sample of imported Innova data:\n\n';
                    outputDiv.innerHTML += `Total records: ${data.totalCount}\n\n`;
                    if (data.sampleData && data.sampleData.length > 0) {
                        data.sampleData.forEach((record, index) => {
                            outputDiv.innerHTML += `Record ${index + 1}:\n`;
                            outputDiv.innerHTML += `  Category: ${record.category}\n`;
                            outputDiv.innerHTML += `  Description: ${record.description}\n`;
                            outputDiv.innerHTML += `  SKU: ${record.sku}\n`;
                            outputDiv.innerHTML += `  Internal ID: ${record.internal_id}\n`;
                            outputDiv.innerHTML += `  Availability: ${record.availability}\n`;
                            outputDiv.innerHTML += `  Current Price: $${record.current_price}\n`;
                            outputDiv.innerHTML += `  Matrix Type: ${record.matrix_type}\n\n`;
                        });
                    } else {
                        outputDiv.innerHTML += 'No data found.\n';
                    }
                } else {
                    outputDiv.innerHTML += 'Error: ' + (data.error || 'Unknown error') + '\n';
                }
            })
            .catch(error => {
                outputDiv.innerHTML += 'Error: ' + error.message + '\n';
                outputDiv.innerHTML += 'Make sure the adminServer.js is running.\n';
            });
        });

        // Safe validation (report only)
        document.getElementById('validateExistingMatchesBtn').addEventListener('click', function() {
            const outputDiv = document.getElementById('innovaOutput');
            outputDiv.style.display = 'block';
            outputDiv.innerHTML = '🔍 Running SAFE validation (report only, no database changes)...\n';

            fetch('/api/validate-innova-matches', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ autoClearBrokenLinks: false })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    outputDiv.innerHTML += '✅ Safe validation completed successfully!\n\n';
                    outputDiv.innerHTML += `Total Innova OSLs with vendor_internal_id: ${data.totalMatches}\n`;
                    outputDiv.innerHTML += `Valid matches: ${data.validMatches}\n`;
                    outputDiv.innerHTML += `Questionable matches: ${data.questionableMatches}\n`;
                    outputDiv.innerHTML += `Broken links found: ${data.brokenLinks}\n`;
                    outputDiv.innerHTML += `Auto-cleared: ${data.autoCleared} (NONE - safe mode)\n\n`;
                    outputDiv.innerHTML += '📊 Detailed report generated. Use "View Validation Report" to see results.\n';
                } else {
                    outputDiv.innerHTML += '❌ Validation failed: ' + (data.error || 'Unknown error') + '\n';
                }
            })
            .catch(error => {
                outputDiv.innerHTML += '❌ Error: ' + error.message + '\n';
                outputDiv.innerHTML += 'Make sure the adminServer.js is running.\n';
            });
        });

        // Enhanced matching using relational data
        document.getElementById('enhancedMatchingBtn').addEventListener('click', function() {
            const outputDiv = document.getElementById('innovaOutput');
            const tableContainer = document.getElementById('validationTableContainer');

            outputDiv.style.display = 'block';
            outputDiv.innerHTML = '🚀 Running ENHANCED matching using relational data...\n';

            fetch('/api/enhanced-innova-matching', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    outputDiv.innerHTML += '✅ Enhanced matching completed successfully!\n\n';
                    outputDiv.innerHTML += `Total Innova OSLs processed: ${data.totalProcessed}\n`;
                    outputDiv.innerHTML += `🟢 Perfect matches: ${data.perfectMatches} (${Math.round(data.perfectMatches/data.totalProcessed*100)}%)\n`;
                    outputDiv.innerHTML += `🟡 Good matches: ${data.goodMatches} (${Math.round(data.goodMatches/data.totalProcessed*100)}%)\n`;
                    outputDiv.innerHTML += `🟠 Partial matches: ${data.partialMatches} (${Math.round(data.partialMatches/data.totalProcessed*100)}%)\n`;
                    outputDiv.innerHTML += `🔴 Poor matches: ${data.poorMatches} (${Math.round(data.poorMatches/data.totalProcessed*100)}%)\n\n`;
                    outputDiv.innerHTML += '📊 Enhanced report generated. Use "View Validation Report" to see detailed results.\n';

                    // Show table with enhanced results
                    if (data.enhancedResults && data.enhancedResults.length > 0) {
                        tableContainer.style.display = 'block';
                        populateEnhancedTable(data.enhancedResults);
                    }
                } else {
                    outputDiv.innerHTML += '❌ Enhanced matching failed: ' + (data.error || 'Unknown error') + '\n';
                    tableContainer.style.display = 'none';
                }
            })
            .catch(error => {
                outputDiv.innerHTML += '❌ Error: ' + error.message + '\n';
                outputDiv.innerHTML += 'Make sure the adminServer.js is running.\n';
                tableContainer.style.display = 'none';
            });
        });

        // Validation with auto-clear (requires confirmation)
        document.getElementById('validateWithClearBtn').addEventListener('click', function() {
            const confirmed = confirm(
                '⚠️ WARNING: This will automatically clear broken Innova links!\n\n' +
                'This action will:\n' +
                '• Find OSLs with vendor_id=2 (Innova)\n' +
                '• Clear vendor_internal_id for broken links\n' +
                '• Only affect Innova records\n\n' +
                'Are you sure you want to proceed?'
            );

            if (!confirmed) {
                return;
            }

            const outputDiv = document.getElementById('innovaOutput');
            outputDiv.style.display = 'block';
            outputDiv.innerHTML = '⚠️ Running validation with AUTO-CLEAR enabled...\n';

            fetch('/api/validate-innova-matches', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ autoClearBrokenLinks: true })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    outputDiv.innerHTML += '✅ Validation with auto-clear completed!\n\n';
                    outputDiv.innerHTML += `Total Innova OSLs processed: ${data.totalMatches}\n`;
                    outputDiv.innerHTML += `Valid matches: ${data.validMatches}\n`;
                    outputDiv.innerHTML += `Questionable matches: ${data.questionableMatches}\n`;
                    outputDiv.innerHTML += `Broken links found: ${data.brokenLinks}\n`;
                    outputDiv.innerHTML += `🗑️ Auto-cleared broken links: ${data.autoCleared}\n\n`;
                    outputDiv.innerHTML += '📊 Detailed report generated. Use "View Validation Report" to see results.\n';
                } else {
                    outputDiv.innerHTML += '❌ Validation failed: ' + (data.error || 'Unknown error') + '\n';
                }
            })
            .catch(error => {
                outputDiv.innerHTML += '❌ Error: ' + error.message + '\n';
                outputDiv.innerHTML += 'Make sure the adminServer.js is running.\n';
            });
        });

        document.getElementById('viewValidationReportBtn').addEventListener('click', function() {
            const outputDiv = document.getElementById('innovaOutput');
            const tableContainer = document.getElementById('validationTableContainer');

            outputDiv.style.display = 'block';
            outputDiv.innerHTML = 'Loading validation report...\n';

            fetch('/api/enhanced-validation-report')
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    outputDiv.innerHTML = `Enhanced Validation Report: ${data.reportData.length} records loaded\n`;

                    if (data.reportData && data.reportData.length > 0) {
                        // Show table and populate it with enhanced data
                        tableContainer.style.display = 'block';
                        populateEnhancedTable(data.reportData);
                    } else {
                        outputDiv.innerHTML += 'No enhanced validation data found. Run Enhanced Matching first.\n';
                        tableContainer.style.display = 'none';
                    }
                } else {
                    outputDiv.innerHTML += 'Error: ' + (data.error || 'Unknown error') + '\n';
                    tableContainer.style.display = 'none';
                }
            })
            .catch(error => {
                outputDiv.innerHTML += 'Error: ' + error.message + '\n';
                outputDiv.innerHTML += 'Make sure the adminServer.js is running.\n';
                tableContainer.style.display = 'none';
            });
        });

        // Phase 2 matching for unmatched OSLs
        document.getElementById('phase2MatchingBtn').addEventListener('click', function() {
            const outputDiv = document.getElementById('phase2Output');
            const skipCount = parseInt(document.getElementById('phase2SkipCount').value) || 0;
            const batchSize = parseInt(document.getElementById('phase2BatchSize').value) || 300;
            const maxCandidates = parseInt(document.getElementById('phase2MaxCandidates').value) || 2;
            const minConfidence = parseInt(document.getElementById('phase2MinConfidence').value) || 60;

            outputDiv.style.display = 'block';
            outputDiv.innerHTML = `🚀 Running Phase 2 matching (skip: ${skipCount}, batch: ${batchSize}, max: ${maxCandidates} candidates, min: ${minConfidence}% confidence)...\n`;

            fetch('/api/phase2-innova-matching', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    skipCount: skipCount,
                    batchSize: batchSize,
                    maxCandidates: maxCandidates,
                    minConfidence: minConfidence
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    outputDiv.innerHTML += '✅ Phase 2 matching completed successfully!\n\n';
                    outputDiv.innerHTML += `OSLs processed: ${data.processed}\n`;
                    outputDiv.innerHTML += `Potential matches found: ${data.matches}\n`;
                    outputDiv.innerHTML += `🟢 High confidence (80%+): ${data.stats.highConfidence}\n`;
                    outputDiv.innerHTML += `🟡 Medium confidence (70-79%): ${data.stats.mediumConfidence}\n`;
                    outputDiv.innerHTML += `🟠 Low confidence (50-69%): ${data.stats.lowConfidence}\n\n`;
                    outputDiv.innerHTML += '📊 Use "View Phase 2 Report" to review and approve matches.\n';
                } else {
                    outputDiv.innerHTML += '❌ Phase 2 matching failed: ' + (data.error || 'Unknown error') + '\n';
                }
            })
            .catch(error => {
                outputDiv.innerHTML += '❌ Error: ' + error.message + '\n';
                outputDiv.innerHTML += 'Make sure the adminServer.js is running.\n';
            });
        });

        // View Phase 2 report
        document.getElementById('viewPhase2ReportBtn').addEventListener('click', function() {
            const outputDiv = document.getElementById('phase2Output');
            const tableContainer = document.getElementById('validationTableContainer');

            outputDiv.style.display = 'block';
            outputDiv.innerHTML = 'Loading Phase 2 report...\n';

            fetch('/api/phase2-validation-report')
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    outputDiv.innerHTML = `Phase 2 Report: ${data.reportData.length} potential matches loaded\n`;

                    if (data.reportData && data.reportData.length > 0) {
                        // Show table and populate it with Phase 2 data
                        tableContainer.style.display = 'block';
                        populatePhase2Table(data.reportData);
                    } else {
                        outputDiv.innerHTML += 'No Phase 2 data found. Run Phase 2 Matching first.\n';
                        tableContainer.style.display = 'none';
                    }
                } else {
                    outputDiv.innerHTML += 'Error: ' + (data.error || 'Unknown error') + '\n';
                    tableContainer.style.display = 'none';
                }
            })
            .catch(error => {
                outputDiv.innerHTML += 'Error: ' + error.message + '\n';
                outputDiv.innerHTML += 'Make sure the adminServer.js is running.\n';
                tableContainer.style.display = 'none';
            });
        });

        // Validation table functionality
        let validationData = [];
        let filteredData = [];
        let sortColumn = '';
        let sortDirection = 'asc';
        let isEnhancedData = false; // Track whether we're using enhanced or basic data

        function populateValidationTable(data) {
            validationData = data;
            filteredData = [...data];
            isEnhancedData = false;
            setBasicTableHeaders();
            renderTable();
            setupTableEventListeners();

            // Hide Phase 2 buttons, show regular buttons
            document.getElementById('phase2BulkConnectBtn').style.display = 'none';
            document.getElementById('bulkRejectMatchBtn').style.display = 'none';
            document.getElementById('bulkMarkInactiveBtn').style.display = 'none';
            document.getElementById('confirmSelectedBtn').style.display = 'inline-block';
            document.getElementById('rejectSelectedBtn').style.display = 'inline-block';
        }

        function populateEnhancedTable(data) {
            validationData = data;
            filteredData = [...data];
            isEnhancedData = true;
            setEnhancedTableHeaders();
            renderEnhancedTable();
            setupTableEventListeners();

            // Hide Phase 2 buttons, show regular buttons
            document.getElementById('phase2BulkConnectBtn').style.display = 'none';
            document.getElementById('bulkRejectMatchBtn').style.display = 'none';
            document.getElementById('bulkMarkInactiveBtn').style.display = 'none';
            document.getElementById('confirmSelectedBtn').style.display = 'inline-block';
            document.getElementById('rejectSelectedBtn').style.display = 'inline-block';
        }

        function populatePhase2Table(data) {
            validationData = data;
            filteredData = [...data];
            isEnhancedData = true; // Use enhanced table format for Phase 2
            setPhase2TableHeaders();
            renderPhase2Table();
            setupTableEventListeners();

            // Show Phase 2 specific buttons
            document.getElementById('phase2BulkConnectBtn').style.display = 'inline-block';
            document.getElementById('bulkRejectMatchBtn').style.display = 'inline-block';
            document.getElementById('bulkMarkInactiveBtn').style.display = 'inline-block';
            document.getElementById('confirmSelectedBtn').style.display = 'none';
            document.getElementById('rejectSelectedBtn').style.display = 'none';
        }

        function setBasicTableHeaders() {
            document.getElementById('tableHeaders').innerHTML = `
                <th style="border: 1px solid #ddd; padding: 8px;">
                    <input type="checkbox" id="selectAllCheckbox" title="Select/Deselect All">
                </th>
                <th style="border: 1px solid #ddd; padding: 8px; cursor: pointer;" data-sort="osl_id">OSL ID ↕</th>
                <th style="border: 1px solid #ddd; padding: 8px; cursor: pointer;" data-sort="g_code">G-Code ↕</th>
                <th style="border: 1px solid #ddd; padding: 8px; cursor: pointer;" data-sort="innova_description">Innova Description ↕</th>
                <th style="border: 1px solid #ddd; padding: 8px; cursor: pointer;" data-sort="weight_match">Weight Match ↕</th>
                <th style="border: 1px solid #ddd; padding: 8px; cursor: pointer;" data-sort="string_similarity">Similarity % ↕</th>
                <th style="border: 1px solid #ddd; padding: 8px; cursor: pointer;" data-sort="status">Status ↕</th>
                <th style="border: 1px solid #ddd; padding: 8px;">Actions</th>
            `;
        }

        function setEnhancedTableHeaders() {
            document.getElementById('tableHeaders').innerHTML = `
                <th style="border: 1px solid #ddd; padding: 8px;">
                    <input type="checkbox" id="selectAllCheckbox" title="Select/Deselect All">
                </th>
                <th style="border: 1px solid #ddd; padding: 8px; cursor: pointer;" data-sort="osl_id">OSL ID ↕</th>
                <th style="border: 1px solid #ddd; padding: 8px; cursor: pointer;" data-sort="mps_id">MPS ID ↕</th>
                <th style="border: 1px solid #ddd; padding: 8px; cursor: pointer;" data-sort="osl_mold">OSL Mold ↕</th>
                <th style="border: 1px solid #ddd; padding: 8px; cursor: pointer;" data-sort="osl_plastic">OSL Plastic ↕</th>
                <th style="border: 1px solid #ddd; padding: 8px; cursor: pointer;" data-sort="osl_stamp">OSL Stamp ↕</th>
                <th style="border: 1px solid #ddd; padding: 8px; cursor: pointer;" data-sort="osl_weights">OSL Weights ↕</th>
                <th style="border: 1px solid #ddd; padding: 8px; cursor: pointer;" data-sort="innova_description">Innova Description ↕</th>
                <th style="border: 1px solid #ddd; padding: 8px; cursor: pointer;" data-sort="innova_weights">Innova Weights ↕</th>
                <th style="border: 1px solid #ddd; padding: 8px; cursor: pointer;" data-sort="mold_match">Mold ↕</th>
                <th style="border: 1px solid #ddd; padding: 8px; cursor: pointer;" data-sort="plastic_match">Plastic ↕</th>
                <th style="border: 1px solid #ddd; padding: 8px; cursor: pointer;" data-sort="weight_match">Weight ↕</th>
                <th style="border: 1px solid #ddd; padding: 8px; cursor: pointer;" data-sort="confidence_score">Score ↕</th>
                <th style="border: 1px solid #ddd; padding: 8px; cursor: pointer;" data-sort="status">Status ↕</th>
                <th style="border: 1px solid #ddd; padding: 8px;">Actions</th>
            `;
        }

        function setPhase2TableHeaders() {
            document.getElementById('tableHeaders').innerHTML = `
                <th style="border: 1px solid #ddd; padding: 4px; font-size: 11px;">
                    <input type="checkbox" id="selectAllCheckbox" title="Select/Deselect All">
                </th>
                <th style="border: 1px solid #ddd; padding: 4px; cursor: pointer; font-size: 11px;" data-sort="osl_id">OSL ID ↕</th>
                <th style="border: 1px solid #ddd; padding: 4px; cursor: pointer; font-size: 11px;" data-sort="candidate_rank">Rank ↕</th>
                <th style="border: 1px solid #ddd; padding: 4px; cursor: pointer; font-size: 11px;" data-sort="mps_id">MPS ID ↕</th>
                <th style="border: 1px solid #ddd; padding: 4px; cursor: pointer; font-size: 11px;" data-sort="osl_mold">OSL Mold ↕</th>
                <th style="border: 1px solid #ddd; padding: 4px; cursor: pointer; font-size: 11px;" data-sort="osl_plastic">OSL Plastic ↕</th>
                <th style="border: 1px solid #ddd; padding: 4px; cursor: pointer; font-size: 11px;" data-sort="osl_stamp">OSL Stamp ↕</th>
                <th style="border: 1px solid #ddd; padding: 4px; cursor: pointer; font-size: 11px;" data-sort="osl_weights">OSL Weights ↕</th>
                <th style="border: 1px solid #ddd; padding: 4px; cursor: pointer; font-size: 11px;" data-sort="innova_description">Innova Description ↕</th>
                <th style="border: 1px solid #ddd; padding: 4px; cursor: pointer; font-size: 11px;" data-sort="innova_parsed_mold">Parsed Mold ↕</th>
                <th style="border: 1px solid #ddd; padding: 4px; cursor: pointer; font-size: 11px;" data-sort="innova_parsed_plastic">Parsed Plastic ↕</th>
                <th style="border: 1px solid #ddd; padding: 4px; cursor: pointer; font-size: 11px;" data-sort="innova_weights">Innova Weights ↕</th>
                <th style="border: 1px solid #ddd; padding: 4px; cursor: pointer; font-size: 11px;" data-sort="mold_match">Mold ↕</th>
                <th style="border: 1px solid #ddd; padding: 4px; cursor: pointer; font-size: 11px;" data-sort="plastic_match">Plastic ↕</th>
                <th style="border: 1px solid #ddd; padding: 4px; cursor: pointer; font-size: 11px;" data-sort="weight_match">Weight ↕</th>
                <th style="border: 1px solid #ddd; padding: 4px; cursor: pointer; font-size: 11px;" data-sort="confidence_score">Score ↕</th>
                <th style="border: 1px solid #ddd; padding: 4px; cursor: pointer; font-size: 11px;" data-sort="status">Status ↕</th>
                <th style="border: 1px solid #ddd; padding: 4px; font-size: 11px;">Actions</th>
            `;
        }

        function renderTable() {
            const tbody = document.getElementById('validationTableBody');
            tbody.innerHTML = '';

            filteredData.forEach(record => {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td style="border: 1px solid #ddd; padding: 4px; text-align: center;">
                        <input type="checkbox" class="row-checkbox" data-osl-id="${record.osl_id}">
                    </td>
                    <td style="border: 1px solid #ddd; padding: 4px; text-align: right;">
                        ${record.osl_id}
                    </td>
                    <td style="border: 1px solid #ddd; padding: 4px; max-width: 200px; overflow: hidden; text-overflow: ellipsis;" title="${record.g_code || 'N/A'}">${record.g_code || 'N/A'}</td>
                    <td style="border: 1px solid #ddd; padding: 4px; max-width: 200px; overflow: hidden; text-overflow: ellipsis;" title="${record.innova_description || 'NOT FOUND'}">${record.innova_description || 'NOT FOUND'}</td>
                    <td style="border: 1px solid #ddd; padding: 4px; text-align: center;">
                        <span style="padding: 2px 6px; border-radius: 3px; font-size: 11px; ${getWeightMatchStyle(record.weight_match)}" title="${getWeightMatchTooltip(record.weight_match)}">${getWeightMatchText(record.weight_match)}</span>
                    </td>
                    <td style="border: 1px solid #ddd; padding: 4px; text-align: right;">${record.string_similarity}%</td>
                    <td style="border: 1px solid #ddd; padding: 4px; text-align: center;">${record.status}</td>
                    <td style="border: 1px solid #ddd; padding: 4px; text-align: center;">
                        ${record.status !== '🔴 BROKEN_LINK' ? `
                            <button onclick="confirmMatch(${record.osl_id})" style="background: #28a745; color: white; border: none; padding: 2px 6px; margin: 1px; border-radius: 3px; font-size: 11px;">✅</button>
                            <button onclick="rejectMatch(${record.osl_id})" style="background: #dc3545; color: white; border: none; padding: 2px 6px; margin: 1px; border-radius: 3px; font-size: 11px;">❌</button>
                            <button onclick="showDetails(${record.osl_id})" style="background: #17a2b8; color: white; border: none; padding: 2px 6px; margin: 1px; border-radius: 3px; font-size: 11px;">🔍</button>
                        ` : `
                            <button onclick="clearBrokenLink(${record.osl_id})" style="background: #6c757d; color: white; border: none; padding: 2px 6px; margin: 1px; border-radius: 3px; font-size: 11px;">🗑️ Clear</button>
                        `}
                    </td>
                `;
                tbody.appendChild(row);
            });

            updateSelectedCount();
            updateSelectAllState();
        }

        function renderEnhancedTable() {
            const tbody = document.getElementById('validationTableBody');
            tbody.innerHTML = '';

            filteredData.forEach(record => {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td style="border: 1px solid #ddd; padding: 4px; text-align: center;">
                        <input type="checkbox" class="row-checkbox" data-osl-id="${record.osl_id}">
                    </td>
                    <td style="border: 1px solid #ddd; padding: 4px; text-align: right;">
                        ${record.osl_id}
                    </td>
                    <td style="border: 1px solid #ddd; padding: 4px; text-align: right; font-weight: bold;" title="Your MPS ID">${record.mps_id || 'N/A'}</td>
                    <td style="border: 1px solid #ddd; padding: 4px;" title="${record.osl_mold || 'N/A'}">${record.osl_mold || 'N/A'}</td>
                    <td style="border: 1px solid #ddd; padding: 4px;" title="${record.osl_plastic || 'N/A'}">${record.osl_plastic || 'N/A'}</td>
                    <td style="border: 1px solid #ddd; padding: 4px; max-width: 150px; overflow: hidden; text-overflow: ellipsis;" title="${record.osl_stamp || 'N/A'}">${record.osl_stamp || 'N/A'}</td>
                    <td style="border: 1px solid #ddd; padding: 4px; text-align: center; font-weight: bold;" title="Your weight range">${record.osl_weights || 'N/A'}</td>
                    <td style="border: 1px solid #ddd; padding: 4px; max-width: 200px; overflow: hidden; text-overflow: ellipsis;" title="${record.innova_description || 'NOT FOUND'}">${record.innova_description || 'NOT FOUND'}</td>
                    <td style="border: 1px solid #ddd; padding: 4px; text-align: center; font-weight: bold;" title="Innova weight range">${record.innova_weights || 'N/A'}</td>
                    <td style="border: 1px solid #ddd; padding: 4px; text-align: center;">
                        <span style="padding: 2px 6px; border-radius: 3px; font-size: 11px; ${record.mold_match ? 'background-color: #28a745; color: white;' : 'background-color: #dc3545; color: white;'}">${record.mold_match ? '✓' : '✗'}</span>
                    </td>
                    <td style="border: 1px solid #ddd; padding: 4px; text-align: center;">
                        <span style="padding: 2px 6px; border-radius: 3px; font-size: 11px; ${record.plastic_match ? 'background-color: #28a745; color: white;' : 'background-color: #dc3545; color: white;'}">${record.plastic_match ? '✓' : '✗'}</span>
                    </td>
                    <td style="border: 1px solid #ddd; padding: 4px; text-align: center;">
                        <span style="padding: 2px 6px; border-radius: 3px; font-size: 11px; ${getWeightMatchStyle(record.weight_match)}" title="${getWeightMatchTooltip(record.weight_match)}">${getWeightMatchText(record.weight_match)}</span>
                    </td>
                    <td style="border: 1px solid #ddd; padding: 4px; text-align: right; font-weight: bold;">${record.confidence_score}%</td>
                    <td style="border: 1px solid #ddd; padding: 4px; text-align: center;">${record.status}</td>
                    <td style="border: 1px solid #ddd; padding: 4px; text-align: center;">
                        ${record.status !== '🔴 BROKEN_LINK' ? `
                            <button onclick="confirmMatch(${record.osl_id})" title="Confirm this match as correct" style="background: #28a745; color: white; border: none; padding: 2px 6px; margin: 1px; border-radius: 3px; font-size: 11px; cursor: pointer;">✅</button>
                            <button onclick="rejectMatch(${record.osl_id})" title="Reject this match and clear connection" style="background: #dc3545; color: white; border: none; padding: 2px 6px; margin: 1px; border-radius: 3px; font-size: 11px; cursor: pointer;">❌</button>
                            <button onclick="showEnhancedDetails(${record.osl_id})" title="View detailed comparison" style="background: #17a2b8; color: white; border: none; padding: 2px 6px; margin: 1px; border-radius: 3px; font-size: 11px; cursor: pointer;">🔍</button>
                        ` : `
                            <button onclick="clearBrokenLink(${record.osl_id})" title="Clear this broken connection" style="background: #6c757d; color: white; border: none; padding: 2px 6px; margin: 1px; border-radius: 3px; font-size: 11px; cursor: pointer;">🗑️</button>
                        `}
                    </td>
                `;
                tbody.appendChild(row);
            });

            updateSelectedCount();
            updateSelectAllState();
        }

        function renderPhase2Table() {
            const tbody = document.getElementById('validationTableBody');
            tbody.innerHTML = '';

            filteredData.forEach(record => {
                const row = document.createElement('tr');

                // Add rank indicator styling
                const rankStyle = record.candidate_rank === 1 ? 'font-weight: bold; color: #28a745;' :
                                 record.candidate_rank === 2 ? 'color: #ffc107;' : 'color: #6c757d;';

                row.innerHTML = `
                    <td style="border: 1px solid #ddd; padding: 2px; text-align: center; font-size: 11px;">
                        <input type="checkbox" class="row-checkbox" data-osl-id="${record.osl_id}" data-innova-id="${record.innova_internal_id}">
                    </td>
                    <td style="border: 1px solid #ddd; padding: 2px; text-align: right; font-size: 11px;">
                        ${record.osl_id}
                    </td>
                    <td style="border: 1px solid #ddd; padding: 2px; text-align: center; ${rankStyle} font-size: 11px;" title="Candidate rank for this OSL">
                        #${record.candidate_rank}
                    </td>
                    <td style="border: 1px solid #ddd; padding: 2px; text-align: right; font-weight: bold; font-size: 11px;" title="Your MPS ID">${record.mps_id || 'N/A'}</td>
                    <td style="border: 1px solid #ddd; padding: 2px; font-size: 11px;" title="${record.osl_mold || 'N/A'}">${record.osl_mold || 'N/A'}</td>
                    <td style="border: 1px solid #ddd; padding: 2px; font-size: 11px;" title="${record.osl_plastic || 'N/A'}">${record.osl_plastic || 'N/A'}</td>
                    <td style="border: 1px solid #ddd; padding: 2px; font-size: 11px;" title="${record.osl_stamp || 'N/A'}">${record.osl_stamp || 'N/A'}</td>
                    <td style="border: 1px solid #ddd; padding: 2px; text-align: center; font-weight: bold; font-size: 11px;" title="Your weight range">${record.osl_weights || 'N/A'}</td>
                    <td style="border: 1px solid #ddd; padding: 2px; max-width: 150px; overflow: hidden; text-overflow: ellipsis; font-size: 11px;" title="${record.innova_description || 'NOT FOUND'}">${record.innova_description || 'NOT FOUND'}</td>
                    <td style="border: 1px solid #ddd; padding: 2px; font-size: 11px; ${record.mold_match ? 'background-color: #d4edda;' : 'background-color: #f8d7da;'}" title="Parsed: ${record.innova_parsed_mold} → Normalized: ${record.innova_mold_normalized}">${record.innova_parsed_mold || 'PARSE_FAILED'}</td>
                    <td style="border: 1px solid #ddd; padding: 2px; font-size: 11px; ${record.plastic_match ? 'background-color: #d4edda;' : 'background-color: #f8d7da;'}" title="Parsed: ${record.innova_parsed_plastic} → Normalized: ${record.innova_plastic_normalized}">${record.innova_parsed_plastic || 'PARSE_FAILED'}</td>
                    <td style="border: 1px solid #ddd; padding: 2px; text-align: center; font-weight: bold; font-size: 11px;" title="Innova weight range">${record.innova_weights || 'N/A'}</td>
                    <td style="border: 1px solid #ddd; padding: 2px; text-align: center; font-size: 11px;">
                        <span style="padding: 1px 4px; border-radius: 3px; font-size: 10px; ${record.mold_match ? 'background-color: #28a745; color: white;' : 'background-color: #dc3545; color: white;'}">${record.mold_match ? '✓' : '✗'}</span>
                    </td>
                    <td style="border: 1px solid #ddd; padding: 2px; text-align: center; font-size: 11px;">
                        <span style="padding: 1px 4px; border-radius: 3px; font-size: 10px; ${record.plastic_match ? 'background-color: #28a745; color: white;' : 'background-color: #dc3545; color: white;'}">${record.plastic_match ? '✓' : '✗'}</span>
                    </td>
                    <td style="border: 1px solid #ddd; padding: 2px; text-align: center; font-size: 11px;">
                        <span style="padding: 1px 4px; border-radius: 3px; font-size: 10px; ${getWeightMatchStyle(record.weight_match)}" title="${getWeightMatchTooltip(record.weight_match)}">${getWeightMatchText(record.weight_match)}</span>
                    </td>
                    <td style="border: 1px solid #ddd; padding: 2px; text-align: right; font-weight: bold; ${getConfidenceStyle(record.confidence_score)} font-size: 11px;">${record.confidence_score}%</td>
                    <td style="border: 1px solid #ddd; padding: 2px; text-align: center; font-size: 10px;">${record.status || 'N/A'}</td>
                    <td style="border: 1px solid #ddd; padding: 2px; text-align: center; font-size: 11px;">
                        <button onclick="confirmPhase2Match(${record.osl_id}, ${record.innova_internal_id})" title="Create connection to this Innova product" style="background: #28a745; color: white; border: none; padding: 1px 4px; margin: 1px; border-radius: 2px; font-size: 10px; cursor: pointer;">✅</button>
                        <button onclick="rejectPhase2Match(${record.osl_id}, ${record.innova_internal_id})" title="Mark as not a match" style="background: #dc3545; color: white; border: none; padding: 1px 4px; margin: 1px; border-radius: 2px; font-size: 10px; cursor: pointer;">❌</button>
                        <button onclick="updateWeightsAndConnect(${record.osl_id}, ${record.innova_internal_id}, '${record.innova_weights}', '${record.osl_weights}')" title="Update OSL weights to match Innova and create connection" style="background: #fd7e14; color: white; border: none; padding: 1px 4px; margin: 1px; border-radius: 2px; font-size: 10px; cursor: pointer;">⚖️</button>
                        <button onclick="clearFromPhase2(${record.osl_id})" title="Mark OSL as verified (no match needed) - removes from Phase 2" style="background: #20c997; color: white; border: none; padding: 1px 4px; margin: 1px; border-radius: 2px; font-size: 10px; cursor: pointer;">🗑️</button>
                        <button onclick="markMPSInactive(${record.mps_id})" title="Mark this MPS as inactive (no longer available)" style="background: #6c757d; color: white; border: none; padding: 1px 4px; margin: 1px; border-radius: 2px; font-size: 10px; cursor: pointer;">🚫</button>
                        <button onclick="showPhase2Details(${record.osl_id}, '${record.innova_description.replace(/'/g, "\\'")}', ${record.confidence_score})" title="View detailed comparison" style="background: #17a2b8; color: white; border: none; padding: 1px 4px; margin: 1px; border-radius: 2px; font-size: 10px; cursor: pointer;">🔍</button>
                    </td>
                `;
                tbody.appendChild(row);
            });

            updateSelectedCount();
            updateSelectAllState();
        }

        function getConfidenceStyle(score) {
            if (score >= 80) return 'color: #28a745;'; // Green for high confidence
            if (score >= 70) return 'color: #ffc107;'; // Yellow for medium confidence
            return 'color: #dc3545;'; // Red for low confidence
        }

        function getWeightMatchStyle(weightMatch) {
            switch(weightMatch) {
                case 'EXACT_MATCH': return 'background-color: #28a745; color: white;';
                case 'OVERLAP': return 'background-color: #ffc107; color: black;';
                case 'NO_OVERLAP': return 'background-color: #dc3545; color: white;';
                case 'UNPARSEABLE': return 'background-color: #6c757d; color: white;';
                case 'BROKEN_LINK': return 'background-color: #6c757d; color: white;';
                default: return 'background-color: #e9ecef; color: black;';
            }
        }

        function getWeightMatchText(weightMatch) {
            switch(weightMatch) {
                case 'EXACT_MATCH': return 'EXACT';
                case 'OVERLAP': return 'OVERLAP';
                case 'NO_OVERLAP': return 'NO MATCH';
                case 'UNPARSEABLE': return 'UNPARSEABLE';
                case 'BROKEN_LINK': return 'BROKEN';
                default: return weightMatch;
            }
        }

        function getWeightMatchTooltip(weightMatch) {
            switch(weightMatch) {
                case 'EXACT_MATCH': return 'Weight ranges match exactly';
                case 'OVERLAP': return 'Weight ranges overlap but are not identical';
                case 'NO_OVERLAP': return 'Weight ranges do not overlap at all';
                case 'UNPARSEABLE': return 'Innova weight data cannot be parsed (e.g., "Max Weight")';
                case 'BROKEN_LINK': return 'Innova record not found';
                default: return weightMatch;
            }
        }

        function setupTableEventListeners() {
            // Sorting
            document.querySelectorAll('th[data-sort]').forEach(th => {
                th.addEventListener('click', () => {
                    const column = th.getAttribute('data-sort');
                    if (sortColumn === column) {
                        sortDirection = sortDirection === 'asc' ? 'desc' : 'asc';
                    } else {
                        sortColumn = column;
                        sortDirection = 'asc';
                    }
                    sortTable();
                });
            });

            // Select All checkbox
            const selectAllCheckbox = document.getElementById('selectAllCheckbox');
            if (selectAllCheckbox) {
                selectAllCheckbox.addEventListener('change', function() {
                    const isChecked = this.checked;
                    document.querySelectorAll('.row-checkbox').forEach(checkbox => {
                        checkbox.checked = isChecked;
                    });
                    updateSelectedCount();
                });
            }

            // Individual checkbox selection
            document.addEventListener('change', function(e) {
                if (e.target.classList.contains('row-checkbox')) {
                    updateSelectedCount();
                    updateSelectAllState();
                }
            });

            // Filters
            document.getElementById('applyFilters').addEventListener('click', applyFilters);
            document.getElementById('clearFilters').addEventListener('click', clearFilters);

            // Bulk actions
            document.getElementById('confirmSelectedBtn').addEventListener('click', confirmSelected);
            document.getElementById('rejectSelectedBtn').addEventListener('click', rejectSelected);
            document.getElementById('phase2BulkConnectBtn').addEventListener('click', phase2BulkConnect);
            document.getElementById('bulkMarkInactiveBtn').addEventListener('click', bulkMarkMPSInactive);
            document.getElementById('bulkRejectMatchBtn').addEventListener('click', bulkRejectMatches);

            // Quick select buttons
            document.getElementById('selectPerfectBtn').addEventListener('click', () => selectByStatus('🟢 PERFECT_MATCH'));
            document.getElementById('selectGoodBtn').addEventListener('click', () => selectByStatus('🟡 GOOD_MATCH'));
            document.getElementById('selectHighConfidenceBtn').addEventListener('click', () => selectByConfidence(70));
            document.getElementById('selectNoneBtn').addEventListener('click', selectNone);
        }

        function sortTable() {
            filteredData.sort((a, b) => {
                let aVal = a[sortColumn];
                let bVal = b[sortColumn];

                if (typeof aVal === 'string') {
                    aVal = aVal.toLowerCase();
                    bVal = bVal.toLowerCase();
                }

                if (sortDirection === 'asc') {
                    return aVal > bVal ? 1 : -1;
                } else {
                    return aVal < bVal ? 1 : -1;
                }
            });

            // Call the appropriate render function based on data type
            if (isEnhancedData) {
                // Check if this is Phase 2 data (has candidate_rank)
                if (validationData.length > 0 && validationData[0].candidate_rank !== undefined) {
                    renderPhase2Table();
                } else {
                    renderEnhancedTable();
                }
            } else {
                renderTable();
            }
        }

        function applyFilters() {
            const statusFilter = document.getElementById('statusFilter').value;
            const similarityFilter = parseInt(document.getElementById('similarityFilter').value) || 0;
            const confidenceFilter = document.getElementById('confidenceFilter')?.value || '';
            const stampFilter = document.getElementById('stampFilter')?.value || '';

            console.log('Applying filters:', { statusFilter, similarityFilter, confidenceFilter, stampFilter });
            console.log('Total records before filter:', validationData.length);

            filteredData = validationData.filter(record => {
                const statusMatch = !statusFilter || record.status === statusFilter;
                // Handle both enhanced matching (confidence_score) and basic validation (string_similarity)
                const scoreField = record.confidence_score !== undefined ? record.confidence_score : record.string_similarity;
                const similarityMatch = scoreField >= similarityFilter;

                // Confidence score filter (for Phase 2)
                let confidenceMatch = true;
                if (confidenceFilter && record.confidence_score !== undefined) {
                    const score = record.confidence_score;
                    if (confidenceFilter === '100' && score !== 100) {
                        confidenceMatch = false;
                    } else if (confidenceFilter === '90+' && score < 90) {
                        confidenceMatch = false;
                    } else if (confidenceFilter === '80+' && score < 80) {
                        confidenceMatch = false;
                    } else if (confidenceFilter === '70+' && score < 70) {
                        confidenceMatch = false;
                    }
                }

                // OSL Stamp filter (for Phase 2)
                const stampMatch = !stampFilter || record.osl_stamp === stampFilter;

                // Debug first few records
                if (validationData.indexOf(record) < 3) {
                    console.log(`Record ${record.osl_id}: status="${record.status}", score=${scoreField}, statusMatch=${statusMatch}, similarityMatch=${similarityMatch}`);
                }

                return statusMatch && similarityMatch && confidenceMatch && stampMatch;
            });

            console.log('Records after filter:', filteredData.length);

            // Call the appropriate render function based on data type
            if (isEnhancedData) {
                // Check if this is Phase 2 data (has candidate_rank)
                if (validationData.length > 0 && validationData[0].candidate_rank !== undefined) {
                    renderPhase2Table();
                } else {
                    renderEnhancedTable();
                }
            } else {
                renderTable();
            }
        }

        function clearFilters() {
            document.getElementById('statusFilter').value = '';
            document.getElementById('similarityFilter').value = '';
            filteredData = [...validationData];

            // Call the appropriate render function based on data type
            if (isEnhancedData) {
                // Check if this is Phase 2 data (has candidate_rank)
                if (validationData.length > 0 && validationData[0].candidate_rank !== undefined) {
                    renderPhase2Table();
                } else {
                    renderEnhancedTable();
                }
            } else {
                renderTable();
            }
        }

        function updateSelectedCount() {
            const selected = document.querySelectorAll('.row-checkbox:checked').length;
            const total = document.querySelectorAll('.row-checkbox').length;
            document.getElementById('selectedCount').textContent = `${selected} of ${total} selected`;
        }

        function updateSelectAllState() {
            const selectAllCheckbox = document.getElementById('selectAllCheckbox');
            if (!selectAllCheckbox) return;

            const allCheckboxes = document.querySelectorAll('.row-checkbox');
            const checkedCheckboxes = document.querySelectorAll('.row-checkbox:checked');

            if (checkedCheckboxes.length === 0) {
                // None selected
                selectAllCheckbox.checked = false;
                selectAllCheckbox.indeterminate = false;
            } else if (checkedCheckboxes.length === allCheckboxes.length) {
                // All selected
                selectAllCheckbox.checked = true;
                selectAllCheckbox.indeterminate = false;
            } else {
                // Some selected
                selectAllCheckbox.checked = false;
                selectAllCheckbox.indeterminate = true;
            }
        }

        // Quick select functions
        function selectByStatus(status) {
            document.querySelectorAll('.row-checkbox').forEach(checkbox => {
                const oslId = parseInt(checkbox.dataset.oslId);
                const record = filteredData.find(r => r.osl_id === oslId);
                checkbox.checked = record && record.status === status;
            });
            updateSelectedCount();
            updateSelectAllState();
        }

        function selectByConfidence(minScore) {
            document.querySelectorAll('.row-checkbox').forEach(checkbox => {
                const oslId = parseInt(checkbox.dataset.oslId);
                const record = filteredData.find(r => r.osl_id === oslId);
                if (record) {
                    const score = record.confidence_score !== undefined ? record.confidence_score : record.string_similarity;
                    checkbox.checked = score >= minScore;
                }
            });
            updateSelectedCount();
            updateSelectAllState();
        }

        function selectNone() {
            document.querySelectorAll('.row-checkbox').forEach(checkbox => {
                checkbox.checked = false;
            });
            updateSelectedCount();
            updateSelectAllState();
        }

        // Action functions
        function confirmMatch(oslId) {
            updateMatchVerification(oslId, true);
        }

        function rejectMatch(oslId) {
            updateMatchVerification(oslId, false);
        }

        function clearBrokenLink(oslId) {
            updateMatchVerification(oslId, false);
        }

        function showDetails(oslId) {
            const record = validationData.find(r => r.osl_id === oslId);
            if (record) {
                alert(`OSL ${oslId} Details:\n\n` +
                      `G-Code: ${record.g_code}\n` +
                      `Innova Description: ${record.innova_description}\n` +
                      `Weight Match: ${record.weight_match}\n` +
                      `OSL Weights: ${record.min_weight}-${record.max_weight}\n` +
                      `Innova Weights: ${record.innova_weights}\n` +
                      `String Similarity: ${record.string_similarity}%\n` +
                      `Status: ${record.status}`);
            }
        }

        function showEnhancedDetails(oslId) {
            const record = validationData.find(r => r.osl_id === oslId);
            if (record) {
                const moldIcon = record.mold_match ? '✅' : '❌';
                const plasticIcon = record.plastic_match ? '✅' : '❌';
                const weightIcon = record.weight_match === 'EXACT_MATCH' ? '✅' : record.weight_match === 'OVERLAP' ? '⚠️' : '❌';

                alert(`OSL ${oslId} Enhanced Details:\n\n` +
                      `OSL Data:\n` +
                      `  MPS ID: ${record.mps_id || 'N/A'}\n` +
                      `  Mold: ${record.osl_mold}${record.osl_mold_normalized ? ` → ${record.osl_mold_normalized}` : ''}\n` +
                      `  Plastic: ${record.osl_plastic}${record.osl_plastic_normalized ? ` → ${record.osl_plastic_normalized}` : ''}\n` +
                      `  Stamp: ${record.osl_stamp}\n` +
                      `  Weights: ${record.osl_weights}\n` +
                      `  Color: ${record.osl_color}\n\n` +
                      `Innova Data:\n` +
                      `  Description: ${record.innova_description}\n` +
                      `  Parsed Mold: ${record.innova_parsed?.mold || 'N/A'}${record.innova_mold_normalized ? ` → ${record.innova_mold_normalized}` : ''}\n` +
                      `  Parsed Plastic: ${record.innova_parsed?.plastic || 'N/A'}${record.innova_plastic_normalized ? ` → ${record.innova_plastic_normalized}` : ''}\n` +
                      `  Weights: ${record.innova_weights}\n\n` +
                      `Matching Results:\n` +
                      `  ${moldIcon} Mold Match: ${record.mold_match}\n` +
                      `  ${plasticIcon} Plastic Match: ${record.plastic_match}\n` +
                      `  ${weightIcon} Weight Match: ${record.weight_match}\n` +
                      `    OSL: ${record.osl_weights} | Innova: ${record.innova_weights}\n` +
                      `  Confidence Score: ${record.confidence_score}%\n` +
                      `  Status: ${record.status}\n` +
                      `  Recommendation: ${record.recommendation}`);
            }
        }

        // Phase 2 specific functions
        function confirmPhase2Match(oslId, innovaInternalId) {
            // No confirmation needed - direct action
                fetch('/api/create-vendor-connection', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        oslId: oslId,
                        vendorInternalId: innovaInternalId
                    })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // Remove all rows for this OSL (all candidates)
                        const rows = document.querySelectorAll(`input[data-osl-id="${oslId}"]`);
                        rows.forEach(checkbox => {
                            const row = checkbox.closest('tr');
                            row.style.backgroundColor = '#d4edda';
                            row.style.opacity = '0.7';
                        });

                        setTimeout(() => {
                            rows.forEach(checkbox => checkbox.closest('tr').remove());
                            // Update the data arrays
                            validationData = validationData.filter(r => r.osl_id !== oslId);
                            filteredData = filteredData.filter(r => r.osl_id !== oslId);
                            updateSelectedCount();
                        }, 1000);

                        // Connection successful - visual feedback only
                    } else {
                        alert('Error creating connection: ' + (data.error || 'Unknown error'));
                    }
                })
                .catch(error => {
                    alert('Error: ' + error.message);
                });
        }

        function rejectPhase2Match(oslId, innovaInternalId) {
            // No confirmation needed - direct action
                fetch('/api/reject-phase2-match', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        oslId: oslId,
                        innovaInternalId: innovaInternalId
                    })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // Remove this specific candidate row
                        const checkbox = document.querySelector(`input[data-osl-id="${oslId}"][data-innova-id="${innovaInternalId}"]`);
                        if (checkbox) {
                            const row = checkbox.closest('tr');
                            row.style.backgroundColor = '#f8d7da';
                            row.style.opacity = '0.7';

                            setTimeout(() => {
                                row.remove();
                                // Update the data arrays
                                validationData = validationData.filter(r => !(r.osl_id === oslId && r.innova_internal_id === innovaInternalId));
                                filteredData = filteredData.filter(r => !(r.osl_id === oslId && r.innova_internal_id === innovaInternalId));
                                updateSelectedCount();
                            }, 1000);
                        }

                        // No additional alert - the visual feedback is sufficient
                    } else {
                        alert('Error rejecting match: ' + (data.error || 'Unknown error'));
                    }
                })
                .catch(error => {
                    alert('Error: ' + error.message);
                });
        }

        function markMPSInactive(mpsId) {
            // No confirmation needed - direct action
            fetch('/api/mark-mps-inactive', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    mpsId: mpsId
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Remove all rows for this MPS ID (all OSLs using this MPS)
                    const rows = document.querySelectorAll(`input[data-osl-id]`);
                    rows.forEach(checkbox => {
                        const oslId = parseInt(checkbox.dataset.oslId);
                        const record = validationData.find(r => r.osl_id === oslId);
                        if (record && record.mps_id === mpsId) {
                            const row = checkbox.closest('tr');
                            row.style.backgroundColor = '#f8f9fa';
                            row.style.opacity = '0.5';
                            row.style.textDecoration = 'line-through';
                        }
                    });

                    setTimeout(() => {
                        // Remove from data arrays
                        validationData = validationData.filter(r => r.mps_id !== mpsId);
                        filteredData = filteredData.filter(r => r.mps_id !== mpsId);

                        // Re-render the table to maintain current filters
                        if (isEnhancedData) {
                            // Check if this is Phase 2 data (has candidate_rank)
                            if (validationData.length > 0 && validationData[0].candidate_rank !== undefined) {
                                renderPhase2Table();
                            } else {
                                renderEnhancedTable();
                            }
                        } else {
                            renderTable();
                        }

                        updateSelectedCount();
                    }, 1500);
                } else {
                    alert('Error marking MPS as inactive: ' + (data.error || 'Unknown error'));
                }
            })
            .catch(error => {
                alert('Error: ' + error.message);
            });
        }

        function bulkMarkMPSInactive() {
            const selectedCheckboxes = Array.from(document.querySelectorAll('.row-checkbox:checked'));
            if (selectedCheckboxes.length === 0) {
                alert('No records selected');
                return;
            }

            // Get unique MPS IDs from selected records
            const mpsIds = [...new Set(selectedCheckboxes.map(checkbox => {
                const oslId = parseInt(checkbox.dataset.oslId);
                const record = filteredData.find(r => r.osl_id === oslId);
                return record ? record.mps_id : null;
            }).filter(id => id !== null))];

            if (mpsIds.length === 0) {
                alert('No valid MPS records found in selection');
                return;
            }

            // No confirmation needed - direct action
            let processedCount = 0;
            let errors = [];

            mpsIds.forEach(mpsId => {
                fetch('/api/mark-mps-inactive', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ mpsId: mpsId })
                })
                .then(response => response.json())
                .then(data => {
                    processedCount++;

                    if (data.success) {
                        // Remove all rows for this MPS ID
                        validationData = validationData.filter(r => r.mps_id !== mpsId);
                        filteredData = filteredData.filter(r => r.mps_id !== mpsId);
                    } else {
                        errors.push(`MPS ${mpsId}: ${data.error}`);
                    }

                    // When all requests are done, re-render table
                    if (processedCount === mpsIds.length) {
                        // Re-render the table to maintain current filters
                        if (isEnhancedData) {
                            if (validationData.length > 0 && validationData[0].candidate_rank !== undefined) {
                                renderPhase2Table();
                            } else {
                                renderEnhancedTable();
                            }
                        } else {
                            renderTable();
                        }

                        updateSelectedCount();

                        if (errors.length > 0) {
                            console.warn('Some MPS records had errors:', errors);
                        }
                    }
                })
                .catch(error => {
                    processedCount++;
                    errors.push(`MPS ${mpsId}: ${error.message}`);

                    if (processedCount === mpsIds.length) {
                        updateSelectedCount();
                        alert('Some errors occurred: ' + errors.join(', '));
                    }
                });
            });
        }

        function bulkRejectMatches() {
            const selectedCheckboxes = Array.from(document.querySelectorAll('.row-checkbox:checked'));
            if (selectedCheckboxes.length === 0) {
                alert('No records selected');
                return;
            }

            // Build rejection array from selected records
            const rejections = [];
            selectedCheckboxes.forEach(checkbox => {
                const oslId = parseInt(checkbox.dataset.oslId);
                const record = filteredData.find(r => r.osl_id === oslId);
                if (record && record.innova_internal_id) {
                    rejections.push({
                        oslId: oslId,
                        innovaInternalId: record.innova_internal_id
                    });
                }
            });

            if (rejections.length === 0) {
                alert('No valid matches found in selected records');
                return;
            }

            // No confirmation needed - direct action
            let processedCount = 0;
            let errors = [];

            rejections.forEach(rejection => {
                fetch('/api/reject-phase2-match', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        oslId: rejection.oslId,
                        innovaInternalId: rejection.innovaInternalId
                    })
                })
                .then(response => response.json())
                .then(data => {
                    processedCount++;

                    if (data.success) {
                        // Remove this specific OSL-Innova combination
                        const row = document.querySelector(`input[data-osl-id="${rejection.oslId}"]`);
                        if (row) {
                            const tableRow = row.closest('tr');
                            tableRow.style.backgroundColor = '#f8d7da';
                            tableRow.style.opacity = '0.7';
                            setTimeout(() => tableRow.remove(), 1000);
                        }

                        // Remove from data arrays
                        validationData = validationData.filter(r =>
                            !(r.osl_id === rejection.oslId && r.innova_internal_id === rejection.innovaInternalId)
                        );
                        filteredData = filteredData.filter(r =>
                            !(r.osl_id === rejection.oslId && r.innova_internal_id === rejection.innovaInternalId)
                        );
                    } else {
                        errors.push(`OSL ${rejection.oslId}: ${data.error}`);
                    }

                    // When all requests are done, update count
                    if (processedCount === rejections.length) {
                        updateSelectedCount();

                        if (errors.length > 0) {
                            console.warn('Some rejections had errors:', errors);
                        }
                    }
                })
                .catch(error => {
                    processedCount++;
                    errors.push(`OSL ${rejection.oslId}: ${error.message}`);

                    if (processedCount === rejections.length) {
                        updateSelectedCount();
                        if (errors.length > 0) {
                            alert('Some errors occurred: ' + errors.join(', '));
                        }
                    }
                });
            });
        }

        function updateWeightsAndConnect(oslId, innovaInternalId, innovaWeights, oslWeights) {
            // Parse Innova weights to extract min/max
            const innovaMatch = innovaWeights.match(/(\d+)-(\d+)/);
            if (!innovaMatch) {
                alert('Cannot parse Innova weights: ' + innovaWeights);
                return;
            }

            const innovaMin = parseInt(innovaMatch[1]);
            const innovaMax = parseInt(innovaMatch[2]);

            // No confirmation needed - direct action
            fetch('/api/update-weights-and-connect', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    oslId: oslId,
                    innovaInternalId: innovaInternalId,
                    newMinWeight: innovaMin,
                    newMaxWeight: innovaMax,
                    originalWeights: oslWeights,
                    innovaWeights: innovaWeights
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Update the row to show the change
                    const row = document.querySelector(`input[data-osl-id="${oslId}"]`).closest('tr');
                    if (row) {
                        row.style.backgroundColor = '#d1ecf1';
                        row.style.border = '2px solid #bee5eb';

                        // Update the weights display in the row
                        const weightCell = row.querySelector('td:nth-child(8)'); // OSL weights column
                        if (weightCell) {
                            weightCell.innerHTML = `<strong>${innovaMin}-${innovaMax}</strong> <small style="color: #6c757d;">(was ${oslWeights})</small>`;
                        }

                        // Remove the row after a delay
                        setTimeout(() => {
                            row.style.opacity = '0.7';
                            setTimeout(() => {
                                row.remove();
                                // Remove from data arrays
                                validationData = validationData.filter(r => r.osl_id !== oslId);
                                filteredData = filteredData.filter(r => r.osl_id !== oslId);
                                updateSelectedCount();
                            }, 1000);
                        }, 2000);
                    }
                } else {
                    alert('Error updating weights and connecting: ' + (data.error || 'Unknown error'));
                }
            })
            .catch(error => {
                alert('Error: ' + error.message);
            });
        }

        function clearFromPhase2(oslId) {
            // Mark OSL as verified without creating a vendor connection
            // This removes it from future Phase 2 matching
            fetch('/api/clear-from-phase2', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    oslId: oslId
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Remove all rows for this OSL ID
                    const rows = document.querySelectorAll(`input[data-osl-id="${oslId}"]`);
                    rows.forEach(checkbox => {
                        const row = checkbox.closest('tr');
                        if (row) {
                            row.style.backgroundColor = '#d1ecf1';
                            row.style.opacity = '0.7';
                            row.style.textDecoration = 'line-through';
                        }
                    });

                    setTimeout(() => {
                        // Remove from data arrays
                        validationData = validationData.filter(r => r.osl_id !== oslId);
                        filteredData = filteredData.filter(r => r.osl_id !== oslId);

                        // Remove from DOM
                        rows.forEach(checkbox => {
                            checkbox.closest('tr').remove();
                        });

                        updateSelectedCount();
                    }, 1500);
                } else {
                    alert('Error clearing from Phase 2: ' + (data.error || 'Unknown error'));
                }
            })
            .catch(error => {
                alert('Error: ' + error.message);
            });
        }

        function showPhase2Details(oslId, innovaDescription, confidenceScore) {
            const record = validationData.find(r => r.osl_id === oslId && r.innova_description === innovaDescription);
            if (record) {
                const moldIcon = record.mold_match ? '✅' : '❌';
                const plasticIcon = record.plastic_match ? '✅' : '❌';
                const weightIcon = record.weight_match === 'EXACT_MATCH' ? '✅' : record.weight_match === 'OVERLAP' ? '⚠️' : '❌';

                alert(`Phase 2 Match Details (Rank #${record.candidate_rank}):\n\n` +
                      `OSL Data:\n` +
                      `  OSL ID: ${record.osl_id}\n` +
                      `  MPS ID: ${record.mps_id || 'N/A'}\n` +
                      `  Mold: ${record.osl_mold}\n` +
                      `  Plastic: ${record.osl_plastic}\n` +
                      `  Stamp: ${record.osl_stamp}\n` +
                      `  Weights: ${record.osl_weights}\n` +
                      `  Color: ${record.osl_color}\n\n` +
                      `Innova Candidate:\n` +
                      `  Internal ID: ${record.innova_internal_id}\n` +
                      `  Description: ${record.innova_description}\n` +
                      `  Parsed Mold: ${record.innova_parsed?.mold || 'N/A'}\n` +
                      `  Parsed Plastic: ${record.innova_parsed?.plastic || 'N/A'}\n` +
                      `  Weights: ${record.innova_weights}\n\n` +
                      `Matching Analysis:\n` +
                      `  ${moldIcon} Mold Match: ${record.mold_match}\n` +
                      `  ${plasticIcon} Plastic Match: ${record.plastic_match}\n` +
                      `  ${weightIcon} Weight Match: ${record.weight_match}\n` +
                      `  🎯 Confidence Score: ${record.confidence_score}%\n` +
                      `  📊 String Similarity: ${record.string_similarity}%\n\n` +
                      `Recommendation: ${record.confidence_score >= 80 ? '🟢 High confidence - Good match' :
                                        record.confidence_score >= 70 ? '🟡 Medium confidence - Review carefully' :
                                        '🟠 Low confidence - Consider alternatives'}`);
            }
        }

        function confirmSelected() {
            const selected = Array.from(document.querySelectorAll('.row-checkbox:checked')).map(cb => parseInt(cb.dataset.oslId));
            if (selected.length === 0) {
                alert('No records selected');
                return;
            }
            if (confirm(`✅ Confirm ${selected.length} selected matches as correct?`)) {
                bulkUpdateMatches(selected, true);
            }
        }

        function rejectSelected() {
            const selected = Array.from(document.querySelectorAll('.row-checkbox:checked')).map(cb => parseInt(cb.dataset.oslId));
            if (selected.length === 0) {
                alert('No records selected');
                return;
            }
            if (confirm(`❌ Reject ${selected.length} selected matches?\n\nThis will clear their vendor_internal_id connections.`)) {
                bulkUpdateMatches(selected, false);
            }
        }

        function phase2BulkConnect() {
            const selectedCheckboxes = Array.from(document.querySelectorAll('.row-checkbox:checked'));
            if (selectedCheckboxes.length === 0) {
                alert('No records selected');
                return;
            }

            // Build connections array from selected Phase 2 records
            const connections = [];
            selectedCheckboxes.forEach(checkbox => {
                const oslId = parseInt(checkbox.dataset.oslId);
                const record = filteredData.find(r => r.osl_id === oslId);
                if (record && record.innova_internal_id) {
                    connections.push({
                        oslId: oslId,
                        vendorInternalId: record.innova_internal_id
                    });
                }
            });

            if (connections.length === 0) {
                alert('No valid connections found in selected records');
                return;
            }

            // No confirmation needed - direct action
            fetch('/api/phase2-bulk-connect', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    connections: connections
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Remove processed rows
                    connections.forEach(conn => {
                        const row = document.querySelector(`input[data-osl-id="${conn.oslId}"]`).closest('tr');
                        if (row) {
                            row.style.backgroundColor = '#d4edda';
                            row.style.opacity = '0.7';
                        }
                    });

                    setTimeout(() => {
                        connections.forEach(conn => {
                            const row = document.querySelector(`input[data-osl-id="${conn.oslId}"]`);
                            if (row) row.closest('tr').remove();
                        });
                        // Update the data arrays
                        const connectedOslIds = connections.map(c => c.oslId);
                        validationData = validationData.filter(r => !connectedOslIds.includes(r.osl_id));
                        filteredData = filteredData.filter(r => !connectedOslIds.includes(r.osl_id));
                        updateSelectedCount();
                    }, 1000);

                    if (data.errors && data.errors.length > 0) {
                        console.warn('Some connections had errors:', data.errors);
                    }
                } else {
                    alert('Error connecting matches: ' + (data.error || 'Unknown error'));
                }
            })
            .catch(error => {
                alert('Error: ' + error.message);
            });
        }

        function updateMatchVerification(oslId, confirmed) {
            fetch('/api/update-match-verification', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    oslId: oslId,
                    confirmed: confirmed
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Remove the row from the table or update its status
                    const row = document.querySelector(`input[data-osl-id="${oslId}"]`).closest('tr');
                    row.style.backgroundColor = confirmed ? '#d4edda' : '#f8d7da';
                    row.style.opacity = '0.7';
                    setTimeout(() => {
                        row.remove();
                        // Update the data arrays
                        validationData = validationData.filter(r => r.osl_id !== oslId);
                        filteredData = filteredData.filter(r => r.osl_id !== oslId);
                        updateSelectedCount();
                    }, 1000);
                } else {
                    alert('Error updating match: ' + (data.error || 'Unknown error'));
                }
            })
            .catch(error => {
                alert('Error: ' + error.message);
            });
        }

        function bulkUpdateMatches(oslIds, confirmed) {
            fetch('/api/bulk-update-match-verification', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    oslIds: oslIds,
                    confirmed: confirmed
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Remove processed rows
                    oslIds.forEach(oslId => {
                        const row = document.querySelector(`input[data-osl-id="${oslId}"]`).closest('tr');
                        if (row) {
                            row.style.backgroundColor = confirmed ? '#d4edda' : '#f8d7da';
                            row.style.opacity = '0.7';
                        }
                    });

                    setTimeout(() => {
                        oslIds.forEach(oslId => {
                            const row = document.querySelector(`input[data-osl-id="${oslId}"]`);
                            if (row) row.closest('tr').remove();
                        });
                        // Update the data arrays
                        validationData = validationData.filter(r => !oslIds.includes(r.osl_id));
                        filteredData = filteredData.filter(r => !oslIds.includes(r.osl_id));
                        updateSelectedCount();
                    }, 1000);

                    alert(`Successfully processed ${data.updated} records`);
                } else {
                    alert('Error updating matches: ' + (data.error || 'Unknown error'));
                }
            })
            .catch(error => {
                alert('Error: ' + error.message);
            });
        }

        // Initialize Informed tab
        document.addEventListener('DOMContentLoaded', function() {
            // Initial status refresh for Informed tab
            refreshInformedStatus();

            // Initial status refresh for Discraft tab
            refreshDiscraftStatus();
        });

        // Discraft Workflow Functions
        function runDiscraftImport() {
            const outputDiv = document.getElementById('discraftImportOutput');
            outputDiv.style.display = 'block';
            outputDiv.innerHTML = '🚀 Starting Discraft vendor catalog import...\n';
            outputDiv.innerHTML += 'This will truncate existing data and import fresh catalog from discraftstock.xlsx\n\n';

            // Call API to run Discraft import
            fetch('/api/discraft/import', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    outputDiv.innerHTML += '✅ Import completed successfully!\n\n';
                    outputDiv.innerHTML += `📊 Import Results:\n`;
                    outputDiv.innerHTML += `   • Products imported: ${data.totalProducts || 'N/A'}\n`;
                    outputDiv.innerHTML += `   • Import batch ID: ${data.batchId || 'N/A'}\n\n`;

                    // Automatically calculate MPS IDs after import
                    outputDiv.innerHTML += '🔄 Calculating MPS IDs...\n';

                    return fetch('/api/discraft/calculate-mps', {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' }
                    });
                } else {
                    throw new Error(data.error);
                }
            })
            .then(response => response.json())
            .then(mpsData => {
                if (mpsData.success) {
                    outputDiv.innerHTML += '✅ MPS ID calculation completed!\n\n';
                    outputDiv.innerHTML += `🎯 Matching Results:\n`;
                    outputDiv.innerHTML += `   • Successfully matched: ${mpsData.calculatedCount}\n`;
                    outputDiv.innerHTML += `   • Failed to match: ${mpsData.failedCount}\n`;
                    outputDiv.innerHTML += `   • Success rate: ${mpsData.successRate}%\n\n`;

                    if (mpsData.successRate >= 80) {
                        outputDiv.innerHTML += '🎉 Great matching success rate!\n';
                    } else {
                        outputDiv.innerHTML += '⚠️ Consider reviewing unmatched products to improve parsing\n';
                    }

                    // Refresh statistics
                    refreshDiscraftStatus();
                } else {
                    outputDiv.innerHTML += `⚠️ MPS calculation failed: ${mpsData.error}\n`;
                }
            })
            .catch(error => {
                outputDiv.innerHTML += `❌ Error: ${error.message}\n`;
                console.error('Discraft import error:', error);
            });
        }



        function analyzeMatching() {
            const outputDiv = document.getElementById('discraftImportOutput');
            outputDiv.style.display = 'block';
            outputDiv.innerHTML = '📊 Analyzing matching results...\n';

            fetch('/api/discraft/analyze-matching')
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    outputDiv.innerHTML = '📊 Matching Analysis Results:\n\n';
                    outputDiv.innerHTML += `🎯 MPS Matching:\n`;
                    outputDiv.innerHTML += `   • Products with MPS ID: ${data.withMpsId}\n`;
                    outputDiv.innerHTML += `   • Products without MPS ID: ${data.withoutMpsId}\n`;
                    outputDiv.innerHTML += `   • Success rate: ${data.mpsSuccessRate}%\n\n`;

                    if (data.topUnmatched && data.topUnmatched.length > 0) {
                        outputDiv.innerHTML += `❌ Top Unmatched Products (need parsing fixes):\n`;
                        data.topUnmatched.forEach(item => {
                            outputDiv.innerHTML += `   • ${item.plastic_name} ${item.mold_name} - ${item.stamp_name} (${item.count} products)\n`;
                        });
                    }
                } else {
                    outputDiv.innerHTML = `❌ Analysis failed: ${data.error}\n`;
                }
            })
            .catch(error => {
                outputDiv.innerHTML = `❌ Error: ${error.message}\n`;
            });
        }



        function reviewUnmatchedOsls() {
            const outputDiv = document.getElementById('oslMatchingOutput');
            const tableDiv = document.getElementById('unmatchedOslsTable');
            const tableBody = document.getElementById('unmatchedOslsTableBody');

            outputDiv.style.display = 'block';
            outputDiv.innerHTML = '📋 Loading unmatched Discraft OSLs...\n';
            tableDiv.style.display = 'none';

            // Hide record count initially
            const recordCountDiv = document.getElementById('tableRecordCount');
            recordCountDiv.innerHTML = '';

            const showInactive = document.getElementById('showInactiveMps').checked;
            const url = `/api/discraft/review-unmatched-osls?showInactive=${showInactive}`;

            fetch(url)
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    const showingCount = data.unmatchedOsls ? data.unmatchedOsls.length : 0;
                    const totalCount = data.totalUnmatchedOsls || 0;

                    outputDiv.innerHTML = `📋 Found ${totalCount} unmatched Discraft OSLs\n`;
                    outputDiv.innerHTML += '💡 These are your OSLs that Discraft doesn\'t seem to carry.\n';
                    outputDiv.innerHTML += 'You can mark MPS records as inactive to remove them from future matching.\n\n';

                    if (data.unmatchedOsls && data.unmatchedOsls.length > 0) {
                        tableDiv.style.display = 'block';
                        tableBody.innerHTML = '';

                        // Update record count display
                        const recordCountDiv = document.getElementById('tableRecordCount');
                        recordCountDiv.innerHTML = `Showing ${showingCount} of ${totalCount} records ${showingCount < totalCount ? '(limited to first 1,000)' : ''}`;

                        data.unmatchedOsls.forEach(osl => {
                            const mps = osl.t_mps;
                            const plastic = mps.t_plastics.plastic;
                            const mold = mps.t_molds.mold;
                            const stamp = mps.t_stamps.stamp;
                            const color = osl.color_name || 'N/A';
                            const isActive = mps.active;

                            const row = document.createElement('tr');
                            row.style.backgroundColor = isActive ? '#ffffff' : '#f8f9fa';
                            row.innerHTML = `
                                <td style="border: 1px solid #ddd; padding: 8px;">${osl.id}</td>
                                <td style="border: 1px solid #ddd; padding: 8px;">${osl.mps_id}</td>
                                <td style="border: 1px solid #ddd; padding: 8px;">${plastic}</td>
                                <td style="border: 1px solid #ddd; padding: 8px;">${mold}</td>
                                <td style="border: 1px solid #ddd; padding: 8px;">${stamp}</td>
                                <td style="border: 1px solid #ddd; padding: 8px;">${osl.min_weight}-${osl.max_weight}g</td>
                                <td style="border: 1px solid #ddd; padding: 8px;">${color}</td>
                                <td style="border: 1px solid #ddd; padding: 8px;">
                                    <span style="color: ${isActive ? '#28a745' : '#dc3545'}; font-weight: bold;">
                                        ${isActive ? '✅ Active' : '❌ Inactive'}
                                    </span>
                                </td>
                                <td style="border: 1px solid #ddd; padding: 4px; text-align: center;">
                                    ${isActive ? `
                                        <button onclick="markMpsInactive(${osl.mps_id}, this)"
                                                title="Mark MPS as Inactive"
                                                style="background: #dc3545; color: white; border: none; padding: 4px 6px; border-radius: 3px; cursor: pointer; font-size: 14px; line-height: 1;">
                                            🚫
                                        </button>
                                    ` : `
                                        <button onclick="markMpsActive(${osl.mps_id}, this)"
                                                title="Mark MPS as Active"
                                                style="background: #28a745; color: white; border: none; padding: 4px 6px; border-radius: 3px; cursor: pointer; font-size: 14px; line-height: 1;">
                                            ✅
                                        </button>
                                    `}
                                </td>
                            `;
                            tableBody.appendChild(row);
                        });
                    } else {
                        outputDiv.innerHTML += '✅ No unmatched OSLs found!\n';
                        // Hide record count when no records
                        const recordCountDiv = document.getElementById('tableRecordCount');
                        recordCountDiv.innerHTML = '';
                    }
                } else {
                    outputDiv.innerHTML = `❌ Review failed: ${data.error}\n`;
                }
            })
            .catch(error => {
                outputDiv.innerHTML = `❌ Error: ${error.message}\n`;
            });
        }

        // Global variables for sorting
        let currentSortColumn = -1;
        let currentSortDirection = 'asc';

        function toggleInactiveMps() {
            // Refresh the table when the checkbox is toggled
            reviewUnmatchedOsls();
        }

        function sortTable(columnIndex) {
            const table = document.getElementById('unmatchedOslsTableContent');
            const tbody = table.querySelector('tbody');
            const rows = Array.from(tbody.querySelectorAll('tr'));

            // Determine sort direction
            if (currentSortColumn === columnIndex) {
                currentSortDirection = currentSortDirection === 'asc' ? 'desc' : 'asc';
            } else {
                currentSortDirection = 'asc';
                currentSortColumn = columnIndex;
            }

            // Update sort indicators
            for (let i = 0; i <= 7; i++) {
                const indicator = document.getElementById(`sort-${i}`);
                if (indicator) {
                    if (i === columnIndex) {
                        indicator.textContent = currentSortDirection === 'asc' ? '↑' : '↓';
                        indicator.style.color = '#007bff';
                    } else {
                        indicator.textContent = '⇅';
                        indicator.style.color = '#666';
                    }
                }
            }

            // Sort rows
            rows.sort((a, b) => {
                let aValue = a.cells[columnIndex].textContent.trim();
                let bValue = b.cells[columnIndex].textContent.trim();

                // Handle numeric columns (OSL ID, MPS ID)
                if (columnIndex === 0 || columnIndex === 1) {
                    aValue = parseInt(aValue) || 0;
                    bValue = parseInt(bValue) || 0;
                }

                // Handle weight column (extract first number)
                if (columnIndex === 5) {
                    aValue = parseInt(aValue.split('-')[0]) || 0;
                    bValue = parseInt(bValue.split('-')[0]) || 0;
                }

                // Handle active status column
                if (columnIndex === 7) {
                    aValue = aValue.includes('Active') ? 1 : 0;
                    bValue = bValue.includes('Active') ? 1 : 0;
                }

                if (typeof aValue === 'number' && typeof bValue === 'number') {
                    return currentSortDirection === 'asc' ? aValue - bValue : bValue - aValue;
                } else {
                    const comparison = aValue.localeCompare(bValue);
                    return currentSortDirection === 'asc' ? comparison : -comparison;
                }
            });

            // Re-append sorted rows
            rows.forEach(row => tbody.appendChild(row));
        }

        function markMpsInactive(mpsId, buttonElement) {
            buttonElement.disabled = true;
            buttonElement.innerHTML = '⏳ Processing...';

            fetch('/api/discraft/mark-mps-inactive', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ mpsId: mpsId })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Update ALL rows with this MPS ID
                    updateAllRowsWithMpsId(mpsId, false);
                } else {
                    alert(`❌ Failed to mark MPS as inactive: ${data.error}`);
                    buttonElement.disabled = false;
                    buttonElement.innerHTML = '🚫 Mark Inactive';
                }
            })
            .catch(error => {
                alert(`❌ Error: ${error.message}`);
                buttonElement.disabled = false;
                buttonElement.innerHTML = '🚫 Mark Inactive';
            });
        }

        function markMpsActive(mpsId, buttonElement) {
            buttonElement.disabled = true;
            buttonElement.innerHTML = '⏳ Processing...';

            fetch('/api/discraft/mark-mps-active', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ mpsId: mpsId })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Update ALL rows with this MPS ID
                    updateAllRowsWithMpsId(mpsId, true);
                } else {
                    alert(`❌ Failed to mark MPS as active: ${data.error}`);
                    buttonElement.disabled = false;
                    buttonElement.innerHTML = '✅ Mark Active';
                }
            })
            .catch(error => {
                alert(`❌ Error: ${error.message}`);
                buttonElement.disabled = false;
                buttonElement.innerHTML = '✅ Mark Active';
            });
        }

        function updateAllRowsWithMpsId(mpsId, isActive) {
            const tableBody = document.getElementById('unmatchedOslsTableBody');
            const rows = tableBody.querySelectorAll('tr');

            rows.forEach(row => {
                const mpsCell = row.cells[1]; // MPS ID is in column 1
                if (mpsCell && mpsCell.textContent.trim() === mpsId.toString()) {
                    // Update row background
                    row.style.backgroundColor = isActive ? '#ffffff' : '#f8f9fa';

                    // Update active status cell (column 7)
                    const activeCell = row.cells[7];
                    activeCell.innerHTML = isActive
                        ? '<span style="color: #28a745; font-weight: bold;">✅ Active</span>'
                        : '<span style="color: #dc3545; font-weight: bold;">❌ Inactive</span>';

                    // Update action button (column 8)
                    const actionCell = row.cells[8];
                    actionCell.innerHTML = isActive
                        ? `<button onclick="markMpsInactive(${mpsId}, this)"
                                   title="Mark MPS as Inactive"
                                   style="background: #dc3545; color: white; border: none; padding: 4px 6px; border-radius: 3px; cursor: pointer; font-size: 14px; line-height: 1;">
                               🚫
                           </button>`
                        : `<button onclick="markMpsActive(${mpsId}, this)"
                                   title="Mark MPS as Active"
                                   style="background: #28a745; color: white; border: none; padding: 4px 6px; border-radius: 3px; cursor: pointer; font-size: 14px; line-height: 1;">
                               ✅
                           </button>`;
                }
            });
        }

        function exportDiscraftOrders() {
            const outputDiv = document.getElementById('discraftExportOutput');
            outputDiv.style.display = 'block';
            outputDiv.innerHTML = '📤 Starting Discraft order data export...\n';
            outputDiv.innerHTML += 'This will create a copy of the spreadsheet with order quantities from v_stats_by_osl_discraft\n\n';

            fetch('/api/discraft/export-orders', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    outputDiv.innerHTML += '✅ Export completed successfully!\n\n';
                    outputDiv.innerHTML += `📊 Export Results:\n`;
                    outputDiv.innerHTML += `   • Output file: ${data.outputFile || 'N/A'}\n`;
                    outputDiv.innerHTML += `   • Records processed: ${data.recordsProcessed || 'N/A'}\n`;
                    outputDiv.innerHTML += `   • Order quantities updated: ${data.orderQuantitiesUpdated || 'N/A'}\n\n`;

                    if (data.outputFile) {
                        outputDiv.innerHTML += `📁 File saved to: ${data.outputFile}\n`;
                        outputDiv.innerHTML += '💡 You can now use this file for placing orders with Discraft\n';
                    }
                } else {
                    outputDiv.innerHTML += `❌ Export failed: ${data.error}\n`;
                    if (data.details) {
                        outputDiv.innerHTML += `Details: ${data.details}\n`;
                    }
                }
            })
            .catch(error => {
                outputDiv.innerHTML += `❌ Error: ${error.message}\n`;
            });
        }

        function reviewParsingIssues() {
            const outputDiv = document.getElementById('oslMatchingOutput');
            outputDiv.style.display = 'block';
            outputDiv.innerHTML = '🔧 Analyzing parsing issues...\n';

            fetch('/api/discraft/review-parsing-issues')
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    outputDiv.innerHTML = '🔧 Discraft Parsing Issues Analysis:\n\n';

                    if (data.unmatchedVendorProducts && data.unmatchedVendorProducts.length > 0) {
                        outputDiv.innerHTML += `❌ Vendor Products That Don't Match Your MPS (${data.unmatchedVendorProducts.length} shown):\n`;
                        data.unmatchedVendorProducts.forEach((product, index) => {
                            outputDiv.innerHTML += `${index + 1}. "${product.plastic_name}" | "${product.mold_name}" | "${product.stamp_name}"\n`;
                            outputDiv.innerHTML += `   Available: ${product.is_currently_available ? 'Yes' : 'No'}, Weight: ${product.min_weight}-${product.max_weight}g\n`;
                        });
                        outputDiv.innerHTML += '\n';
                    }

                    if (data.plasticMismatches && data.plasticMismatches.length > 0) {
                        outputDiv.innerHTML += `🎯 Plastic Parsing Issues:\n`;
                        data.plasticMismatches.forEach((plastic, index) => {
                            outputDiv.innerHTML += `${index + 1}. Vendor: "${plastic.vendor_plastic}" → Your DB: ${plastic.db_suggestions.join(', ') || 'No matches'}\n`;
                        });
                        outputDiv.innerHTML += '\n';
                    }

                    if (data.moldMismatches && data.moldMismatches.length > 0) {
                        outputDiv.innerHTML += `🥏 Mold Parsing Issues:\n`;
                        data.moldMismatches.forEach((mold, index) => {
                            outputDiv.innerHTML += `${index + 1}. Vendor: "${mold.vendor_mold}" → Your DB: ${mold.db_suggestions.join(', ') || 'No matches'}\n`;
                        });
                        outputDiv.innerHTML += '\n';
                    }

                    outputDiv.innerHTML += '💡 Recommendations:\n';
                    outputDiv.innerHTML += '1. Fix parsing logic to match vendor names to your MPS names\n';
                    outputDiv.innerHTML += '2. Focus on the most common mismatches first\n';
                    outputDiv.innerHTML += '3. Consider if some vendor products are not in your catalog\n';
                } else {
                    outputDiv.innerHTML = `❌ Analysis failed: ${data.error}\n`;
                }
            })
            .catch(error => {
                outputDiv.innerHTML = `❌ Error: ${error.message}\n`;
            });
        }

        function refreshDiscraftStatus() {
            const statsDiv = document.getElementById('discraftStats');
            if (!statsDiv) return;

            // Call API to get current statistics
            fetch('/api/discraft/status')
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    statsDiv.innerHTML = `
                        <h4>📊 Current Statistics</h4>
                        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin-top: 10px;">
                            <div style="background: #e8f5e8; padding: 10px; border-radius: 5px; text-align: center;">
                                <strong style="color: #27ae60; font-size: 24px;">${data.totalProducts || 0}</strong><br>
                                <span style="color: #27ae60;">Total Products</span>
                            </div>
                            <div style="background: #e8f4fd; padding: 10px; border-radius: 5px; text-align: center;">
                                <strong style="color: #3498db; font-size: 24px;">${data.availableProducts || 0}</strong><br>
                                <span style="color: #3498db;">Available Now</span>
                            </div>
                            <div style="background: #fff3cd; padding: 10px; border-radius: 5px; text-align: center;">
                                <strong style="color: #f39c12; font-size: 24px;">${data.outOfStockProducts || 0}</strong><br>
                                <span style="color: #f39c12;">Out of Stock</span>
                            </div>
                            <div style="background: #f8d7da; padding: 10px; border-radius: 5px; text-align: center;">
                                <strong style="color: #dc3545; font-size: 24px;">${data.uniqueKeys || 0}</strong><br>
                                <span style="color: #dc3545;">Mapping Keys</span>
                            </div>
                        </div>
                        <p style="margin-top: 15px; color: #6c757d; font-style: italic;">
                            Last Import: ${data.lastImport || 'Never'}
                        </p>
                    `;
                } else {
                    statsDiv.innerHTML = '<p style="color: #dc3545;">Error loading statistics. Run import first.</p>';
                }
            })
            .catch(error => {
                statsDiv.innerHTML = '<p style="color: #dc3545;">Error loading statistics.</p>';
            });
        }
    </script>
</body>
</html>

