import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

dotenv.config();

const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_KEY;
const supabase = createClient(supabaseUrl, supabaseKey);

async function simpleDebug() {
    try {
        console.log('🔍 Simple debug for matching issue...\n');
        
        // Check MPS 14849
        console.log('1. Checking MPS 14849:');
        const { data: mps, error: mpsError } = await supabase
            .from('t_mps')
            .select(`id, active, t_plastics(plastic, brand_id), t_molds(mold, brand_id), t_stamps(stamp)`)
            .eq('id', 14849);
        
        if (mpsError) {
            console.error('Error:', mpsError);
            return;
        }
        
        if (mps && mps.length > 0) {
            const m = mps[0];
            console.log(`   MPS ${m.id}: "${m.t_plastics.plastic}" | "${m.t_molds.mold}" | "${m.t_stamps.stamp}"`);
            console.log(`   Active: ${m.active}, Plastic Brand: ${m.t_plastics.brand_id}, Mold Brand: ${m.t_molds.brand_id}`);
        } else {
            console.log('   Not found');
        }
        
        // Check for products that should match
        console.log('\n2. Looking for products with "Jawbreaker | Roach | Stock":');
        const { data: products, error: prodError } = await supabase
            .from('it_discraft_order_sheet_lines')
            .select('id, plastic_name, mold_name, stamp_name, calculated_mps_id')
            .eq('plastic_name', 'Jawbreaker')
            .eq('mold_name', 'Roach')
            .eq('stamp_name', 'Stock');
        
        if (prodError) {
            console.error('Error:', prodError);
            return;
        }
        
        if (products && products.length > 0) {
            products.forEach(p => {
                console.log(`   ID ${p.id}: "${p.plastic_name}" | "${p.mold_name}" | "${p.stamp_name}" → MPS: ${p.calculated_mps_id || 'NULL'}`);
            });
        } else {
            console.log('   No exact matches found');
            
            // Try partial matches
            console.log('\n3. Looking for partial matches:');
            const { data: partial, error: partialError } = await supabase
                .from('it_discraft_order_sheet_lines')
                .select('id, plastic_name, mold_name, stamp_name, calculated_mps_id')
                .ilike('plastic_name', '%jawbreaker%')
                .limit(5);
            
            if (!partialError && partial) {
                partial.forEach(p => {
                    console.log(`   ID ${p.id}: "${p.plastic_name}" | "${p.mold_name}" | "${p.stamp_name}" → MPS: ${p.calculated_mps_id || 'NULL'}`);
                });
            }
        }
        
        // Check what's in the ID range you mentioned
        console.log('\n4. Checking ID range 14848-14854:');
        const { data: range, error: rangeError } = await supabase
            .from('it_discraft_order_sheet_lines')
            .select('id, plastic_name, mold_name, stamp_name, calculated_mps_id')
            .gte('id', 14848)
            .lte('id', 14854);
        
        if (!rangeError && range) {
            if (range.length > 0) {
                range.forEach(p => {
                    console.log(`   ID ${p.id}: "${p.plastic_name}" | "${p.mold_name}" | "${p.stamp_name}" → MPS: ${p.calculated_mps_id || 'NULL'}`);
                });
            } else {
                console.log('   No records in this ID range');
            }
        }
        
    } catch (error) {
        console.error('❌ Error:', error);
    }
}

simpleDebug();
