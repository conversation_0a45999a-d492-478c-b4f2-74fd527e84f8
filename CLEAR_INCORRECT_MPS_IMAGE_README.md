# Clear Incorrect MPS Image Task

This document describes the `clear_incorrect_mps_image` task type that removes incorrect MPS images from both the database and S3 storage.

## Overview

The `clear_incorrect_mps_image` task type is designed to clean up incorrect MPS images by:

1. **Database Cleanup**: Deleting the corresponding record from the `t_images` table where `table_name = 't_mps'` and `record_id = id`
2. **S3 Cleanup**: Removing the image file from the S3 bucket (located at `mps/{id}.jpg`)

## Files

- **`processClearIncorrectMpsImageTask.js`** - Main task processor
- **`enqueueClearIncorrectMpsImageTask.js`** - Helper to enqueue tasks and command-line interface
- **`testClearIncorrectMpsImageTask.js`** - Test script to verify functionality
- **`taskQueueWorker.js`** - Updated to handle the new task type

## Usage

### 1. Enqueue a Task Programmatically

```javascript
import { enqueueClearIncorrectMpsImageTask } from './enqueueClearIncorrectMpsImageTask.js';

// Basic usage
const task = await enqueueClearIncorrectMpsImageTask(null, 12345, 'Incorrect image uploaded');

// With custom scheduling (optional)
const scheduledTime = new Date(Date.now() + 10 * 60 * 1000); // 10 minutes from now
const task = await enqueueClearIncorrectMpsImageTask(null, 12345, 'Scheduled deletion', scheduledTime);
```

### 2. Enqueue via Command Line

```bash
# Basic usage
node enqueueClearIncorrectMpsImageTask.js 12345

# With reason
node enqueueClearIncorrectMpsImageTask.js 12345 "Incorrect image uploaded"
```

### 3. Test the Functionality

```bash
# Test with a specific MPS ID
node testClearIncorrectMpsImageTask.js 12345
```

### 4. Process Tasks

Run the task queue worker to process pending tasks:

```bash
# Process once
node taskQueueWorker.js

# Run as daemon
node runTaskQueueWorker.js
```

## Task Payload

The task expects a JSON payload with the following structure:

```json
{
  "id": 12345,
  "reason": "Optional reason for deletion"
}
```

### Required Fields

- **`id`** (number): The MPS ID whose image should be cleared

### Optional Fields

- **`reason`** (string): A description of why the image is being cleared

## Task Processing

When the task is processed, it will:

1. **Validate the payload** - Ensure `id` is provided
2. **Search for t_images record** - Look for a record where `table_name = 't_mps'` and `record_id = id`
3. **Delete database record** - Remove the t_images record if it exists
4. **Delete S3 file** - Remove the file at `mps/{id}.jpg` from the S3 bucket
5. **Update task status** - Mark the task as completed with detailed results

## Task Results

### Successful Completion

```json
{
  "message": "Successfully processed clear_incorrect_mps_image for MPS ID 12345",
  "id": 12345,
  "database_record_found": true,
  "database_deleted": true,
  "s3_file_deleted": true,
  "s3_key": "mps/12345.jpg",
  "processing_time_ms": 1250,
  "processing_time_sec": 1.25
}
```

### No Records Found (Still Successful)

```json
{
  "message": "Successfully processed clear_incorrect_mps_image for MPS ID 12345",
  "id": 12345,
  "database_record_found": false,
  "database_deleted": false,
  "s3_file_deleted": true,
  "s3_key": "mps/12345.jpg",
  "processing_time_ms": 800,
  "processing_time_sec": 0.80
}
```

### Error Case

```json
{
  "message": "Failed to process clear_incorrect_mps_image task due to an unexpected error",
  "error": "Error details here",
  "id": 12345,
  "processing_time_ms": 500,
  "processing_time_sec": 0.50
}
```

## Safety Features

- **Idempotent**: The task can be run multiple times safely. If the t_images record or S3 file doesn't exist, the task will still complete successfully.
- **Graceful Error Handling**: Database and S3 errors are handled separately, so if one fails, the other operation may still succeed.
- **Detailed Logging**: All operations are logged with timestamps and detailed status information.

## S3 Configuration

The task uses the same S3 configuration as other image operations in the system:

- **Region**: Configured via `S3_REGION` environment variable (defaults to 'us-east-1')
- **Bucket**: Configured via `S3_BUCKET` environment variable (defaults to 'discgolfunited')
- **Credentials**: Uses `S3_ACCESS_KEY_ID` and `S3_SECRET_ACCESS_KEY` environment variables

## Monitoring

You can monitor task progress by querying the `t_task_queue` table:

```sql
-- Check specific task
SELECT * FROM t_task_queue WHERE id = <task_id>;

-- Check all clear_incorrect_mps_image tasks
SELECT * FROM t_task_queue 
WHERE task_type = 'clear_incorrect_mps_image' 
ORDER BY created_at DESC;
```

## Troubleshooting

### Common Issues

1. **Missing MPS ID**: Ensure the payload includes a valid `id` field
2. **S3 Permissions**: Verify that the S3 credentials have delete permissions for the bucket
3. **Database Permissions**: Ensure the Supabase service role can delete from the `t_images` table

### Error Messages

- **"Invalid payload: Missing id"**: The task payload doesn't include the required `id` field
- **"Error checking for existing t_images record"**: Database connection or permission issue
- **"Error deleting t_images record"**: Database deletion failed (record may be referenced by other tables)

## Integration with Admin Interface

This task type can be easily integrated into admin interfaces by calling the enqueue function:

```javascript
// In your admin interface
async function clearMpsImage(mpsId, reason) {
  try {
    const task = await enqueueClearIncorrectMpsImageTask(supabase, mpsId, reason);
    console.log(`Task ${task.id} enqueued successfully`);
    return { success: true, taskId: task.id };
  } catch (error) {
    console.error(`Failed to enqueue task: ${error.message}`);
    return { success: false, error: error.message };
  }
}
```
