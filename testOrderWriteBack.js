import { writeOrdersToExcel, detectFileChanges } from './robustOrderWriteBack.js';
import { createClient } from '@supabase/supabase-js';
import path from 'path';
import { fileURLToPath } from 'url';
import dotenv from 'dotenv';

dotenv.config();

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_KEY;
const supabase = createClient(supabaseUrl, supabaseKey);

async function testOrderWriteBack() {
    try {
        console.log('🚀 Testing robust order write-back...\n');
        
        const excelFilePath = path.join(__dirname, 'data', 'external data', 'discraftstock.xlsx');
        const outputFilePath = path.join(__dirname, 'data', 'external data', 'discraftstock_with_orders.xlsx');
        
        // Get some sample order quantities from database
        const { data: sampleProducts, error } = await supabase
            .from('it_discraft_order_sheet_lines')
            .select('excel_mapping_key, plastic_name, mold_name, min_weight, max_weight, import_file_hash')
            .eq('is_currently_available', true)
            .limit(10);
        
        if (error) throw error;
        
        if (sampleProducts.length === 0) {
            console.log('❌ No available products found in database');
            return;
        }
        
        console.log(`📦 Found ${sampleProducts.length} available products`);
        
        // Check for file changes
        console.log('\n🔍 Checking for file changes...');
        const fileChangeInfo = await detectFileChanges(excelFilePath, sampleProducts[0].import_file_hash);
        
        // Create sample order quantities
        const orderQuantities = sampleProducts.map((product, index) => ({
            excel_mapping_key: product.excel_mapping_key,
            quantity: Math.floor(Math.random() * 10) + 1, // Random quantity 1-10
            product_description: `${product.plastic_name} ${product.mold_name} ${product.min_weight}-${product.max_weight}g`
        }));
        
        console.log('\n📦 Sample order quantities:');
        orderQuantities.forEach(order => {
            console.log(`  ${order.product_description}: ${order.quantity} (Key: ${order.excel_mapping_key})`);
        });
        
        // Write orders to Excel
        console.log('\n📝 Writing orders to Excel...');
        const result = await writeOrdersToExcel(orderQuantities, excelFilePath, outputFilePath);
        
        if (result.success) {
            console.log(`\n🎉 Orders successfully written!`);
            console.log(`📁 Output file: ${result.outputFile}`);
            console.log(`✅ Successful writes: ${result.successfulWrites}`);
            console.log(`❌ Failed writes: ${result.failedWrites}`);
            console.log(`⏭️ Skipped zero quantities: ${result.skippedZeroQuantities}`);
        } else {
            console.log(`\n❌ Failed to write orders: ${result.error}`);
        }
        
    } catch (error) {
        console.error('❌ Error in test:', error);
    }
}

testOrderWriteBack();
