import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

dotenv.config();

const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_KEY;
const supabase = createClient(supabaseUrl, supabaseKey);

async function checkInnovaCount() {
    try {
        // Get total count
        const { count: totalCount, error: countError } = await supabase
            .from('it_innova_order_sheet_lines')
            .select('*', { count: 'exact', head: true });
        
        if (countError) {
            console.error('Error getting count:', countError);
            return;
        }
        
        console.log(`Total Innova records: ${totalCount}`);
        
        // Get first 1000 (what enhanced matching currently sees)
        const { data: first1000, error: dataError } = await supabase
            .from('it_innova_order_sheet_lines')
            .select('internal_id')
            .limit(1000);
        
        if (dataError) {
            console.error('Error getting data:', dataError);
            return;
        }
        
        console.log(`First 1000 records retrieved: ${first1000.length}`);
        
        // Check ID ranges
        const ids = first1000.map(r => r.internal_id).sort((a, b) => a - b);
        console.log(`ID range in first 1000: ${ids[0]} to ${ids[ids.length - 1]}`);
        
        // Check if 6292 is in the first 1000
        const has6292 = first1000.some(r => r.internal_id === 6292);
        console.log(`Does first 1000 include ID 6292? ${has6292 ? 'YES' : 'NO'}`);
        
        if (!has6292) {
            // Find where 6292 actually is
            const { data: record6292, error: findError } = await supabase
                .from('it_innova_order_sheet_lines')
                .select('internal_id, description')
                .eq('internal_id', 6292)
                .single();
            
            if (!findError) {
                console.log(`Record 6292 exists: ${record6292.description}`);
                console.log('❌ But it\'s NOT in the first 1000 records!');
            } else {
                console.log('❌ Record 6292 does not exist at all');
            }
        }
        
    } catch (error) {
        console.error('Check failed:', error);
    }
}

checkInnovaCount();
