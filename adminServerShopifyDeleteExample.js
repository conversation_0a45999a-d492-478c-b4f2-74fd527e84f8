// adminServerShopifyDeleteExample.js
// Example of how to add Shopify deletion endpoint to your adminServer.js

// Add this endpoint to your adminServer.js file

/*
// Endpoint to enqueue a delete SKU from Shopify task
app.post('/api/enqueue-delete-shopify-sku', async (req, res) => {
  console.log('[adminServer] POST /api/enqueue-delete-shopify-sku');

  try {
    const { sku, reason } = req.body;

    if (!sku) {
      return res.status(400).json({
        success: false,
        error: 'SKU parameter is required'
      });
    }

    // Import the enqueue function
    const { enqueueDeleteSkuFromShopifyTask } = await import('./processDeleteSkuFromShopifyTask.js');

    // Enqueue the task
    const task = await enqueueDeleteSkuFromShopifyTask(
      supabase,
      sku,
      reason
    );

    console.log(`[adminServer] Successfully enqueued Shopify deletion task ${task.id} for SKU: ${sku}`);

    return res.json({
      success: true,
      message: `Successfully enqueued Shopify deletion task for SKU: ${sku}`,
      taskId: task.id,
      sku: sku,
      reason: reason || 'Manual deletion via admin interface'
    });

  } catch (error) {
    console.error(`[adminServer] Error enqueueing Shopify deletion task:`, error);
    return res.status(500).json({
      success: false,
      error: error.message
    });
  }
});
*/

// Example HTML form for admin interface (add to admin.html)
const exampleHtmlForm = `
<!-- Add this to your admin.html file -->
<div class="section">
  <h3>Delete Product from Shopify</h3>
  <form id="deleteShopifySkuForm">
    <div class="form-group">
      <label for="shopifyDeleteSku">SKU:</label>
      <input type="text" id="shopifyDeleteSku" name="sku" required placeholder="e.g., D12345">
    </div>
    <div class="form-group">
      <label for="shopifyDeleteReason">Reason (optional):</label>
      <input type="text" id="shopifyDeleteReason" name="reason" placeholder="e.g., Product discontinued">
    </div>
    <button type="submit">Delete from Shopify</button>
  </form>
  <div id="shopifyDeleteResult"></div>
</div>

<script>
document.getElementById('deleteShopifySkuForm').addEventListener('submit', async function(e) {
  e.preventDefault();
  
  const formData = new FormData(e.target);
  const sku = formData.get('sku');
  const reason = formData.get('reason');
  
  const resultDiv = document.getElementById('shopifyDeleteResult');
  resultDiv.innerHTML = 'Processing...';
  
  try {
    const response = await fetch('/api/enqueue-delete-shopify-sku', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ sku, reason })
    });
    
    const result = await response.json();
    
    if (result.success) {
      resultDiv.innerHTML = \`
        <div class="success">
          ✅ Successfully enqueued deletion task for SKU: \${result.sku}<br>
          Task ID: \${result.taskId}<br>
          Reason: \${result.reason}
        </div>
      \`;
      e.target.reset();
    } else {
      resultDiv.innerHTML = \`<div class="error">❌ Error: \${result.error}</div>\`;
    }
  } catch (error) {
    resultDiv.innerHTML = \`<div class="error">❌ Network error: \${error.message}</div>\`;
  }
});
</script>
`;

// Example usage in other parts of your system
const exampleUsageInOtherFunctions = `
// Example 1: In a disc sold webhook handler
async function handleDiscSoldWebhook(discId) {
  // ... update inventory ...
  
  // Check if this was the last disc
  const { data: remainingDiscs } = await supabase
    .from('t_discs')
    .select('id')
    .eq('mps_id', mpsId)
    .is('sold_date', null);
    
  if (remainingDiscs.length === 0) {
    // No more discs available, remove from Shopify
    const sku = \`D\${discId}\`;
    const { enqueueDeleteSkuFromShopifyTask } = await import('./processDeleteSkuFromShopifyTask.js');
    await enqueueDeleteSkuFromShopifyTask(supabase, sku, 'Last disc sold - removing from store');
  }
}

// Example 2: In a batch cleanup process
async function cleanupDiscontinuedProducts() {
  const { data: discontinuedProducts } = await supabase
    .from('t_mps')
    .select('id, sku')
    .eq('active', false)
    .not('shopify_uploaded_at', 'is', null); // Only products that were uploaded to Shopify
    
  const { enqueueDeleteSkuFromShopifyTask } = await import('./processDeleteSkuFromShopifyTask.js');
  
  for (const product of discontinuedProducts) {
    await enqueueDeleteSkuFromShopifyTask(
      supabase, 
      product.sku, 
      'Product marked as inactive - batch cleanup'
    );
  }
  
  console.log(\`Enqueued \${discontinuedProducts.length} products for deletion from Shopify\`);
}

// Example 3: In a product management workflow
async function markProductDiscontinued(mpsId, reason) {
  // Mark as discontinued in database
  await supabase
    .from('t_mps')
    .update({ 
      active: false, 
      discontinued_at: new Date().toISOString(),
      discontinued_reason: reason 
    })
    .eq('id', mpsId);
    
  // Get the SKU
  const { data: mpsRecord } = await supabase
    .from('t_mps')
    .select('sku')
    .eq('id', mpsId)
    .single();
    
  if (mpsRecord?.sku) {
    // Remove from Shopify
    const { enqueueDeleteSkuFromShopifyTask } = await import('./processDeleteSkuFromShopifyTask.js');
    await enqueueDeleteSkuFromShopifyTask(
      supabase, 
      mpsRecord.sku, 
      \`Product discontinued: \${reason}\`
    );
  }
}
`;

console.log('Example integration code for adminServer.js:');
console.log(exampleHtmlForm);
console.log('\nExample usage in other functions:');
console.log(exampleUsageInOtherFunctions);

export { exampleHtmlForm, exampleUsageInOtherFunctions };
