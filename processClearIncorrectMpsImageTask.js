// processClearIncorrectMpsImageTask.js - Process clear_incorrect_mps_image tasks
import { S3Client, DeleteObjectCommand } from '@aws-sdk/client-s3';
import dotenv from 'dotenv';
dotenv.config();

// S3 Configuration (using same config as renameImages.js)
const DEFAULT_CONFIG = {
  S3_REGION: process.env.S3_REGION || 'us-east-1',
  S3_ACCESS_KEY_ID: process.env.S3_ACCESS_KEY_ID,
  S3_SECRET_ACCESS_KEY: process.env.S3_SECRET_ACCESS_KEY,
  S3_BUCKET: process.env.S3_BUCKET || 'discgolfunited'
};

// Initialize S3 client
const s3Client = new S3Client({
  region: DEFAULT_CONFIG.S3_REGION,
  credentials: {
    accessKeyId: DEFAULT_CONFIG.S3_ACCESS_KEY_ID,
    secretAccessKey: DEFAULT_CONFIG.S3_SECRET_ACCESS_KEY
  }
});

/**
 * Process a clear_incorrect_mps_image task
 * @param {Object} task - The task object from the task queue
 * @param {Object} options - Options including supabase client and helper functions
 * @returns {Promise<void>}
 */
async function processClearIncorrectMpsImageTask(task, { supabase, updateTaskStatus, logError }) {
  console.log(`[processClearIncorrectMpsImageTask.js] Processing task ${task.id} of type ${task.task_type}`);

  const startTime = new Date();

  try {
    // Parse the payload
    let payload;
    try {
      if (typeof task.payload === 'string') {
        payload = JSON.parse(task.payload);
      } else {
        payload = task.payload;
      }
    } catch (err) {
      const errMsg = `[processClearIncorrectMpsImageTask.js] Error parsing payload for task ${task.id}: ${err.message}`;
      console.error(errMsg);
      await logError(errMsg, `Parsing payload for task ${task.id}`);

      await updateTaskStatus(task.id, 'error', {
        message: "Failed to parse task payload",
        error: err.message
      });
      return;
    }

    // Validate the payload
    if (!payload || !payload.id) {
      const errMsg = `[processClearIncorrectMpsImageTask.js] Invalid payload for task ${task.id}: Missing id`;
      console.error(errMsg);
      await logError(errMsg, `Validating payload for task ${task.id}`);

      await updateTaskStatus(task.id, 'error', {
        message: "Invalid payload: Missing id",
        payload: payload
      });
      return;
    }

    const mpsId = payload.id;
    console.log(`[processClearIncorrectMpsImageTask.js] Processing MPS ID: ${mpsId}`);

    // Update task to 'processing' status
    await updateTaskStatus(task.id, 'processing');

    // Step 1: Find and delete the t_images record
    console.log(`[processClearIncorrectMpsImageTask.js] Looking for t_images record with table_name='t_mps' and record_id=${mpsId}`);
    
    // First, check if the record exists
    const { data: existingRecord, error: checkError } = await supabase
      .from('t_images')
      .select('id')
      .eq('table_name', 't_mps')
      .eq('record_id', parseInt(mpsId))
      .maybeSingle();

    if (checkError) {
      const errMsg = `[processClearIncorrectMpsImageTask.js] Error checking for existing t_images record: ${checkError.message}`;
      console.error(errMsg);
      await logError(errMsg, `Checking t_images for MPS ID ${mpsId}`);
      
      await updateTaskStatus(task.id, 'error', {
        message: `Error checking for existing t_images record: ${checkError.message}`,
        id: mpsId
      });
      return;
    }

    let databaseDeleted = false;
    if (existingRecord) {
      console.log(`[processClearIncorrectMpsImageTask.js] Found t_images record with id=${existingRecord.id}, deleting...`);
      
      // Delete the record
      const { error: deleteError } = await supabase
        .from('t_images')
        .delete()
        .eq('table_name', 't_mps')
        .eq('record_id', parseInt(mpsId));

      if (deleteError) {
        const errMsg = `[processClearIncorrectMpsImageTask.js] Error deleting t_images record: ${deleteError.message}`;
        console.error(errMsg);
        await logError(errMsg, `Deleting t_images for MPS ID ${mpsId}`);
        
        await updateTaskStatus(task.id, 'error', {
          message: `Error deleting t_images record: ${deleteError.message}`,
          id: mpsId
        });
        return;
      }
      
      console.log(`[processClearIncorrectMpsImageTask.js] Successfully deleted t_images record for MPS ID ${mpsId}`);
      databaseDeleted = true;
    } else {
      console.log(`[processClearIncorrectMpsImageTask.js] No t_images record found for MPS ID ${mpsId}`);
    }

    // Step 2: Delete the file from S3
    const s3Key = `mps/${mpsId}.jpg`;
    console.log(`[processClearIncorrectMpsImageTask.js] Attempting to delete S3 file: ${s3Key}`);
    
    let s3Deleted = false;
    let s3Error = null;
    
    try {
      const deleteParams = {
        Bucket: DEFAULT_CONFIG.S3_BUCKET,
        Key: s3Key
      };

      const command = new DeleteObjectCommand(deleteParams);
      await s3Client.send(command);
      
      console.log(`[processClearIncorrectMpsImageTask.js] Successfully deleted S3 file: ${s3Key}`);
      s3Deleted = true;
    } catch (error) {
      // S3 delete operations don't fail if the file doesn't exist, but other errors might occur
      s3Error = error.message;
      console.warn(`[processClearIncorrectMpsImageTask.js] Warning during S3 delete for ${s3Key}: ${error.message}`);
      // We'll still consider this a success since the file might not have existed
      s3Deleted = true;
    }

    // Calculate processing time
    const endTime = new Date();
    const processingTimeMs = endTime - startTime;
    const processingTimeSec = (processingTimeMs / 1000).toFixed(2);

    // Update task status to completed
    const result = {
      message: `Successfully processed clear_incorrect_mps_image for MPS ID ${mpsId}`,
      id: mpsId,
      database_record_found: !!existingRecord,
      database_deleted: databaseDeleted,
      s3_file_deleted: s3Deleted,
      s3_key: s3Key,
      processing_time_ms: processingTimeMs,
      processing_time_sec: parseFloat(processingTimeSec)
    };

    if (s3Error) {
      result.s3_warning = s3Error;
    }

    await updateTaskStatus(task.id, 'completed', result);
    
    console.log(`[processClearIncorrectMpsImageTask.js] Task ${task.id} completed successfully`);

  } catch (err) {
    // Calculate processing time even for errors
    const endTime = new Date();
    const processingTimeMs = endTime - startTime;
    const processingTimeSec = (processingTimeMs / 1000).toFixed(2);

    const errMsg = `[processClearIncorrectMpsImageTask.js] Exception while processing task ${task.id}: ${err.message}`;
    console.error(errMsg);
    await logError(errMsg, `Processing task ${task.id}`);

    await updateTaskStatus(task.id, 'error', {
      message: "Failed to process clear_incorrect_mps_image task due to an unexpected error",
      error: err.message,
      id: payload?.id || 'unknown',
      processing_time_ms: processingTimeMs,
      processing_time_sec: parseFloat(processingTimeSec)
    });
  }
}

export default processClearIncorrectMpsImageTask;
