import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

dotenv.config();

const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_KEY;
const supabase = createClient(supabaseUrl, supabaseKey);

async function getAllRecords() {
    console.log('📊 Fetching ALL records using chunked queries...');
    
    const allRecords = [];
    let offset = 0;
    const chunkSize = 1000;
    
    while (true) {
        const { data: chunk, error } = await supabase
            .from('it_discraft_order_sheet_lines')
            .select('*')
            .range(offset, offset + chunkSize - 1)
            .order('id');
        
        if (error) throw error;
        
        if (chunk.length === 0) break;
        
        allRecords.push(...chunk);
        console.log(`  📦 Fetched chunk: ${allRecords.length} total records`);
        
        if (chunk.length < chunkSize) break; // Last chunk
        
        offset += chunkSize;
    }
    
    return allRecords;
}

async function properFullAnalysis() {
    try {
        console.log('🔍 Proper full analysis with chunked queries...\n');
        
        // Get total count
        const { count: totalCount, error: countError } = await supabase
            .from('it_discraft_order_sheet_lines')
            .select('*', { count: 'exact', head: true });
        
        if (countError) throw countError;
        
        console.log(`📊 Total records in database: ${totalCount}`);
        
        // Get ALL records using chunked queries
        const allRecords = await getAllRecords();
        
        console.log(`\n✅ Successfully retrieved all ${allRecords.length} records`);
        
        // Proper analysis
        console.log('\n📊 CORRECT Import Summary:');
        
        const byPlastic = {};
        const byMold = {};
        let availableCount = 0;
        let outOfStockCount = 0;
        
        allRecords.forEach(record => {
            byPlastic[record.plastic_name] = (byPlastic[record.plastic_name] || 0) + 1;
            byMold[record.mold_name] = (byMold[record.mold_name] || 0) + 1;
            
            if (record.is_currently_available) {
                availableCount++;
            } else {
                outOfStockCount++;
            }
        });
        
        console.log('\n🎯 By Plastic Type (Top 10):');
        Object.entries(byPlastic)
            .sort(([,a], [,b]) => b - a)
            .slice(0, 10)
            .forEach(([plastic, count]) => {
                console.log(`  ${plastic}: ${count} products`);
            });
        
        console.log('\n🥏 By Mold (Top 15):');
        Object.entries(byMold)
            .sort(([,a], [,b]) => b - a)
            .slice(0, 15)
            .forEach(([mold, count]) => {
                console.log(`  ${mold}: ${count} products`);
            });
        
        console.log('\n📈 By Availability:');
        console.log(`  ✅ Available now: ${availableCount} products`);
        console.log(`  ❌ Out of stock: ${outOfStockCount} products`);
        console.log(`  📊 Total: ${allRecords.length} products`);
        
        // Check mapping key uniqueness
        console.log('\n🔑 Mapping Key Analysis:');
        const mappingKeys = allRecords.map(r => r.excel_mapping_key);
        const keySet = new Set(mappingKeys);
        const duplicateCount = mappingKeys.length - keySet.size;
        
        console.log(`  📊 Total products: ${mappingKeys.length}`);
        console.log(`  🔑 Unique mapping keys: ${keySet.size}`);
        console.log(`  🔄 Duplicate keys: ${duplicateCount}`);
        
        if (duplicateCount > 0) {
            console.log('  ⚠️ Some products have identical mapping keys');
            
            // Find duplicates
            const keyCount = {};
            mappingKeys.forEach(key => {
                keyCount[key] = (keyCount[key] || 0) + 1;
            });
            
            const duplicates = Object.entries(keyCount).filter(([key, count]) => count > 1);
            console.log('\n🔄 Duplicate mapping keys:');
            duplicates.slice(0, 5).forEach(([key, count]) => {
                console.log(`  "${key}": ${count} occurrences`);
            });
        } else {
            console.log('  ✅ All mapping keys are unique!');
        }
        
        // Row distribution
        console.log('\n📊 Row Distribution:');
        const rowHints = allRecords.map(r => r.excel_row_hint).sort((a, b) => a - b);
        console.log(`  📊 Row range: ${rowHints[0]} to ${rowHints[rowHints.length - 1]}`);
        console.log(`  📊 Rows with products: ${new Set(rowHints).size} unique rows`);
        
        // Check problematic parsing
        console.log('\n🚨 Parsing Issues Summary:');
        const problematicMolds = ['NEW', 'PM', 'Retired', 'Discontinued', 'SPECIAL', 'Unknown'];
        
        problematicMolds.forEach(moldName => {
            const count = allRecords.filter(r => r.mold_name === moldName).length;
            if (count > 0) {
                console.log(`  ❌ "${moldName}": ${count} products need fixing`);
            }
        });
        
    } catch (error) {
        console.error('❌ Error in proper analysis:', error);
    }
}

properFullAnalysis();
