// Debug RocX3 parsing issue

function parseInnovaDescription(description) {
    if (!description) return null;
    
    // Common Innova plastic types (order matters - longer names first)
    const plasticTypes = [
        'Champion I-Dye', 'Champion Proto Glow', 'Champion Classic Glow', 'Champion', 
        'Star I-Dye', 'Halo Star', 'Star', 
        'DX Proto Glow', 'DX Classic Glow', 'DX', 
        'Pro KC', 'KC Pro', 'R-Pro', 'Pro',
        'Halo Nexus', 'Nexus',
        'Metal Flake Champion', 'GStar', 'G-Star', 'XT'
    ];
    
    let plastic = null;
    let remainingText = description.trim();
    
    // Find plastic type
    for (const plasticType of plasticTypes) {
        if (description.toLowerCase().includes(plasticType.toLowerCase())) {
            plastic = plasticType;
            // Remove plastic from description to get mold
            remainingText = description.replace(new RegExp(plasticType, 'gi'), '').trim();
            break;
        }
    }
    
    // Extract mold name (remove Innova disc type classifications - these are ignored for matching)
    let mold = remainingText
        .replace(/\s+(Putt & Approach|Distance Driver|Fairway Driver|Mid-Range|Midrange|Putter|Driver|Approach)(\s+Disc)?$/i, '')
        .replace(/\s+Disc$/i, '') // Remove standalone "Disc" at the end
        .replace(/\s+with\s+.*/i, '') // Remove "with Burst Logo" etc
        .trim();
    
    return {
        plastic: plastic,
        mold: mold,
        original: description
    };
}

// Test the specific RocX3 case
const testCases = [
    'XT RocX3 Midrange Disc',
    'XT RocX3 Mid-Range',
    'Champion RocX3 Midrange',
    'DX RocX3 Midrange Disc'
];

console.log('=== ROCX3 PARSING DEBUG ===\n');

testCases.forEach(description => {
    const result = parseInnovaDescription(description);
    console.log(`"${description}"`);
    console.log(`  → Plastic: "${result?.plastic || 'NOT FOUND'}"`);
    console.log(`  → Mold: "${result?.mold || 'NOT FOUND'}"`);
    console.log('');
});

// Step by step for the specific case
console.log('=== STEP BY STEP: "XT RocX3 Midrange Disc" ===');
const description = 'XT RocX3 Midrange Disc';

console.log(`1. Original: "${description}"`);

// Remove XT
let step1 = description.replace(new RegExp('XT', 'gi'), '').trim();
console.log(`2. Remove XT: "${step1}"`);

// Remove Midrange
let step2 = step1.replace(/\s+(Putt & Approach|Distance Driver|Fairway Driver|Mid-Range|Midrange|Putter|Driver|Approach)$/i, '').trim();
console.log(`3. Remove Midrange: "${step2}"`);

// The issue might be "Disc" at the end
let step3 = step2.replace(/\s+Disc$/i, '').trim();
console.log(`4. Remove "Disc": "${step3}"`);
