import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

dotenv.config();

const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_KEY;
const supabase = createClient(supabaseUrl, supabaseKey);

async function testPhase2Simple() {
    try {
        console.log('Testing Phase 2 components...');
        
        // 1. Test OSL query
        console.log('1. Testing OSL query...');
        const { data: oslData, error: oslError } = await supabase
            .from('t_order_sheet_lines')
            .select(`
                id, g_code, min_weight, max_weight, color_id, mps_id,
                t_mps!inner (
                    id, g_code, active,
                    t_molds!inner (id, mold),
                    t_plastics!inner (id, plastic),
                    t_stamps!inner (id, stamp)
                ),
                t_colors!inner (id, color)
            `)
            .eq('vendor_id', 2)
            .is('vendor_internal_id', null)
            .or('vendor_match_verified.is.null,vendor_match_verified.eq.false')
            .eq('t_mps.active', true)
            .range(0, 2); // Just 3 OSLs

        if (oslError) {
            console.error('OSL query error:', oslError);
            return;
        }

        console.log(`✅ Found ${oslData.length} OSLs`);
        
        // 2. Test Innova query (small sample)
        console.log('2. Testing Innova query...');
        const { data: innovaData, error: innovaError } = await supabase
            .from('it_innova_order_sheet_lines')
            .select('internal_id, description, matrix_option_1')
            .not('matrix_option_1', 'is', null)
            .range(0, 99); // Just 100 records

        if (innovaError) {
            console.error('Innova query error:', innovaError);
            return;
        }

        console.log(`✅ Found ${innovaData.length} Innova records`);
        
        // 3. Test parsing
        console.log('3. Testing parsing...');
        if (innovaData.length > 0) {
            const { parseInnovaDescription } = await import('./phase2InnovaMatching.js');
            const parsed = parseInnovaDescription(innovaData[0].description);
            console.log(`Sample: "${innovaData[0].description}"`);
            console.log(`Parsed: Plastic="${parsed?.plastic}", Mold="${parsed?.mold}"`);
        }
        
        console.log('\n✅ All components working! Phase 2 should work now.');
        
    } catch (error) {
        console.error('Test failed:', error);
    }
}

testPhase2Simple();
