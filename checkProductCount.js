import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

dotenv.config();

const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_KEY;
const supabase = createClient(supabaseUrl, supabaseKey);

async function checkProductCount() {
    try {
        console.log('🔍 Checking product count...\n');
        
        // Get total count
        const { count: totalProducts, error: totalError } = await supabase
            .from('it_discraft_order_sheet_lines')
            .select('*', { count: 'exact', head: true });
        
        if (totalError) {
            console.error('Error getting total count:', totalError);
            return;
        }
        
        console.log(`Total products in database: ${totalProducts}`);
        
        // Get products with select
        const { data: products, error: productsError } = await supabase
            .from('it_discraft_order_sheet_lines')
            .select('id');
        
        if (productsError) {
            console.error('Error getting products:', productsError);
            return;
        }
        
        console.log(`Products returned by select: ${products.length}`);
        
        if (products.length !== totalProducts) {
            console.log('⚠️ Mismatch! Supabase is limiting the select query to 1000 records');
            console.log('Need to use chunking approach for the MPS calculation');
        } else {
            console.log('✅ All products returned correctly');
        }
        
    } catch (error) {
        console.error('❌ Check error:', error);
    }
}

checkProductCount();
