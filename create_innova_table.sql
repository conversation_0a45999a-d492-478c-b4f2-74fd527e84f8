-- Create table for Innova order sheet lines import
CREATE TABLE IF NOT EXISTS public.it_innova_order_sheet_lines (
    id SERIAL PRIMARY KEY,
    category TEXT,
    sub_category TEXT,
    description TEXT,
    availability INTEGER,
    sku TEXT,
    internal_id INTEGER,
    parent_id INTEGER,
    ordered INTEGER,
    matrix_type TEXT,
    matrix_option_1 TEXT,
    matrix_option_2 TEXT,
    base_price NUMERIC(10,2),
    dealer_25 NUMERIC(10,2),
    dealer_50 NUMERIC(10,2),
    dealer_100 NUMERIC(10,2),
    dealer_200 NUMERIC(10,2),
    dealer_500 NUMERIC(10,2),
    current_price NUMERIC(10,2),
    total_price NUMERIC(10,2),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    import_batch_id UUID DEFAULT gen_random_uuid()
);

-- Create indexes for common search fields
CREATE INDEX IF NOT EXISTS idx_innova_order_sku ON public.it_innova_order_sheet_lines(sku);
CREATE INDEX IF NOT EXISTS idx_innova_order_internal_id ON public.it_innova_order_sheet_lines(internal_id);
CREATE INDEX IF NOT EXISTS idx_innova_order_parent_id ON public.it_innova_order_sheet_lines(parent_id);
CREATE INDEX IF NOT EXISTS idx_innova_order_category ON public.it_innova_order_sheet_lines(category);
CREATE INDEX IF NOT EXISTS idx_innova_order_matrix_type ON public.it_innova_order_sheet_lines(matrix_type);
CREATE INDEX IF NOT EXISTS idx_innova_order_import_batch ON public.it_innova_order_sheet_lines(import_batch_id);

-- Add comments for documentation
COMMENT ON TABLE public.it_innova_order_sheet_lines IS 'Imported data from Innova order form Excel files';
COMMENT ON COLUMN public.it_innova_order_sheet_lines.internal_id IS 'Innova internal product ID';
COMMENT ON COLUMN public.it_innova_order_sheet_lines.parent_id IS 'Parent product ID for matrix items';
COMMENT ON COLUMN public.it_innova_order_sheet_lines.matrix_type IS 'Type: _parent, _child, or null';
COMMENT ON COLUMN public.it_innova_order_sheet_lines.import_batch_id IS 'UUID to track import batches';
