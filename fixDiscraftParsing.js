import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

dotenv.config();

const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_KEY;
const supabase = createClient(supabaseUrl, supabaseKey);

async function fixDiscraftParsing() {
    try {
        console.log('🔧 Fixing Discraft parsing issues...\n');
        
        // 1. Fix "Soft" plastic mapping
        console.log('1. Fixing "Soft" plastic mapping...');
        const { data: softProducts, error: softError } = await supabase
            .from('it_discraft_order_sheet_lines')
            .select('id, plastic_name, mold_name, stamp_name')
            .eq('plastic_name', 'Soft')
            .is('calculated_mps_id', null);
        
        if (softError) {
            console.error('Error getting Soft products:', softError);
        } else {
            console.log(`Found ${softProducts.length} products with "Soft" plastic`);
            
            // Update "Soft" to "Elite X Soft" (most common Discraft soft plastic)
            for (const product of softProducts) {
                const { error: updateError } = await supabase
                    .from('it_discraft_order_sheet_lines')
                    .update({ plastic_name: 'Elite X Soft' })
                    .eq('id', product.id);
                
                if (updateError) {
                    console.error(`Error updating product ${product.id}:`, updateError);
                } else {
                    console.log(`✅ Updated product ${product.id}: "Soft" → "Elite X Soft"`);
                }
            }
        }
        
        // 2. Fix long description plastics
        console.log('\n2. Fixing long description plastics...');
        
        // Fix "Spark Line: ..." → "Spark Line"
        const { data: sparkProducts, error: sparkError } = await supabase
            .from('it_discraft_order_sheet_lines')
            .select('id, plastic_name')
            .ilike('plastic_name', 'Spark Line:%');
        
        if (!sparkError && sparkProducts) {
            for (const product of sparkProducts) {
                const { error: updateError } = await supabase
                    .from('it_discraft_order_sheet_lines')
                    .update({ plastic_name: 'Spark Line' })
                    .eq('id', product.id);
                
                if (!updateError) {
                    console.log(`✅ Updated product ${product.id}: "${product.plastic_name}" → "Spark Line"`);
                }
            }
        }
        
        // Fix "D-Line: ..." → "D-Line"
        const { data: dlineProducts, error: dlineError } = await supabase
            .from('it_discraft_order_sheet_lines')
            .select('id, plastic_name')
            .ilike('plastic_name', 'D-Line:%');
        
        if (!dlineError && dlineProducts) {
            for (const product of dlineProducts) {
                const { error: updateError } = await supabase
                    .from('it_discraft_order_sheet_lines')
                    .update({ plastic_name: 'D-Line' })
                    .eq('id', product.id);
                
                if (!updateError) {
                    console.log(`✅ Updated product ${product.id}: "${product.plastic_name}" → "D-Line"`);
                }
            }
        }
        
        // 3. Remove product sets (multi-disc packs)
        console.log('\n3. Removing product sets...');
        const { data: setProducts, error: setError } = await supabase
            .from('it_discraft_order_sheet_lines')
            .select('id, mold_name, vendor_description')
            .or('mold_name.ilike.%pack%,mold_name.ilike.%set%,vendor_description.ilike.%pack%,vendor_description.ilike.%set%');
        
        if (!setError && setProducts) {
            console.log(`Found ${setProducts.length} product sets to remove`);
            
            for (const product of setProducts) {
                const { error: deleteError } = await supabase
                    .from('it_discraft_order_sheet_lines')
                    .delete()
                    .eq('id', product.id);
                
                if (!deleteError) {
                    console.log(`✅ Removed product set ${product.id}: "${product.mold_name}"`);
                }
            }
        }
        
        // 4. Check what "Tour" should map to
        console.log('\n4. Analyzing "Tour" plastic...');
        const { data: tourProducts, error: tourError } = await supabase
            .from('it_discraft_order_sheet_lines')
            .select('id, plastic_name, mold_name, stamp_name')
            .eq('plastic_name', 'Tour')
            .limit(5);
        
        if (!tourError && tourProducts) {
            console.log(`Found ${tourProducts.length} products with "Tour" plastic:`);
            tourProducts.forEach(product => {
                console.log(`   "${product.plastic_name}" | "${product.mold_name}" | "${product.stamp_name}"`);
            });
            
            // Check if there's a similar plastic in your database
            const { data: dbPlastics, error: dbError } = await supabase
                .from('t_plastics')
                .select('plastic')
                .eq('brand_id', 6)
                .ilike('plastic', '%tour%');
            
            if (!dbError && dbPlastics && dbPlastics.length > 0) {
                console.log('   Potential matches in your database:');
                dbPlastics.forEach(plastic => {
                    console.log(`   → "${plastic.plastic}"`);
                });
            } else {
                console.log('   ❌ No "Tour" plastic found in your database');
                console.log('   💡 You may need to create a "Tour" plastic or map it to an existing one');
            }
        }
        
        // 5. Recalculate MPS IDs after fixes
        console.log('\n5. Recalculating MPS IDs after fixes...');
        
        // Get all products for matching
        const { data: allProducts, error: allError } = await supabase
            .from('it_discraft_order_sheet_lines')
            .select('id, plastic_name, mold_name, stamp_name');
        
        if (allError) {
            console.error('Error getting all products:', allError);
            return;
        }
        
        // Get Discraft MPS records
        const { data: mpsRecords, error: mpsError } = await supabase
            .from('t_mps')
            .select(`id, t_plastics!inner(plastic, brand_id), t_molds!inner(mold, brand_id), t_stamps!inner(stamp)`)
            .eq('active', true)
            .eq('t_plastics.brand_id', 6)
            .eq('t_molds.brand_id', 6);
        
        if (mpsError) {
            console.error('Error getting MPS records:', mpsError);
            return;
        }
        
        // Create lookup map
        const mpsMap = new Map();
        mpsRecords.forEach(mps => {
            const key = `${mps.t_plastics.plastic.trim()}|${mps.t_molds.mold.trim()}|${mps.t_stamps.stamp.trim()}`;
            mpsMap.set(key, mps.id);
        });
        
        // Match products
        let newMatches = 0;
        for (const product of allProducts) {
            if (product.calculated_mps_id) continue; // Skip already matched
            
            const key = `${product.plastic_name.trim()}|${product.mold_name.trim()}|${product.stamp_name.trim()}`;
            const mpsId = mpsMap.get(key);
            
            if (mpsId) {
                const { error } = await supabase
                    .from('it_discraft_order_sheet_lines')
                    .update({ calculated_mps_id: mpsId })
                    .eq('id', product.id);
                
                if (!error) {
                    newMatches++;
                }
            }
        }
        
        console.log(`✅ Found ${newMatches} new matches after parsing fixes`);
        
        // Final statistics
        const { count: totalProducts } = await supabase
            .from('it_discraft_order_sheet_lines')
            .select('*', { count: 'exact', head: true });
        
        const { count: matchedProducts } = await supabase
            .from('it_discraft_order_sheet_lines')
            .select('*', { count: 'exact', head: true })
            .not('calculated_mps_id', 'is', null);
        
        const successRate = ((matchedProducts / totalProducts) * 100).toFixed(2);
        
        console.log('\n📊 Final Results:');
        console.log(`   • Total products: ${totalProducts}`);
        console.log(`   • Matched products: ${matchedProducts}`);
        console.log(`   • Success rate: ${successRate}%`);
        
        if (successRate > 10) {
            console.log('🎉 Success rate improved!');
        }
        
    } catch (error) {
        console.error('❌ Error fixing parsing:', error);
    }
}

fixDiscraftParsing();
