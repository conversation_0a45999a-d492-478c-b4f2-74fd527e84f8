// Debug script to check why Rhyno mold isn't matching

// Parse Innova description to extract mold and plastic
function parseInnovaDescription(description) {
    if (!description) return null;
    
    // Common Innova plastic types (order matters - longer names first)
    const plasticTypes = [
        'Champion I-Dye', 'Champion Proto Glow', 'Champion Classic Glow', 'Champion', 
        'Star I-Dye', 'Halo Star', 'Star', 
        'DX Proto Glow', 'DX Classic Glow', 'DX', 
        'Pro KC', 'KC Pro', 'R-Pro', 'Pro',
        'Halo Nexus', 'Nexus',
        'Metal Flake Champion', 'GStar', 'G-Star', 'XT'
    ];
    
    let plastic = null;
    let remainingText = description.trim();
    
    // Find plastic type
    for (const plasticType of plasticTypes) {
        if (description.toLowerCase().includes(plasticType.toLowerCase())) {
            plastic = plasticType;
            // Remove plastic from description to get mold
            remainingText = description.replace(new RegExp(plasticType, 'gi'), '').trim();
            break;
        }
    }
    
    // Extract mold name (remove Innova disc type classifications - these are ignored for matching)
    let mold = remainingText
        .replace(/\s+(Putt & Approach|Distance Driver|Fairway Driver|Mid-Range|Midrange|Putter|Driver|Approach)$/i, '')
        .replace(/\s+with\s+.*/i, '') // Remove "with Burst Logo" etc
        .trim();
    
    return {
        plastic: plastic,
        mold: mold,
        original: description
    };
}

// Test cases
const testCases = [
    'DX Rhyno Putt & Approach',
    'Champion Rhyno Putt & Approach',
    'Star Rhyno Putt & Approach',
    'DX Katana Distance Driver',
    'Champion Eagle Fairway Driver',
    'DX Classic Glow TL Fairway Driver'
];

console.log('=== INNOVA DESCRIPTION PARSING TEST ===\n');

testCases.forEach(description => {
    const result = parseInnovaDescription(description);
    console.log(`"${description}"`);
    console.log(`  → Plastic: "${result?.plastic || 'NOT FOUND'}"`);
    console.log(`  → Mold: "${result?.mold || 'NOT FOUND'}"`);
    console.log(`  → Remaining: "${result?.mold}"`);
    console.log('');
});

// Specific Rhyno test
console.log('=== RHYNO SPECIFIC DEBUG ===');
const rhynoDescription = 'DX Rhyno Putt & Approach';
const parsed = parseInnovaDescription(rhynoDescription);

console.log(`Original: "${rhynoDescription}"`);
console.log(`Parsed plastic: "${parsed?.plastic}"`);
console.log(`Parsed mold: "${parsed?.mold}"`);

// Step by step parsing
console.log('\n=== STEP BY STEP ===');
let step1 = rhynoDescription.trim();
console.log(`1. Start: "${step1}"`);

let step2 = step1.replace(new RegExp('DX', 'gi'), '').trim();
console.log(`2. Remove DX: "${step2}"`);

let step3 = step2.replace(/\s+(Mid-Range|Distance Driver|Fairway Driver|Putter|Driver|Midrange|Approach)$/i, '').trim();
console.log(`3. Remove suffix: "${step3}"`);

// Check if "Putt &" is the issue
let step4 = step2.replace(/\s+(Putt & Approach|Mid-Range|Distance Driver|Fairway Driver|Putter|Driver|Midrange|Approach)$/i, '').trim();
console.log(`4. Remove "Putt & Approach": "${step4}"`);
