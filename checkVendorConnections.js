import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

// Initialize Supabase client
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_KEY;
const supabase = createClient(supabaseUrl, supabaseKey);

async function checkVendorConnections() {
    try {
        console.log('Checking vendor connections in t_order_sheet_lines...\n');
        
        // Get counts by vendor_id using count queries to avoid 1000 limit
        console.log('Getting vendor connection counts...');

        // Get total counts per vendor
        const { data: vendorList, error: vendorListError } = await supabase
            .from('t_order_sheet_lines')
            .select('vendor_id')
            .not('vendor_id', 'is', null);

        if (vendorListError) {
            throw new Error(`Error fetching vendor list: ${vendorListError.message}`);
        }
        
        if (vendorError) {
            throw new Error(`Error fetching vendor data: ${vendorError.message}`);
        }
        
        // Group by vendor_id and count connections
        const vendorStats = {};
        vendorCounts.forEach(record => {
            const vendorId = record.vendor_id || 'NULL';
            if (!vendorStats[vendorId]) {
                vendorStats[vendorId] = {
                    total: 0,
                    withConnection: 0,
                    withoutConnection: 0
                };
            }
            vendorStats[vendorId].total++;
            if (record.vendor_internal_id) {
                vendorStats[vendorId].withConnection++;
            } else {
                vendorStats[vendorId].withoutConnection++;
            }
        });
        
        console.log('=== VENDOR CONNECTION SUMMARY ===');
        Object.entries(vendorStats).forEach(([vendorId, stats]) => {
            console.log(`Vendor ID ${vendorId}:`);
            console.log(`  Total OSLs: ${stats.total}`);
            console.log(`  With vendor_internal_id: ${stats.withConnection}`);
            console.log(`  Without vendor_internal_id: ${stats.withoutConnection}`);
            console.log('');
        });
        
        // Check specifically for Innova (vendor_id = 2)
        const { data: innovaData, error: innovaError } = await supabase
            .from('t_order_sheet_lines')
            .select('id, vendor_internal_id, g_code, created_at, updated_at')
            .eq('vendor_id', 2)
            .order('updated_at', { ascending: false })
            .limit(10);
        
        if (innovaError) {
            throw new Error(`Error fetching Innova data: ${innovaError.message}`);
        }
        
        console.log('=== RECENT INNOVA OSLs (vendor_id=2) ===');
        innovaData.forEach(record => {
            console.log(`OSL ${record.id}: vendor_internal_id=${record.vendor_internal_id || 'NULL'}, g_code=${record.g_code || 'NULL'}, updated=${record.updated_at}`);
        });
        
        // Check for recently updated records (potential victims of our clearing)
        const { data: recentlyUpdated, error: recentError } = await supabase
            .from('t_order_sheet_lines')
            .select('id, vendor_id, vendor_internal_id, g_code, updated_at')
            .gte('updated_at', '2025-06-05T15:30:00')  // Around the time we ran validation
            .is('vendor_internal_id', null)
            .order('updated_at', { ascending: false });
        
        if (recentError) {
            console.log('Could not check recently updated records:', recentError.message);
        } else {
            console.log('\n=== RECENTLY CLEARED CONNECTIONS (since 15:30 today) ===');
            if (recentlyUpdated.length > 0) {
                console.log(`Found ${recentlyUpdated.length} records with NULL vendor_internal_id updated recently:`);
                recentlyUpdated.slice(0, 20).forEach(record => {
                    console.log(`OSL ${record.id}: vendor_id=${record.vendor_id}, g_code=${record.g_code || 'NULL'}, updated=${record.updated_at}`);
                });
                if (recentlyUpdated.length > 20) {
                    console.log(`... and ${recentlyUpdated.length - 20} more`);
                }
            } else {
                console.log('No recently cleared connections found.');
            }
        }
        
        return {
            success: true,
            vendorStats,
            innovaCount: innovaData.length,
            recentlyCleared: recentlyUpdated?.length || 0
        };
        
    } catch (error) {
        console.error('Check failed:', error);
        return {
            success: false,
            error: error.message
        };
    }
}

// Run the check if this file is executed directly
if (process.argv[1] && process.argv[1].endsWith('checkVendorConnections.js')) {
    console.log('Checking vendor connections...');
    checkVendorConnections()
        .then(result => {
            if (result.success) {
                console.log('\nCheck completed successfully');
                process.exit(0);
            } else {
                console.error('Check failed:', result.error);
                process.exit(1);
            }
        })
        .catch(error => {
            console.error('Unexpected error:', error);
            process.exit(1);
        });
}

export { checkVendorConnections };
