import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

dotenv.config();

const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_KEY;
const supabase = createClient(supabaseUrl, supabaseKey);

async function fixTourSeriesWeights() {
    try {
        console.log('🔧 Fixing Tour Series weight ranges to 160-180g...\n');
        
        // Get all Tour Series products (Elite Z Swirl with 2025 Tour Series stamps)
        const { data: tourProducts, error: tourError } = await supabase
            .from('it_discraft_order_sheet_lines')
            .select('id, plastic_name, mold_name, stamp_name, min_weight, max_weight')
            .eq('plastic_name', 'Elite Z Swirl')
            .ilike('stamp_name', '%2025 Tour Series%');
        
        if (tourError) {
            console.error('Error getting Tour Series products:', tourError);
            return;
        }
        
        console.log(`Found ${tourProducts.length} Tour Series products to fix:`);
        
        let fixedCount = 0;
        let alreadyCorrect = 0;
        
        for (const product of tourProducts) {
            const currentRange = `${product.min_weight}-${product.max_weight}g`;
            
            if (product.min_weight === 160 && product.max_weight === 180) {
                console.log(`✅ Already correct: ${product.mold_name} (${currentRange})`);
                alreadyCorrect++;
                continue;
            }
            
            console.log(`🔄 Fixing: ${product.mold_name} from ${currentRange} → 160-180g`);
            
            const { error: updateError } = await supabase
                .from('it_discraft_order_sheet_lines')
                .update({ 
                    min_weight: 160,
                    max_weight: 180
                })
                .eq('id', product.id);
            
            if (updateError) {
                console.error(`❌ Error updating product ${product.id}:`, updateError);
            } else {
                fixedCount++;
            }
        }
        
        console.log(`\n📊 Weight Fix Results:`);
        console.log(`   • Fixed: ${fixedCount} products`);
        console.log(`   • Already correct: ${alreadyCorrect} products`);
        console.log(`   • Total Tour Series: ${tourProducts.length} products`);
        
        // Show summary of current weight ranges
        console.log('\n📋 Current Tour Series Weight Ranges:');
        const uniqueRanges = {};
        tourProducts.forEach(product => {
            const range = `${product.min_weight === 160 && product.max_weight === 180 ? '160-180' : `${product.min_weight}-${product.max_weight}`}g`;
            const mold = product.mold_name;
            if (!uniqueRanges[range]) {
                uniqueRanges[range] = [];
            }
            if (!uniqueRanges[range].includes(mold)) {
                uniqueRanges[range].push(mold);
            }
        });
        
        Object.entries(uniqueRanges).forEach(([range, molds]) => {
            console.log(`   ${range}: ${molds.join(', ')}`);
        });
        
        if (fixedCount > 0) {
            console.log('\n🎉 All Tour Series products now have unified 160-180g weight range!');
        } else {
            console.log('\n✅ All Tour Series products already had correct weight ranges!');
        }
        
        // Verify the fix
        console.log('\n🔍 Verification - checking for any remaining incorrect weights...');
        const { data: incorrectWeights, error: verifyError } = await supabase
            .from('it_discraft_order_sheet_lines')
            .select('id, mold_name, min_weight, max_weight')
            .eq('plastic_name', 'Elite Z Swirl')
            .ilike('stamp_name', '%2025 Tour Series%')
            .or('min_weight.neq.160,max_weight.neq.180');
        
        if (verifyError) {
            console.error('Error verifying weights:', verifyError);
        } else if (incorrectWeights && incorrectWeights.length > 0) {
            console.log(`❌ Still found ${incorrectWeights.length} products with incorrect weights:`);
            incorrectWeights.forEach(product => {
                console.log(`   ${product.mold_name}: ${product.min_weight}-${product.max_weight}g`);
            });
        } else {
            console.log('✅ Verification passed - all Tour Series products have 160-180g range!');
        }
        
    } catch (error) {
        console.error('❌ Error fixing Tour Series weights:', error);
    }
}

fixTourSeriesWeights();
