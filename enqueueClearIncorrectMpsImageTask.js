// enqueueClearIncorrectMpsImageTask.js - Helper to enqueue clear_incorrect_mps_image tasks
import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';
dotenv.config();

// Initialize Supabase client
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY;
const supabase = createClient(supabaseUrl, supabaseKey);

/**
 * Enqueue a clear_incorrect_mps_image task
 * @param {Object} supabaseClient - Supabase client instance (optional, will use default if not provided)
 * @param {number|string} mpsId - The MPS ID to clear the image for
 * @param {string} reason - Optional reason for the deletion
 * @param {Date} scheduledAt - Optional scheduled time (defaults to 5 minutes from now)
 * @returns {Promise<Object>} - The created task object
 */
export async function enqueueClearIncorrectMpsImageTask(supabaseClient = null, mpsId, reason = null, scheduledAt = null) {
  const client = supabaseClient || supabase;

  if (!mpsId) {
    throw new Error('MPS ID is required');
  }

  const now = new Date();
  const defaultScheduledAt = new Date(now.getTime() + 5 * 60 * 1000); // 5 minutes from now

  const task = {
    task_type: 'clear_incorrect_mps_image',
    payload: {
      id: parseInt(mpsId),
      reason: reason || 'Clear incorrect MPS image'
    },
    status: 'pending',
    scheduled_at: (scheduledAt || defaultScheduledAt).toISOString(),
    created_at: now.toISOString(),
    enqueued_by: 'enqueueClearIncorrectMpsImageTask'
  };
  
  console.log(`[enqueueClearIncorrectMpsImageTask] Enqueueing clear incorrect MPS image task for MPS ID: ${mpsId}`);
  
  const { data, error } = await client
    .from('t_task_queue')
    .insert(task)
    .select()
    .single();
  
  if (error) {
    console.error(`[enqueueClearIncorrectMpsImageTask] Error enqueueing task:`, error);
    throw error;
  }
  
  console.log(`[enqueueClearIncorrectMpsImageTask] Successfully enqueued task with ID: ${data.id}`);
  return data;
}

// Command line usage
if (import.meta.url === `file://${process.argv[1]}`) {
  const mpsId = process.argv[2];
  const reason = process.argv[3];
  
  if (!mpsId) {
    console.error('Usage: node enqueueClearIncorrectMpsImageTask.js <mps_id> [reason]');
    console.error('Example: node enqueueClearIncorrectMpsImageTask.js 12345 "Incorrect image uploaded"');
    process.exit(1);
  }
  
  try {
    const task = await enqueueClearIncorrectMpsImageTask(null, mpsId, reason);
    console.log(`Task enqueued successfully with ID: ${task.id}`);
    process.exit(0);
  } catch (error) {
    console.error(`Error enqueueing task: ${error.message}`);
    process.exit(1);
  }
}
