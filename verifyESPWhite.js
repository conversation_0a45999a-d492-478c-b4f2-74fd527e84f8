import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

dotenv.config();

const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_KEY;
const supabase = createClient(supabaseUrl, supabaseKey);

async function verifyESPWhite() {
    try {
        console.log('🔍 Verifying ESP White special case...\n');
        
        // Check ESP White products
        const { data: espWhite, error } = await supabase
            .from('it_discraft_order_sheet_lines')
            .select('plastic_name, mold_name, stamp_name, color_name, excel_row, vendor_description')
            .eq('plastic_name', 'ESP')
            .order('excel_row')
            .limit(10);
        
        if (error) throw error;
        
        console.log('🎯 ESP Products:');
        espWhite.forEach(item => {
            console.log(`  Row ${item.excel_row}: ${item.plastic_name} ${item.mold_name}`);
            console.log(`    Color: ${item.color_name}`);
            console.log(`    Stamp: ${item.stamp_name}`);
            console.log(`    Description: ${item.vendor_description}`);
            console.log('');
        });
        
    } catch (error) {
        console.error('❌ Error verifying ESP White:', error);
    }
}

verifyESPWhite();
