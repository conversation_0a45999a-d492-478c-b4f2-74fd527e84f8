// testDeleteVariantFromShopify.js - Test script for the delete variant from Shopify functionality
import dotenv from 'dotenv';
dotenv.config();

import { enqueueDeleteVariantFromShopifyTask } from './processDeleteVariantFromShopifyTask.js';

/**
 * Test function to demonstrate enqueueing a delete variant from Shopify task
 */
async function testDeleteVariantFromShopify() {
  try {
    console.log('=== Testing Delete Variant from Shopify Task ===');
    
    // Example OSL SKU - replace with an actual OSL SKU from your Shopify store for testing
    const testSku = 'OS16890';
    const reason = 'Testing variant deletion functionality';
    
    console.log(`Enqueueing variant deletion task for SKU: ${testSku}`);
    console.log(`Reason: ${reason}`);
    
    // Enqueue the task
    const task = await enqueueDeleteVariantFromShopifyTask(testSku, reason);
    
    console.log('\n✅ Task successfully enqueued!');
    console.log(`Task ID: ${task.id}`);
    console.log(`Task Type: ${task.task_type}`);
    console.log(`Status: ${task.status}`);
    console.log(`Scheduled At: ${task.scheduled_at}`);
    console.log(`Payload:`, JSON.stringify(task.payload, null, 2));
    
    console.log('\n📝 Expected behavior when this task is processed:');
    console.log('1. Find the variant with the specified SKU in Shopify');
    console.log('2. Check how many variants the parent product has');
    console.log('3. Delete ONLY the specific variant (not the whole product)');
    console.log('4. If it was the only variant, the product will be deleted as a side effect');
    console.log('5. Log the results with variant and product information');
    
    console.log('\n📋 Next steps:');
    console.log('1. Run the task queue worker to process this task');
    console.log('2. Check the t_task_queue table for task status updates');
    console.log('3. Verify the variant was deleted from Shopify (product may remain)');
    console.log('4. Check that other variants in the same product are unaffected');
    
  } catch (error) {
    console.error('❌ Error testing delete variant from Shopify:', error.message);
    process.exit(1);
  }
}

/**
 * Test the updated OSL workflow
 */
async function testOslUpdatedVariantDeletion() {
  try {
    console.log('\n=== Testing OSL Updated with Variant Deletion ===');
    
    // This would be called when an OSL is updated
    const oslId = 16890; // Example OSL ID
    const oslSku = `OS${oslId}`;
    
    console.log(`Simulating OSL update for ID: ${oslId} (SKU: ${oslSku})`);
    console.log('This would trigger the enhanced osl_updated task processing...');
    
    console.log('\n🔄 Enhanced OSL Updated Workflow:');
    console.log('1. ✅ Check if OSL was previously uploaded to Shopify');
    console.log('2. 🗑️  If yes, enqueue delete_variant_from_shopify task');
    console.log('3. 🔄 Set shopify_uploaded_at to NULL');
    console.log('4. ⚙️  Continue with normal OSL processing');
    console.log('5. 🔍 Variant deletion will:');
    console.log('   - Find the specific variant by SKU');
    console.log('   - Delete only that variant from the product');
    console.log('   - Leave other variants (different weights/colors) intact');
    console.log('   - Only delete the whole product if it was the last variant');
    
    console.log('\n📊 Key Differences from Product Deletion:');
    console.log('✅ Variant Deletion (NEW):');
    console.log('  - Deletes only the specific OSL variant');
    console.log('  - Preserves other variants in the same product');
    console.log('  - Uses REST API DELETE /products/{id}/variants/{variant_id}');
    console.log('  - Safer for shared products (same MPS, different weights/colors)');
    
    console.log('\n❌ Product Deletion (OLD):');
    console.log('  - Would delete the entire product');
    console.log('  - Would remove ALL variants, even unrelated ones');
    console.log('  - Uses GraphQL productDelete mutation');
    console.log('  - Could accidentally delete other OSL variants');
    
  } catch (error) {
    console.error('❌ Error in OSL updated test:', error.message);
  }
}

/**
 * Show example of product structure in Shopify
 */
function showProductStructureExample() {
  console.log('\n=== Shopify Product Structure Example ===');
  
  const exampleProduct = {
    title: "Innova Champion Destroyer",
    handle: "innova-champion-destroyer",
    variants: [
      { sku: "OS16890", title: "Blue / 170-175g", weight_range: "170-175g", color: "Blue" },
      { sku: "OS16891", title: "Red / 170-175g", weight_range: "170-175g", color: "Red" },
      { sku: "OS16892", title: "Blue / 165-169g", weight_range: "165-169g", color: "Blue" },
      { sku: "OS16893", title: "Blue / 176-180g", weight_range: "176-180g", color: "Blue" }
    ]
  };
  
  console.log('Product:', JSON.stringify(exampleProduct, null, 2));
  
  console.log('\n🎯 Scenario: OSL 16890 (Blue, 170-175g) is updated to 165-180g');
  console.log('\n✅ With Variant Deletion:');
  console.log('- Delete variant OS16890 (Blue / 170-175g)');
  console.log('- Keep variants OS16891, OS16892, OS16893');
  console.log('- Product "Innova Champion Destroyer" remains with 3 variants');
  console.log('- OSL 16890 will be re-uploaded as new variant with 165-180g range');
  
  console.log('\n❌ With Product Deletion (what we were doing before):');
  console.log('- Delete entire "Innova Champion Destroyer" product');
  console.log('- Lose ALL variants: OS16890, OS16891, OS16892, OS16893');
  console.log('- Other OSLs (16891, 16892, 16893) would lose their Shopify presence');
  console.log('- Would need to re-upload all 4 variants, not just the updated one');
}

// Run the test if this script is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  if (process.argv[2] === 'variant-only') {
    testDeleteVariantFromShopify();
  } else if (process.argv[2] === 'structure') {
    showProductStructureExample();
  } else {
    testDeleteVariantFromShopify()
      .then(() => testOslUpdatedVariantDeletion())
      .then(() => showProductStructureExample());
  }
}

export { 
  testDeleteVariantFromShopify, 
  testOslUpdatedVariantDeletion, 
  showProductStructureExample 
};
