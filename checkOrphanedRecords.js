import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

dotenv.config();

const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_KEY;
const supabase = createClient(supabaseUrl, supabaseKey);

async function checkOrphanedRecords() {
    try {
        console.log('Checking for orphaned records (verified but no vendor_internal_id)...');
        
        // Find records that are verified but have no vendor_internal_id
        const { data, error } = await supabase
            .from('t_order_sheet_lines')
            .select(`
                id, g_code, mps_id,
                vendor_match_verified,
                vendor_internal_id,
                vendor_match_verified_at,
                vendor_match_verified_by,
                t_mps!inner (
                    id, g_code,
                    t_molds!inner (id, mold),
                    t_plastics!inner (id, plastic),
                    t_stamps!inner (id, stamp)
                )
            `)
            .eq('vendor_id', 2) // Innova
            .eq('vendor_match_verified', true) // Verified
            .is('vendor_internal_id', null); // But no connection
        
        if (error) {
            console.error('Error querying orphaned records:', error);
            return;
        }
        
        console.log(`\n📊 Found ${data.length} orphaned records:`);
        console.log('These are verified but missing vendor_internal_id connections\n');
        
        if (data.length > 0) {
            console.log('Sample orphaned records:');
            data.slice(0, 10).forEach(record => {
                console.log(`  OSL ${record.id}: ${record.t_mps.t_molds.mold} ${record.t_mps.t_plastics.plastic} (verified: ${record.vendor_match_verified_at})`);
            });
            
            if (data.length > 10) {
                console.log(`  ... and ${data.length - 10} more`);
            }
            
            console.log('\n🔧 To fix these records:');
            console.log('1. Run Phase 2 matching to get new candidates');
            console.log('2. Use the "🔗 Connect Selected" button (not "✅ Confirm Selected")');
            console.log('3. Or manually reset them with SQL:');
            console.log(`   UPDATE t_order_sheet_lines SET vendor_match_verified = false WHERE vendor_match_verified = true AND vendor_internal_id IS NULL AND vendor_id = 2;`);
        } else {
            console.log('✅ No orphaned records found!');
        }
        
    } catch (error) {
        console.error('Check failed:', error);
    }
}

checkOrphanedRecords();
