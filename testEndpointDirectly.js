// Test the adminserver endpoints directly
import fetch from 'node-fetch';

async function testEndpointDirectly() {
    try {
        console.log('🧪 Testing adminserver endpoints directly...\n');
        
        // Test the unmatched OSLs endpoint
        console.log('1. Testing /api/discraft/review-unmatched-osls...');
        
        const response = await fetch('http://localhost:3001/api/discraft/review-unmatched-osls');
        
        if (!response.ok) {
            console.error(`❌ HTTP Error: ${response.status} ${response.statusText}`);
            const errorText = await response.text();
            console.error('Error details:', errorText);
            return;
        }
        
        const data = await response.json();
        
        if (data.success) {
            console.log('✅ Endpoint working successfully!');
            console.log(`   Found ${data.unmatchedOsls?.length || 0} unmatched OSLs`);
            console.log(`   Total unmatched OSLs: ${data.totalUnmatchedOsls || 0}`);
            console.log(`   Common mismatches: ${data.commonMismatches?.length || 0}`);
            
            if (data.unmatchedOsls && data.unmatchedOsls.length > 0) {
                console.log('\n   Sample unmatched OSLs:');
                data.unmatchedOsls.slice(0, 3).forEach((osl, index) => {
                    const mps = osl.t_mps;
                    console.log(`     ${index + 1}. OSL ${osl.id}: "${mps.t_plastics.plastic}" | "${mps.t_molds.mold}" | "${mps.t_stamps.stamp}"`);
                });
            }
        } else {
            console.error('❌ Endpoint returned error:', data.error);
        }
        
    } catch (error) {
        console.error('❌ Test error:', error.message);
        
        if (error.code === 'ECONNREFUSED') {
            console.log('\n💡 Make sure adminserver.js is running on port 3001');
            console.log('   Run: node adminserver.js');
        }
    }
}

testEndpointDirectly();
