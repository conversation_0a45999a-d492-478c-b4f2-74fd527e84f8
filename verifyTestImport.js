import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

dotenv.config();

const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_KEY;
const supabase = createClient(supabaseUrl, supabaseKey);

async function verifyTestImport() {
    try {
        console.log('🔍 Verifying test import...\n');
        
        // Get all imported records
        const { data: records, error } = await supabase
            .from('it_discraft_order_sheet_lines')
            .select('*')
            .order('excel_row', { ascending: true })
            .order('min_weight', { ascending: true });
        
        if (error) throw error;
        
        console.log(`📊 Total records imported: ${records.length}\n`);
        
        console.log('📋 All imported records:');
        records.forEach((record, index) => {
            console.log(`${index + 1}. Row ${record.excel_row}, Col ${record.excel_column}: ${record.plastic_name} ${record.mold_name} ${record.min_weight}-${record.max_weight}g ${record.stamp_name ? '(' + record.stamp_name + ')' : ''} - ${record.is_currently_available ? '✅ Available' : '⏳ Out of Stock'}`);
        });
        
        // Check for available items
        const availableItems = records.filter(r => r.is_currently_available);
        const outOfStockItems = records.filter(r => !r.is_currently_available);
        
        console.log(`\n✅ Available now: ${availableItems.length} products`);
        console.log(`⏳ Out of stock: ${outOfStockItems.length} products`);
        
        if (availableItems.length > 0) {
            console.log('\n🟢 Available products:');
            availableItems.forEach(item => {
                console.log(`  - ${item.plastic_name} ${item.mold_name} ${item.min_weight}-${item.max_weight}g`);
            });
        }
        
        // Show Excel mapping
        console.log('\n📍 Excel cell mapping:');
        records.forEach(record => {
            console.log(`  ${record.excel_column}${record.excel_row}: ${record.plastic_name} ${record.mold_name} ${record.min_weight}-${record.max_weight}g`);
        });
        
    } catch (error) {
        console.error('❌ Error verifying import:', error);
    }
}

verifyTestImport();
