import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

dotenv.config();

const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_KEY;
const supabase = createClient(supabaseUrl, supabaseKey);

async function calculateMpsIds() {
    try {
        console.log('🔄 Calculating MPS IDs for Discraft products...');
        
        // First, reset all calculated_mps_id to NULL
        console.log('🧹 Resetting existing MPS IDs...');
        const { error: resetError } = await supabase
            .from('it_discraft_order_sheet_lines')
            .update({ calculated_mps_id: null })
            .neq('id', 0); // Update all records
        
        if (resetError) {
            console.error('❌ Error resetting MPS IDs:', resetError);
            return;
        }
        
        // Get all Discraft products
        console.log('📊 Getting Discraft products...');
        const { data: discraftProducts, error: discraftError } = await supabase
            .from('it_discraft_order_sheet_lines')
            .select('id, plastic_name, mold_name, stamp_name');
        
        if (discraftError) {
            console.error('❌ Error getting Discraft products:', discraftError);
            return;
        }
        
        console.log(`📦 Found ${discraftProducts.length} Discraft products`);
        
        // Get all Discraft MPS records with their plastic/mold/stamp names
        console.log('📊 Getting Discraft MPS records...');
        const { data: mpsRecords, error: mpsError } = await supabase
            .from('t_mps')
            .select(`
                id,
                t_plastics!inner(plastic, brand_id),
                t_molds!inner(mold, brand_id),
                t_stamps!inner(stamp)
            `)
            .eq('active', true)
            .eq('t_plastics.brand_id', 6)  // Discraft brand
            .eq('t_molds.brand_id', 6);    // Discraft brand
        
        if (mpsError) {
            console.error('❌ Error getting MPS records:', mpsError);
            return;
        }
        
        console.log(`📦 Found ${mpsRecords.length} active Discraft MPS records`);
        
        // Create a map for faster lookup
        const mpsMap = new Map();
        mpsRecords.forEach(mps => {
            const key = `${mps.t_plastics.plastic.trim()}|${mps.t_molds.mold.trim()}|${mps.t_stamps.stamp.trim()}`;
            mpsMap.set(key, mps.id);
        });
        
        console.log('🔍 Matching products to MPS records...');
        
        let matchedCount = 0;
        let batchUpdates = [];
        
        for (const product of discraftProducts) {
            const key = `${product.plastic_name.trim()}|${product.mold_name.trim()}|${product.stamp_name.trim()}`;
            const mpsId = mpsMap.get(key);
            
            if (mpsId) {
                batchUpdates.push({
                    id: product.id,
                    calculated_mps_id: mpsId
                });
                matchedCount++;
                
                // Process in batches of 100
                if (batchUpdates.length >= 100) {
                    await processBatch(batchUpdates);
                    batchUpdates = [];
                }
            }
        }
        
        // Process remaining updates
        if (batchUpdates.length > 0) {
            await processBatch(batchUpdates);
        }
        
        const failedCount = discraftProducts.length - matchedCount;
        const successRate = ((matchedCount / discraftProducts.length) * 100).toFixed(2);
        
        console.log('✅ MPS ID calculation completed!');
        console.log(`📊 Results:`);
        console.log(`   • Total products: ${discraftProducts.length}`);
        console.log(`   • Successfully matched: ${matchedCount}`);
        console.log(`   • Failed to match: ${failedCount}`);
        console.log(`   • Success rate: ${successRate}%`);
        
        if (successRate < 80) {
            console.log('⚠️ Low success rate - consider improving parsing logic');
        }
        
    } catch (error) {
        console.error('❌ Error calculating MPS IDs:', error);
    }
}

async function processBatch(updates) {
    try {
        for (const update of updates) {
            const { error } = await supabase
                .from('it_discraft_order_sheet_lines')
                .update({ calculated_mps_id: update.calculated_mps_id })
                .eq('id', update.id);
            
            if (error) {
                console.error(`❌ Error updating product ${update.id}:`, error);
            }
        }
        console.log(`✅ Updated batch of ${updates.length} products`);
    } catch (error) {
        console.error('❌ Error processing batch:', error);
    }
}

calculateMpsIds();
