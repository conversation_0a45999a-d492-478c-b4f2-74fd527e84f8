import XLSX from 'xlsx';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Path to the Excel file
const excelFilePath = path.join(__dirname, 'data', 'external data', 'innovaorderform.xlsx');

console.log('Examining Innova Excel file:', excelFilePath);

try {
    // Read the workbook
    const workbook = XLSX.readFile(excelFilePath);
    
    console.log('\nWorksheet names:');
    workbook.SheetNames.forEach((name, index) => {
        console.log(`${index + 1}. ${name}`);
    });
    
    // Focus on the Order_Table sheet
    if (workbook.SheetNames.includes('Order_Table')) {
        console.log('\n=== Order_Table Sheet Analysis ===');
        const worksheet = workbook.Sheets['Order_Table'];
        
        // Get the range of the worksheet
        const range = XLSX.utils.decode_range(worksheet['!ref']);
        console.log(`Range: ${worksheet['!ref']} (${range.e.r + 1} rows, ${range.e.c + 1} columns)`);
        
        // Get headers (first row)
        console.log('\nColumn Headers:');
        const headers = [];
        for (let col = range.s.c; col <= range.e.c; col++) {
            const cellAddress = XLSX.utils.encode_cell({ r: 0, c: col });
            const cell = worksheet[cellAddress];
            const header = cell ? cell.v : `Column_${col}`;
            headers.push(header);
            console.log(`${col + 1}. ${header}`);
        }
        
        // Show first few rows of data (only meaningful columns)
        console.log('\nFirst 5 data rows (meaningful columns only):');
        const meaningfulColumns = headers.slice(0, 19); // First 19 columns
        for (let row = 1; row <= Math.min(5, range.e.r); row++) {
            const rowData = {};
            for (let col = 0; col < 19; col++) {
                const cellAddress = XLSX.utils.encode_cell({ r: row, c: col });
                const cell = worksheet[cellAddress];
                const header = headers[col];
                rowData[header] = cell ? cell.v : null;
            }
            console.log(`Row ${row}:`, rowData);
        }
        
        // Convert to JSON to see data types (only meaningful columns)
        console.log('\n=== Data Type Analysis ===');
        const jsonData = XLSX.utils.sheet_to_json(worksheet, {
            header: meaningfulColumns,
            range: `A1:S${range.e.r + 1}` // Only first 19 columns (A-S)
        });
        if (jsonData.length > 0) {
            const firstRow = jsonData[0];
            console.log('Sample data types (meaningful columns):');
            Object.entries(firstRow).forEach(([key, value]) => {
                console.log(`${key}: ${typeof value} (${value})`);
            });
        }
        
        console.log(`\nTotal data rows: ${jsonData.length}`);
        
    } else {
        console.log('\nOrder_Table sheet not found!');
    }
    
} catch (error) {
    console.error('Error reading Excel file:', error);
}
