import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

dotenv.config();

const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_KEY;
const supabase = createClient(supabaseUrl, supabaseKey);

async function investigateMatching() {
    try {
        console.log('🔍 Investigating exact match issues...\n');
        
        // Test specific cases that should be exact matches
        const testCases = ['Buzzz', 'BuzzzSS', 'Drive', 'Force', 'Luna', 'Nuke'];
        
        for (const moldName of testCases) {
            console.log(`\n🧪 Testing mold: "${moldName}"`);
            
            // Check vendor products
            const { data: vendorProducts, error: vendorError } = await supabase
                .from('it_discraft_order_sheet_lines')
                .select('id, plastic_name, mold_name, stamp_name, calculated_mps_id')
                .eq('mold_name', moldName)
                .limit(5);
            
            if (vendorError) {
                console.error(`Error getting vendor products for ${moldName}:`, vendorError);
                continue;
            }
            
            console.log(`   Vendor products found: ${vendorProducts.length}`);
            vendorProducts.forEach(product => {
                console.log(`   - "${product.plastic_name}" | "${product.mold_name}" | "${product.stamp_name}" | MPS: ${product.calculated_mps_id || 'NULL'}`);
            });
            
            // Check database molds
            const { data: dbMolds, error: moldError } = await supabase
                .from('t_molds')
                .select('id, mold, brand_id')
                .eq('mold', moldName)
                .eq('brand_id', 6); // Discraft
            
            if (moldError) {
                console.error(`Error getting DB molds for ${moldName}:`, moldError);
                continue;
            }
            
            console.log(`   DB molds found: ${dbMolds.length}`);
            dbMolds.forEach(mold => {
                console.log(`   - Mold ID ${mold.id}: "${mold.mold}" (brand ${mold.brand_id})`);
            });
            
            // Check MPS records for this mold
            if (dbMolds.length > 0) {
                const { data: mpsRecords, error: mpsError } = await supabase
                    .from('t_mps')
                    .select(`id, t_plastics!inner(plastic, brand_id), t_molds!inner(mold, brand_id), t_stamps!inner(stamp)`)
                    .eq('active', true)
                    .eq('t_molds.mold', moldName)
                    .eq('t_molds.brand_id', 6)
                    .eq('t_plastics.brand_id', 6)
                    .limit(10);
                
                if (mpsError) {
                    console.error(`Error getting MPS records for ${moldName}:`, mpsError);
                    continue;
                }
                
                console.log(`   MPS records found: ${mpsRecords.length}`);
                mpsRecords.forEach(mps => {
                    console.log(`   - MPS ${mps.id}: "${mps.t_plastics.plastic}" | "${mps.t_molds.mold}" | "${mps.t_stamps.stamp}"`);
                });
                
                // Test specific matching for Elite Z Swirl
                const { data: eliteZMps, error: eliteZError } = await supabase
                    .from('t_mps')
                    .select(`id, t_plastics!inner(plastic, brand_id), t_molds!inner(mold, brand_id), t_stamps!inner(stamp)`)
                    .eq('active', true)
                    .eq('t_molds.mold', moldName)
                    .eq('t_molds.brand_id', 6)
                    .eq('t_plastics.plastic', 'Elite Z Swirl')
                    .eq('t_plastics.brand_id', 6);
                
                if (!eliteZError && eliteZMps.length > 0) {
                    console.log(`   Elite Z Swirl MPS records: ${eliteZMps.length}`);
                    eliteZMps.forEach(mps => {
                        console.log(`   - MPS ${mps.id}: "${mps.t_plastics.plastic}" | "${mps.t_molds.mold}" | "${mps.t_stamps.stamp}"`);
                    });
                }
            }
        }
        
        // Check for any obvious issues with the matching logic
        console.log('\n🔍 Checking for common matching issues...');
        
        // Check for whitespace issues
        const { data: whitespaceIssues, error: whitespaceError } = await supabase
            .from('it_discraft_order_sheet_lines')
            .select('mold_name, plastic_name, stamp_name')
            .is('calculated_mps_id', null)
            .in('mold_name', testCases)
            .limit(10);
        
        if (!whitespaceError && whitespaceIssues.length > 0) {
            console.log('\n⚠️ Unmatched products with exact mold names:');
            whitespaceIssues.forEach(product => {
                console.log(`   - "${product.plastic_name}" | "${product.mold_name}" | "${product.stamp_name}"`);
                console.log(`     Mold length: ${product.mold_name.length}, has leading/trailing spaces: ${product.mold_name !== product.mold_name.trim()}`);
                console.log(`     Plastic length: ${product.plastic_name.length}, has leading/trailing spaces: ${product.plastic_name !== product.plastic_name.trim()}`);
                console.log(`     Stamp length: ${product.stamp_name.length}, has leading/trailing spaces: ${product.stamp_name !== product.stamp_name.trim()}`);
            });
        }
        
        // Check the actual matching query that should work
        console.log('\n🧪 Testing manual matching query...');
        const { data: testMatch, error: testError } = await supabase
            .from('t_mps')
            .select(`id, t_plastics!inner(plastic, brand_id), t_molds!inner(mold, brand_id), t_stamps!inner(stamp)`)
            .eq('active', true)
            .eq('t_plastics.plastic', 'Elite Z Swirl')
            .eq('t_molds.mold', 'Buzzz')
            .eq('t_stamps.stamp', 'Chris Dickerson 2025 Tour Series')
            .eq('t_plastics.brand_id', 6)
            .eq('t_molds.brand_id', 6);
        
        if (!testError) {
            console.log(`Manual match test for "Elite Z Swirl | Buzzz | Chris Dickerson 2025 Tour Series": ${testMatch.length} results`);
            testMatch.forEach(mps => {
                console.log(`   - MPS ${mps.id}: "${mps.t_plastics.plastic}" | "${mps.t_molds.mold}" | "${mps.t_stamps.stamp}"`);
            });
        }
        
    } catch (error) {
        console.error('❌ Investigation error:', error);
    }
}

investigateMatching();
