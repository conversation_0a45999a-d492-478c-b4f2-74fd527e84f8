// Test the weight parsing function
function parseInnovaWeights(weightStr) {
    if (!weightStr) return null;
    
    const str = weightStr.toString().toLowerCase().trim();
    
    // Handle "max weight" or similar
    if (str.includes('max') || str.includes('minimum') || str.includes('varies')) {
        return { error: 'unparseable', original: weightStr };
    }
    
    // Handle "<139" format (means up to 139g, typically 100-139g for discs)
    const lessThanMatch = str.match(/^<\s*(\d+)/);
    if (lessThanMatch) {
        const maxWeight = parseInt(lessThanMatch[1]);
        return {
            min: 100, // Assume minimum disc weight is 100g
            max: maxWeight,
            original: weightStr,
            note: 'parsed_less_than'
        };
    }
    
    // Handle ">175" format (means 175g and up, typically 175-180g for discs)
    const greaterThanMatch = str.match(/^>\s*(\d+)/);
    if (greaterThanMatch) {
        const minWeight = parseInt(greaterThanMatch[1]);
        return {
            min: minWeight,
            max: 180, // Assume maximum disc weight is 180g
            original: weightStr,
            note: 'parsed_greater_than'
        };
    }
    
    // Look for pattern like "173-175" or "173-175g"
    const rangeMatch = str.match(/(\d+)\s*-\s*(\d+)/);
    if (rangeMatch) {
        return {
            min: parseInt(rangeMatch[1]),
            max: parseInt(rangeMatch[2]),
            original: weightStr
        };
    }
    
    // Look for single number like "175g" or "175"
    const singleMatch = str.match(/(\d+)/);
    if (singleMatch) {
        const weight = parseInt(singleMatch[1]);
        return {
            min: weight,
            max: weight,
            original: weightStr
        };
    }
    
    return { error: 'unparseable', original: weightStr };
}

// Test cases
const testCases = [
    '<139',
    '>175', 
    '173-175',
    '165-175g',
    '175',
    '175g',
    'Max Weight',
    'Varies',
    '< 139',
    '> 175',
    '160 - 170'
];

console.log('=== WEIGHT PARSING TEST RESULTS ===\n');

testCases.forEach(testCase => {
    const result = parseInnovaWeights(testCase);
    if (result?.error) {
        console.log(`"${testCase}" → ERROR: ${result.original}`);
    } else if (result) {
        const display = `${result.min}-${result.max}${result.note ? ` (${result.note})` : ''}`;
        console.log(`"${testCase}" → ${display}`);
    } else {
        console.log(`"${testCase}" → NULL`);
    }
});

console.log('\n=== EXAMPLES FROM YOUR DATA ===');
console.log('"<139" → 100-139 (parsed_less_than)');
console.log('"173-175" → 173-175');
console.log('">175" → 175-180 (parsed_greater_than)');
console.log('"Max Weight" → ERROR: unparseable');
