// Simple test for the export functionality using Node.js built-in fetch (Node 18+)

async function testExport() {
    console.log('🧪 Testing Discraft export...');
    
    try {
        const response = await fetch('http://localhost:3001/api/discraft/export-orders', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            }
        });

        const data = await response.json();
        
        if (data.success) {
            console.log('✅ Export successful!');
            console.log('📊 Results:', data);
        } else {
            console.log('❌ Export failed:', data.error);
            if (data.details) {
                console.log('Details:', data.details);
            }
        }
    } catch (error) {
        console.log('❌ Test error:', error.message);
    }
}

testExport();
