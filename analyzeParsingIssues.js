import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

dotenv.config();

const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_KEY;
const supabase = createClient(supabaseUrl, supabaseKey);

async function analyzeParsingIssues() {
    try {
        console.log('🔍 Analyzing parsing issues in full import...\n');
        
        // Check problematic mold names
        console.log('🚨 Problematic Mold Names:');
        
        const problematicMolds = ['NEW', 'PM', 'Retired', 'Discontinued', 'SPECIAL', 'Unknown'];
        
        for (const moldName of problematicMolds) {
            const { data: products, error } = await supabase
                .from('it_discraft_order_sheet_lines')
                .select('mold_name, plastic_name, raw_model, excel_row_hint')
                .eq('mold_name', moldName)
                .limit(10);
            
            if (error) throw error;
            
            if (products.length > 0) {
                console.log(`\n❌ "${moldName}" (${products.length} products):`);
                products.slice(0, 5).forEach(product => {
                    console.log(`  Row ${product.excel_row_hint}: "${product.raw_model}" → ${product.plastic_name} ${product.mold_name}`);
                });
                if (products.length > 5) {
                    console.log(`  ... and ${products.length - 5} more`);
                }
            }
        }
        
        // Check plastic parsing issues
        console.log('\n\n🎨 Plastic Parsing Issues:');
        
        const { data: plastics, error: plasticError } = await supabase
            .from('it_discraft_order_sheet_lines')
            .select('plastic_name, raw_line_type, excel_row_hint')
            .in('plastic_name', ['Unknown', 'SPECIAL'])
            .limit(10);
        
        if (plasticError) throw plasticError;
        
        if (plastics.length > 0) {
            plastics.forEach(product => {
                console.log(`  Row ${product.excel_row_hint}: "${product.raw_line_type}" → ${product.plastic_name}`);
            });
        } else {
            console.log('  ✅ No major plastic parsing issues found');
        }
        
        // Check special sections
        console.log('\n\n🎯 Special Sections:');
        
        const { data: special, error: specialError } = await supabase
            .from('it_discraft_order_sheet_lines')
            .select('raw_line_type, raw_model, plastic_name, mold_name, excel_row_hint')
            .eq('raw_line_type', 'SPECIAL')
            .limit(10);
        
        if (specialError) throw specialError;
        
        if (special.length > 0) {
            special.forEach(product => {
                console.log(`  Row ${product.excel_row_hint}: "${product.raw_model}" → ${product.plastic_name} ${product.mold_name}`);
            });
        } else {
            console.log('  ✅ No special sections found');
        }
        
        // Check stamp parsing
        console.log('\n\n🏷️ Stamp Distribution:');
        
        const { data: stamps, error: stampError } = await supabase
            .from('it_discraft_order_sheet_lines')
            .select('stamp_name')
            .order('stamp_name');
        
        if (stampError) throw stampError;
        
        const stampCounts = {};
        stamps.forEach(product => {
            stampCounts[product.stamp_name] = (stampCounts[product.stamp_name] || 0) + 1;
        });
        
        Object.entries(stampCounts)
            .sort(([,a], [,b]) => b - a)
            .slice(0, 10)
            .forEach(([stamp, count]) => {
                console.log(`  ${stamp}: ${count} products`);
            });
        
        // Check mapping key uniqueness
        console.log('\n\n🔑 Mapping Key Analysis:');
        
        const { data: mappingKeys, error: keyError } = await supabase
            .from('it_discraft_order_sheet_lines')
            .select('excel_mapping_key');
        
        if (keyError) throw keyError;
        
        const keySet = new Set(mappingKeys.map(k => k.excel_mapping_key));
        const duplicateCount = mappingKeys.length - keySet.size;
        
        console.log(`  📊 Total products: ${mappingKeys.length}`);
        console.log(`  🔑 Unique mapping keys: ${keySet.size}`);
        console.log(`  🔄 Duplicate keys: ${duplicateCount}`);
        
        if (duplicateCount > 0) {
            console.log('  ⚠️ Some products have identical mapping keys - this could cause write-back conflicts');
        } else {
            console.log('  ✅ All mapping keys are unique - perfect for order write-back!');
        }
        
    } catch (error) {
        console.error('❌ Error analyzing parsing issues:', error);
    }
}

analyzeParsingIssues();
