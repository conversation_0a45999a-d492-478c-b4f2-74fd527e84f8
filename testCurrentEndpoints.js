import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

dotenv.config();

const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_KEY;
const supabase = createClient(supabaseUrl, supabaseKey);

async function testCurrentEndpoints() {
    try {
        console.log('🧪 Testing current endpoint logic...\n');
        
        // Test what the status endpoint should return
        console.log('1. Testing Discraft status logic...');
        
        const { count: totalProducts, error: totalError } = await supabase
            .from('it_discraft_order_sheet_lines')
            .select('*', { count: 'exact', head: true });
        
        if (totalError) {
            console.error('Error getting total:', totalError);
            return;
        }
        
        const { count: availableProducts, error: availableError } = await supabase
            .from('it_discraft_order_sheet_lines')
            .select('*', { count: 'exact', head: true })
            .eq('is_currently_available', true);
        
        if (availableError) {
            console.error('Error getting available:', availableError);
            return;
        }
        
        const outOfStockProducts = totalProducts - availableProducts;
        
        console.log(`✅ Total products: ${totalProducts}`);
        console.log(`✅ Available: ${availableProducts}`);
        console.log(`✅ Out of stock: ${outOfStockProducts}`);
        
        // Test analyze matching logic
        console.log('\n2. Testing analyze matching logic...');
        
        const { count: withMpsId, error: withMpsError } = await supabase
            .from('it_discraft_order_sheet_lines')
            .select('*', { count: 'exact', head: true })
            .not('calculated_mps_id', 'is', null);
        
        if (withMpsError) {
            console.error('Error getting matched:', withMpsError);
            return;
        }
        
        const withoutMpsId = totalProducts - withMpsId;
        const mpsSuccessRate = ((withMpsId / totalProducts) * 100).toFixed(1);
        
        console.log(`✅ With MPS ID: ${withMpsId}`);
        console.log(`✅ Without MPS ID: ${withoutMpsId}`);
        console.log(`✅ Success rate: ${mpsSuccessRate}%`);
        
        // Test getting unmatched products (chunked)
        console.log('\n3. Testing unmatched products (chunked)...');
        
        let allUnmatched = [];
        let offset = 0;
        const chunkSize = 1000;
        
        while (true) {
            const { data: unmatchedChunk, error: unmatchedError } = await supabase
                .from('it_discraft_order_sheet_lines')
                .select('plastic_name, mold_name, stamp_name')
                .is('calculated_mps_id', null)
                .range(offset, offset + chunkSize - 1);
            
            if (unmatchedError) {
                console.error('Error getting unmatched chunk:', unmatchedError);
                break;
            }
            
            if (!unmatchedChunk || unmatchedChunk.length === 0) break;
            
            allUnmatched = allUnmatched.concat(unmatchedChunk);
            console.log(`   Retrieved chunk: ${unmatchedChunk.length} (total: ${allUnmatched.length})`);
            
            if (unmatchedChunk.length < chunkSize) break;
            offset += chunkSize;
        }
        
        console.log(`✅ Total unmatched products retrieved: ${allUnmatched.length}`);
        
        // Group unmatched products
        const unmatchedGroups = {};
        allUnmatched.forEach(row => {
            const key = `${row.plastic_name}|${row.mold_name}|${row.stamp_name}`;
            unmatchedGroups[key] = (unmatchedGroups[key] || 0) + 1;
        });
        
        const topUnmatched = Object.entries(unmatchedGroups)
            .sort(([,a], [,b]) => b - a)
            .slice(0, 10)
            .map(([key, count]) => {
                const [plastic_name, mold_name, stamp_name] = key.split('|');
                return { plastic_name, mold_name, stamp_name, count };
            });
        
        console.log('\n📊 Top 10 unmatched product types:');
        topUnmatched.forEach((item, index) => {
            console.log(`${index + 1}. "${item.plastic_name}" | "${item.mold_name}" | "${item.stamp_name}" (${item.count} products)`);
        });
        
        console.log('\n🎉 All endpoint logic tests completed successfully!');
        
    } catch (error) {
        console.error('❌ Test error:', error);
    }
}

testCurrentEndpoints();
