# Delete SKU from Shopify - Task Queue Integration

This document describes the new `delete_sku_from_shopify` task type that allows you to delete products from your Shopify store by SKU through the task queue system.

## Overview

The system provides a way to safely delete products from Shopify by:
1. Finding the product variant by SKU using Shopify's GraphQL API
2. Retrieving the parent product information
3. Deleting the entire product using Shopify's `productDelete` mutation
4. Handling cases where the SKU doesn't exist (graceful completion)

## Files Created

- `processDeleteSkuFromShopifyTask.js` - Main task processor
- `enqueueDeleteSkuFromShopifyTask.js` - Helper to enqueue tasks
- `testDeleteSkuFromShopify.js` - Test script
- `taskQueueWorker.js` - Updated to handle the new task type

## Usage

### 1. Enqueue a Task Programmatically

```javascript
import { enqueueDeleteSkuFromShopifyTask } from './enqueueDeleteSkuFromShopifyTask.js';

// Basic usage
const task = await enqueueDeleteSkuFromShopifyTask('D12345', 'Product discontinued');

// With custom scheduling (optional)
const scheduledTime = new Date(Date.now() + 10 * 60 * 1000); // 10 minutes from now
const task = await enqueueDeleteSkuFromShopifyTask('D12345', 'Scheduled deletion', scheduledTime);
```

### 2. Enqueue via Command Line

```bash
node enqueueDeleteSkuFromShopifyTask.js "D12345" "Product discontinued"
```

### 3. Enqueue via Database Insert

```sql
INSERT INTO t_task_queue (task_type, payload, status, scheduled_at, created_at)
VALUES (
  'delete_sku_from_shopify',
  '{"sku": "D12345", "reason": "Product discontinued"}',
  'pending',
  NOW() + INTERVAL '5 minutes',
  NOW()
);
```

### 4. Integration with Other Processes

You can enqueue this task from any part of your system:

```javascript
// From a webhook handler
app.post('/webhook/product-discontinued', async (req, res) => {
  const { sku } = req.body;
  await enqueueDeleteSkuFromShopifyTask(sku, 'Product discontinued via webhook');
  res.json({ success: true });
});

// From a batch process
async function cleanupDiscontinuedProducts(skus) {
  for (const sku of skus) {
    await enqueueDeleteSkuFromShopifyTask(sku, 'Batch cleanup of discontinued products');
  }
}

// From another task processor
async function processDiscSoldTask(task) {
  // ... other logic ...
  
  // If this was the last disc, remove from Shopify
  if (shouldRemoveFromShopify) {
    await enqueueDeleteSkuFromShopifyTask(sku, 'Last disc sold - removing from store');
  }
}
```

## Task Payload Structure

```json
{
  "sku": "D12345",
  "reason": "Optional reason for deletion"
}
```

## Task Processing Flow

1. **Task Validation**: Checks that SKU is provided in payload
2. **SKU Lookup**: Uses Shopify GraphQL API to find variant by SKU
3. **Product Deletion**: If found, deletes the entire product
4. **Result Logging**: Updates task status with detailed results

## Possible Task Results

### Success Cases

1. **Product Found and Deleted**:
   ```json
   {
     "message": "Successfully deleted product for SKU D12345 from Shopify",
     "sku": "D12345",
     "deletedProductId": "gid://shopify/Product/8723341705404",
     "productTitle": "Innova Champion Destroyer",
     "productHandle": "innova-champion-destroyer",
     "reason": "Product discontinued"
   }
   ```

2. **SKU Not Found** (also considered success):
   ```json
   {
     "message": "SKU D12345 not found in Shopify - nothing to delete",
     "sku": "D12345",
     "found": false,
     "reason": "Product discontinued"
   }
   ```

### Error Cases

```json
{
  "message": "Failed to delete SKU D12345 from Shopify: GraphQL errors: ...",
  "error": "Detailed error message",
  "sku": "D12345",
  "reason": "Product discontinued"
}
```

## Environment Requirements

Ensure these environment variables are set:

```env
SHOPIFY_ENDPOINT=https://yourstore.myshopify.com/admin/api/2024-01/graphql.json
SHOPIFY_ACCESS_TOKEN=shpat_your_access_token_here
SUPABASE_URL=https://your-project.supabase.co
SUPABASE_KEY=your_supabase_key_here
```

## Shopify Permissions

Your Shopify access token needs the following scopes:
- `read_products` - To find products by SKU
- `write_products` - To delete products

## Testing

Run the test script to verify everything works:

```bash
node testDeleteSkuFromShopify.js
```

This will enqueue a test task that you can process with the task queue worker.

## Monitoring

Monitor task execution through:

1. **Database**: Check `t_task_queue` table for task status
2. **Logs**: Task processor provides detailed console logging
3. **Error Logs**: Errors are logged to `t_error_logs` table

## Safety Considerations

- **Irreversible**: Product deletion is permanent in Shopify
- **Whole Product**: Deletes the entire product, not just the variant
- **Graceful Handling**: SKUs not found are treated as successful completion
- **Detailed Logging**: All actions are logged for audit purposes

## Integration Examples

### Example 1: Disc Sold Workflow

```javascript
// When a disc is sold, check if we should remove from Shopify
async function handleDiscSold(discId) {
  // ... update inventory ...
  
  // If this was our last disc of this SKU, remove from Shopify
  const remainingCount = await getRemainingDiscCount(discId);
  if (remainingCount === 0) {
    const sku = `D${discId}`;
    await enqueueDeleteSkuFromShopifyTask(sku, 'Last disc sold - removing from store');
  }
}
```

### Example 2: Product Discontinuation

```javascript
// When a product is discontinued
async function discontinueProduct(productId) {
  // Mark as discontinued in database
  await markProductDiscontinued(productId);
  
  // Remove from all sales channels
  const sku = await getProductSku(productId);
  await enqueueDeleteSkuFromShopifyTask(sku, 'Product discontinued');
  // Could also enqueue Amazon deletion, etc.
}
```

## Troubleshooting

### Common Issues

1. **Invalid SKU Format**: Ensure SKU matches exactly what's in Shopify
2. **Permission Errors**: Verify Shopify access token has required scopes
3. **Network Timeouts**: Shopify API calls may timeout; task will retry
4. **Product Not Found**: This is handled gracefully as a success case

### Debug Steps

1. Check task status in `t_task_queue` table
2. Review console logs from task queue worker
3. Check `t_error_logs` table for detailed error information
4. Verify Shopify credentials and permissions
5. Test with a known existing SKU first
