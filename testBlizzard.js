// Test Blizzard Champion plastic mapping

const plasticMapping = {
    'Champion Blizzard': 'Blizzard Champion'
};

function normalizePlasticName(plasticName) {
    if (!plasticName) return null;
    const mapped = plasticMapping[plasticName];
    if (mapped) return mapped;
    return plasticName;
}

console.log('=== BLIZZARD PLASTIC MAPPING TEST ===');
console.log(`Your "Champion Blizzard" → Innova "${normalizePlasticName('Champion Blizzard')}"`);

// Test matching
const yourPlastic = 'Champion Blizzard';
const innovaPlastic = 'Blizzard Champion';

const yourNormalized = normalizePlasticName(yourPlastic);
const innovaNormalized = normalizePlasticName(innovaPlastic);

const match = yourNormalized?.toLowerCase() === innovaNormalized?.toLowerCase();

console.log('\n=== MATCHING TEST ===');
console.log(`Your: "${yourPlastic}" → Normalized: "${yourNormalized}"`);
console.log(`Innova: "${innovaPlastic}" → Normalized: "${innovaNormalized}"`);
console.log(`Match: ${match ? '✅ YES' : '❌ NO'}`);

// Test parsing
function parseInnovaDescription(description) {
    const plasticTypes = [
        'Overmold Star INNfuse', 'INNfuse Star', 'Overmold Champion', 'Overmold',
        'Champion I-Dye', 'Champion Proto Glow', 'Champion Classic Glow', 'Blizzard Champion', 'Champion', 
        'Star I-Dye', 'Halo Star', 'Star', 
        'DX Proto Glow', 'DX Classic Glow', 'DX', 
        'Pro KC', 'KC Pro', 'R-Pro', 'Pro',
        'Halo Nexus', 'Nexus',
        'Metal Flake Champion', 'GStar', 'G-Star', 'XT'
    ];
    
    let plastic = null;
    let remainingText = description.trim();
    
    for (const plasticType of plasticTypes) {
        const regex = new RegExp(`\\b${plasticType}\\b`, 'gi');
        if (regex.test(description)) {
            plastic = plasticType;
            remainingText = description.replace(regex, '').trim();
            break;
        }
    }
    
    let mold = remainingText
        .replace(/\s+(Putt & Approach|Fairway Distance Driver|Distance Driver|Fairway Driver|Mid-Range|Midrange|Specialty Disc|Putter|Driver|Approach)(\s+Disc)?$/i, '')
        .replace(/\s+Disc$/i, '')
        .replace(/\s+with\s+.*/i, '')
        .replace(/\s+Overmold\s*-?\s*$/i, '')
        .replace(/\s*-\s*$/i, '')
        .trim();
    
    return { plastic, mold, original: description };
}

console.log('\n=== PARSING TEST ===');
const testDescription = 'Blizzard Champion Destroyer Distance Driver';
const parsed = parseInnovaDescription(testDescription);
console.log(`"${testDescription}"`);
console.log(`  → Plastic: "${parsed.plastic}"`);
console.log(`  → Mold: "${parsed.mold}"`);
