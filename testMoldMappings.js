// Test the new mold mappings

const moldMapping = {
    'AviardX3': 'Aviar X3',
    'AviarX3': 'Aviar X3',
    'Aviar3': 'Aviar3', // No mapping needed - should match directly
    'Mako3': 'Mako 3',
    'Aviar Classic': 'Classic Aviar',
    'Aviar Putter': 'Aviar',
    'Aviar P&A': 'Aviar', // Alternative name for Aviar Putter
    'Leopard3': 'Leopard 3',
    'Teebird3': 'Teebird 3',
    'Roc3': 'Roc 3',
    'TL3': 'TL 3'
};

function normalizeMoldName(moldName) {
    if (!moldName) return null;
    const mapped = moldMapping[moldName];
    if (mapped) return mapped;
    return moldName;
}

console.log('=== MOLD MAPPING TEST ===\n');

// Test all mappings
Object.keys(moldMapping).forEach(yourMold => {
    const innovaMold = moldMapping[yourMold];
    const normalized = normalizeMoldName(yourMold);
    console.log(`Your "${yourMold}" → Innova "${innovaMold}" → Normalized "${normalized}"`);
});

console.log('\n=== NEW MAPPINGS ===');
console.log(`Roc3 → ${normalizeMoldName('Roc3')}`);
console.log(`TL3 → ${normalizeMoldName('TL3')}`);
console.log(`Aviar P&A → ${normalizeMoldName('Aviar P&A')}`);

console.log('\n=== MATCHING TEST ===');
// Test if they would match
const testCases = [
    { your: 'Roc3', innova: 'Roc 3' },
    { your: 'TL3', innova: 'TL 3' },
    { your: 'Aviar P&A', innova: 'Aviar' },
    { your: 'Aviar Putter', innova: 'Aviar' }
];

testCases.forEach(test => {
    const yourNormalized = normalizeMoldName(test.your);
    const innovaNormalized = normalizeMoldName(test.innova);
    const match = yourNormalized?.toLowerCase() === innovaNormalized?.toLowerCase();
    console.log(`"${test.your}" vs "${test.innova}" → ${yourNormalized} vs ${innovaNormalized} → ${match ? '✅ MATCH' : '❌ NO MATCH'}`);
});
