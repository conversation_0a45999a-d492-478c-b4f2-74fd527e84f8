import { runPhase2Matching } from './phase2InnovaMatching.js';

console.log('Testing new plastic mappings in Phase 2...');

// Test with a small batch to see if the new mappings work
runPhase2Matching(0, 10, 1, 50).then(result => {
    if (result.success) {
        console.log('✅ New plastic mappings working!');
        console.log(`Processed: ${result.processed} OSLs`);
        console.log(`Matches found: ${result.matches}`);
        
        console.log('\n🎨 New Plastic Mappings Added:');
        console.log('✅ Champion I-Dye → I-Dye Champion');
        console.log('✅ Star I-Dye → I-Dye Star');
        console.log('✅ Champion Metal Flake → Champion Metal Flake');
        console.log('✅ Metal Flake Champion → Champion Metal Flake');
        
        console.log('\n📊 These mappings will improve matching for:');
        console.log('- I-Dye Champion discs');
        console.log('- I-Dye Star discs');
        console.log('- Champion Metal Flake discs');
        
    } else {
        console.error('❌ Test failed:', result.error);
    }
}).catch(error => {
    console.error('Test error:', error.message);
});
