import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

dotenv.config();

const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_KEY;
const supabase = createClient(supabaseUrl, supabaseKey);

async function analyzeMismatches() {
    try {
        console.log('🔍 Analyzing MPS matching mismatches...\n');
        
        // Get unmatched Discraft products
        const { data: unmatchedProducts, error: unmatchedError } = await supabase
            .from('it_discraft_order_sheet_lines')
            .select('plastic_name, mold_name, stamp_name')
            .is('calculated_mps_id', null)
            .limit(50);
        
        if (unmatchedError) {
            console.error('❌ Error getting unmatched products:', unmatchedError);
            return;
        }
        
        console.log(`❌ Top ${unmatchedProducts.length} Unmatched Discraft Products:`);
        unmatchedProducts.slice(0, 20).forEach((product, index) => {
            console.log(`${index + 1}. "${product.plastic_name}" | "${product.mold_name}" | "${product.stamp_name}"`);
        });
        
        // Get sample of your actual plastics, molds, stamps
        console.log('\n📊 Sample of Your Database Values:\n');
        
        const { data: plastics, error: plasticsError } = await supabase
            .from('t_plastics')
            .select('plastic')
            .eq('brand_id', 6)  // Discraft only
            .limit(20);
        
        if (!plasticsError && plastics) {
            console.log('🎯 Sample Plastics in Your Database:');
            plastics.forEach((p, index) => {
                console.log(`${index + 1}. "${p.plastic}"`);
            });
        }
        
        const { data: molds, error: moldsError } = await supabase
            .from('t_molds')
            .select('mold')
            .eq('brand_id', 6)  // Discraft only
            .limit(20);
        
        if (!moldsError && molds) {
            console.log('\n🥏 Sample Molds in Your Database:');
            molds.forEach((m, index) => {
                console.log(`${index + 1}. "${m.mold}"`);
            });
        }
        
        const { data: stamps, error: stampsError } = await supabase
            .from('t_stamps')
            .select('stamp')
            .limit(20);
        
        if (!stampsError && stamps) {
            console.log('\n🏷️ Sample Stamps in Your Database:');
            stamps.forEach((s, index) => {
                console.log(`${index + 1}. "${s.stamp}"`);
            });
        }
        
        // Check for potential matches with fuzzy logic
        console.log('\n🔍 Potential Matching Issues:\n');
        
        // Check if any Discraft plastics contain common patterns
        const discraftPlastics = [...new Set(unmatchedProducts.map(p => p.plastic_name))];
        const dbPlastics = plastics ? plastics.map(p => p.plastic) : [];
        
        console.log('🎯 Discraft Plastics vs Database Plastics:');
        discraftPlastics.slice(0, 10).forEach(discraftPlastic => {
            const potentialMatches = dbPlastics.filter(dbPlastic => 
                dbPlastic.toLowerCase().includes(discraftPlastic.toLowerCase()) ||
                discraftPlastic.toLowerCase().includes(dbPlastic.toLowerCase())
            );
            
            if (potentialMatches.length > 0) {
                console.log(`   "${discraftPlastic}" might match: ${potentialMatches.join(', ')}`);
            } else {
                console.log(`   "${discraftPlastic}" - no obvious matches`);
            }
        });
        
        // Check molds
        const discraftMolds = [...new Set(unmatchedProducts.map(p => p.mold_name))];
        const dbMolds = molds ? molds.map(m => m.mold) : [];
        
        console.log('\n🥏 Discraft Molds vs Database Molds:');
        discraftMolds.slice(0, 10).forEach(discraftMold => {
            const potentialMatches = dbMolds.filter(dbMold => 
                dbMold.toLowerCase().includes(discraftMold.toLowerCase()) ||
                discraftMold.toLowerCase().includes(dbMold.toLowerCase())
            );
            
            if (potentialMatches.length > 0) {
                console.log(`   "${discraftMold}" might match: ${potentialMatches.join(', ')}`);
            } else {
                console.log(`   "${discraftMold}" - no obvious matches`);
            }
        });
        
        // Show successful matches for comparison
        const { data: matchedProducts, error: matchedError } = await supabase
            .from('it_discraft_order_sheet_lines')
            .select('plastic_name, mold_name, stamp_name, calculated_mps_id')
            .not('calculated_mps_id', 'is', null)
            .limit(10);
        
        if (!matchedError && matchedProducts) {
            console.log('\n✅ Sample Successful Matches:');
            matchedProducts.forEach((product, index) => {
                console.log(`${index + 1}. "${product.plastic_name}" | "${product.mold_name}" | "${product.stamp_name}" → MPS ${product.calculated_mps_id}`);
            });
        }
        
        console.log('\n💡 Recommendations:');
        console.log('1. Check if plastic names need normalization (e.g., "Elite Z" vs "Z")');
        console.log('2. Verify mold name parsing (e.g., compound names like "BuzzzSS")');
        console.log('3. Check stamp name standardization');
        console.log('4. Consider if you need to create missing MPS records for Discraft products');
        
    } catch (error) {
        console.error('❌ Error analyzing mismatches:', error);
    }
}

analyzeMismatches();
