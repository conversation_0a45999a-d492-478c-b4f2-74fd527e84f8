// Debug Overmold and INNfuse parsing

function parseInnovaDescription(description) {
    if (!description) return null;
    
    // Common Innova plastic types (order matters - longer names first)
    const plasticTypes = [
        'Overmold Star INNfuse', 'INNfuse Star', 'Overmold Champion', 'Overmold',
        'Champion I-Dye', 'Champion Proto Glow', 'Champion Classic Glow', 'Champion',
        'Star I-Dye', 'Halo Star', 'Star',
        'DX Proto Glow', 'DX Classic Glow', 'DX',
        'Pro KC', 'KC Pro', 'R-Pro', 'Pro',
        'Halo Nexus', 'Nexus',
        'Metal Flake Champion', 'GStar', 'G-Star', 'XT'
    ];
    
    let plastic = null;
    let remainingText = description.trim();
    
    console.log(`  Starting with: "${description}"`);
    
    // Find plastic type
    for (const plasticType of plasticTypes) {
        // Use word boundaries to prevent partial matches (e.g., "Pro" in "Approach")
        const regex = new RegExp(`\\b${plasticType}\\b`, 'gi');
        if (regex.test(description)) {
            plastic = plasticType;
            // Remove plastic from description to get mold
            remainingText = description.replace(regex, '').trim();
            console.log(`  Found plastic "${plasticType}", remaining: "${remainingText}"`);
            break;
        }
    }
    
    // Extract mold name (remove Innova disc type classifications - these are ignored for matching)
    let mold = remainingText
        .replace(/\s+(Putt & Approach|Fairway Distance Driver|Distance Driver|Fairway Driver|Mid-Range|Midrange|Specialty Disc|Putter|Driver|Approach)(\s+Disc)?$/i, '')
        .replace(/\s+Disc$/i, '') // Remove standalone "Disc" at the end
        .replace(/\s+with\s+.*/i, '') // Remove "with Burst Logo" etc
        .replace(/\s+Overmold\s*-?\s*$/i, '') // Remove "Overmold" and trailing dashes
        .replace(/\s*-\s*$/i, '') // Remove trailing dashes
        .trim();
    
    console.log(`  Final mold: "${mold}"`);
    
    return {
        plastic: plastic,
        mold: mold,
        original: description
    };
}

// Test the problematic cases
const testCases = [
    'Overmold Champion Atlas Mid-Range',
    'INNfuse Star Avatar Overmold - Mid-Range', 
    'DX Condor Specialty Disc'
];

console.log('=== OVERMOLD/INNFUSE PARSING DEBUG ===\n');

testCases.forEach(description => {
    console.log(`Testing: "${description}"`);
    const result = parseInnovaDescription(description);
    console.log(`  → Plastic: "${result?.plastic || 'NOT FOUND'}"`);
    console.log(`  → Mold: "${result?.mold || 'NOT FOUND'}"`);
    console.log('');
});

// Test plastic mappings
const plasticMapping = {
    'Overmold Champion': 'Overmold Champion',
    'Overmold Star INNfuse': 'INNfuse Star',
    'INNfuse Star': 'INNfuse Star'
};

function normalizePlasticName(plasticName) {
    if (!plasticName) return null;
    const mapped = plasticMapping[plasticName];
    if (mapped) return mapped;
    return plasticName;
}

console.log('=== PLASTIC MAPPING TEST ===');
console.log(`"Overmold Champion" → "${normalizePlasticName('Overmold Champion')}"`);
console.log(`"Overmold Star INNfuse" → "${normalizePlasticName('Overmold Star INNfuse')}"`);
console.log(`"INNfuse Star" → "${normalizePlasticName('INNfuse Star')}"`);

console.log('\n=== MATCHING TEST ===');
// Test if they would match
const testMatches = [
    { your: 'Overmold Champion', innova: 'Overmold Champion' },
    { your: 'Overmold Star INNfuse', innova: 'INNfuse Star' }
];

testMatches.forEach(test => {
    const yourNormalized = normalizePlasticName(test.your);
    const innovaNormalized = normalizePlasticName(test.innova);
    const match = yourNormalized?.toLowerCase() === innovaNormalized?.toLowerCase();
    console.log(`"${test.your}" vs "${test.innova}" → ${yourNormalized} vs ${innovaNormalized} → ${match ? '✅ MATCH' : '❌ NO MATCH'}`);
});
