import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

dotenv.config();

const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_KEY;
const supabase = createClient(supabaseUrl, supabaseKey);

async function checkTourSeriesStatus() {
    try {
        console.log('🔍 Checking Tour Series status in database...\n');
        
        // Check how many products still have "Tour" plastic
        const { count: tourCount, error: tourError } = await supabase
            .from('it_discraft_order_sheet_lines')
            .select('*', { count: 'exact', head: true })
            .eq('plastic_name', 'Tour');
        
        if (tourError) {
            console.error('Error getting Tour count:', tourError);
            return;
        }
        
        console.log(`❌ Products still with "Tour" plastic: ${tourCount}`);
        
        // Check how many products have "Elite Z Swirl" with Tour Series stamps
        const { count: eliteZTourCount, error: eliteZTourError } = await supabase
            .from('it_discraft_order_sheet_lines')
            .select('*', { count: 'exact', head: true })
            .eq('plastic_name', 'Elite Z Swirl')
            .ilike('stamp_name', '%2025 Tour Series%');
        
        if (eliteZTourError) {
            console.error('Error getting Elite Z Tour count:', eliteZTourError);
            return;
        }
        
        console.log(`✅ Products with "Elite Z Swirl" + Tour Series stamps: ${eliteZTourCount}`);
        
        // Show some examples of what's still "Tour"
        const { data: tourExamples, error: exampleError } = await supabase
            .from('it_discraft_order_sheet_lines')
            .select('id, plastic_name, mold_name, stamp_name, calculated_mps_id')
            .eq('plastic_name', 'Tour')
            .limit(10);
        
        if (!exampleError && tourExamples.length > 0) {
            console.log('\n📋 Examples of products still marked as "Tour":');
            tourExamples.forEach(product => {
                console.log(`   ID ${product.id}: "${product.plastic_name}" | "${product.mold_name}" | "${product.stamp_name}" | MPS: ${product.calculated_mps_id || 'NULL'}`);
            });
        }
        
        // Check if the fix actually worked for any products
        const { data: fixedExamples, error: fixedError } = await supabase
            .from('it_discraft_order_sheet_lines')
            .select('id, plastic_name, mold_name, stamp_name, calculated_mps_id')
            .eq('plastic_name', 'Elite Z Swirl')
            .ilike('stamp_name', '%2025 Tour Series%')
            .limit(5);
        
        if (!fixedError && fixedExamples.length > 0) {
            console.log('\n✅ Examples of properly fixed Tour Series products:');
            fixedExamples.forEach(product => {
                console.log(`   ID ${product.id}: "${product.plastic_name}" | "${product.mold_name}" | "${product.stamp_name}" | MPS: ${product.calculated_mps_id || 'NULL'}`);
            });
        }
        
        // Summary
        console.log('\n📊 Summary:');
        console.log(`   • Still "Tour": ${tourCount} products`);
        console.log(`   • Fixed to "Elite Z Swirl": ${eliteZTourCount} products`);
        
        if (tourCount > 0) {
            console.log('\n❌ The Tour Series fix did NOT work completely!');
            console.log('   Need to re-run the fix script.');
        } else {
            console.log('\n✅ All Tour Series products have been fixed!');
        }
        
    } catch (error) {
        console.error('❌ Check error:', error);
    }
}

checkTourSeriesStatus();
