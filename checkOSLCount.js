import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

dotenv.config();

const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_KEY;
const supabase = createClient(supabaseUrl, supabaseKey);

async function checkOSLCount() {
    try {
        // Get total count of Innova OSLs with vendor_internal_id
        const { count: totalCount, error: countError } = await supabase
            .from('t_order_sheet_lines')
            .select('*', { count: 'exact', head: true })
            .eq('vendor_id', 2)
            .not('vendor_internal_id', 'is', null);
        
        if (countError) {
            console.error('Error getting count:', countError);
            return;
        }
        
        console.log(`Total Innova OSLs with vendor_internal_id: ${totalCount}`);
        
        // Get count of unverified ones (what enhanced matching should process)
        const { count: unverifiedCount, error: unverifiedError } = await supabase
            .from('t_order_sheet_lines')
            .select('*', { count: 'exact', head: true })
            .eq('vendor_id', 2)
            .not('vendor_internal_id', 'is', null)
            .is('vendor_match_verified', null);
        
        if (unverifiedError) {
            console.error('Error getting unverified count:', unverifiedError);
            return;
        }
        
        console.log(`Unverified Innova OSLs: ${unverifiedCount}`);
        
        // Test what enhanced matching currently gets (first 1000)
        const { data: first1000, error: dataError } = await supabase
            .from('t_order_sheet_lines')
            .select('id, vendor_internal_id')
            .eq('vendor_id', 2)
            .not('vendor_internal_id', 'is', null)
            .is('vendor_match_verified', null)
            .limit(1000);
        
        if (dataError) {
            console.error('Error getting data:', dataError);
            return;
        }
        
        console.log(`Enhanced matching currently gets: ${first1000.length} records`);
        
        if (unverifiedCount > 1000) {
            console.log(`❌ MISSING ${unverifiedCount - 1000} OSL records from enhanced matching!`);
            
            // Check if OSL 18003 is in the first 1000
            const has18003 = first1000.some(r => r.id === 18003);
            console.log(`Does first 1000 include OSL 18003? ${has18003 ? 'YES' : 'NO'}`);
            
            if (!has18003) {
                console.log('❌ OSL 18003 is NOT in the first 1000 - that\'s why it shows as BROKEN_LINK!');
            }
        } else {
            console.log('✅ All unverified OSLs fit in first 1000 records');
        }
        
    } catch (error) {
        console.error('Check failed:', error);
    }
}

checkOSLCount();
