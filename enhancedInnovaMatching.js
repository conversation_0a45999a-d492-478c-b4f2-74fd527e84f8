import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

// Initialize Supabase client
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_KEY;
const supabase = createClient(supabaseUrl, supabaseKey);

// Plastic name mapping - maps your plastic names to Innova's plastic names
const plasticMapping = {
    // Your plastic name: Innova plastic name
    'KC Pro': 'Pro KC',
    'Pro KC': 'Pro KC',  // Already matches
    'Champion': 'Champion',
    'Star': 'Star',
    'DX': 'DX',
    'Pro': 'Pro',
    'XT': 'XT',
    'GStar': 'G-Star',
    'G-Star': 'G-Star',
    'R-Pro': 'R-Pro',
    'Nexus': 'Nexus',

    // Glow plastic mappings
    'DX Glow': 'DX Classic Glow',
    'Champion Glow': 'Champion Classic Glow',
    'Proto Glow Champion': 'Champion Proto Glow',
    'Proto Glow DX': 'DX Proto Glow',

    // Halo plastic mappings
    'Halo Star': 'Halo Star',
    'Halo Nexus': 'Halo Nexus',
    'Proto Glow Halo Star': 'Halo Proto Glow Star',

    // Overmold and INNfuse plastic mappings
    'Overmold Champion': 'Overmold Champion',
    'Overmold Star INNfuse': 'INNfuse Star',
    'INNfuse Star': 'INNfuse Star',

    // Blizzard plastic mappings
    'Champion Blizzard': 'Blizzard Champion',

    // Direct matches (for clarity)
    'R-Pro': 'R-Pro'
};

// Mold name mapping - maps your mold names to Innova's mold names
const moldMapping = {
    // Your mold name: Innova mold name
    'AviardX3': 'Aviar X3',
    'AviarX3': 'Aviar X3',
    'Aviar3': 'Aviar3', // No mapping needed - should match directly
    'Mako3': 'Mako 3',
    'Aviar Classic': 'Classic Aviar',
    'Aviar Putter': 'Aviar',
    'Aviar P&A': 'Aviar', // Alternative name for Aviar Putter
    'Leopard3': 'Leopard 3',
    'Teebird3': 'Teebird 3',
    'Roc3': 'Roc 3',
    'TL3': 'TL 3'
};

// Normalize plastic name for comparison
function normalizePlasticName(plasticName) {
    if (!plasticName) return null;

    // Check if we have a direct mapping
    const mapped = plasticMapping[plasticName];
    if (mapped) return mapped;

    // If no mapping found, return original (case-insensitive)
    return plasticName;
}

// Normalize mold name for comparison
function normalizeMoldName(moldName) {
    if (!moldName) return null;

    // Check if we have a direct mapping
    const mapped = moldMapping[moldName];
    if (mapped) return mapped;

    // If no mapping found, return original (case-insensitive)
    return moldName;
}

// Parse Innova description to extract mold and plastic
function parseInnovaDescription(description) {
    if (!description) return null;
    
    // Common Innova plastic types (order matters - longer names first)
    const plasticTypes = [
        'Overmold Star INNfuse', 'INNfuse Star', 'Overmold Champion', 'Overmold',
        'Champion I-Dye', 'Champion Proto Glow', 'Champion Classic Glow', 'Blizzard Champion', 'Champion',
        'Star I-Dye', 'Halo Star', 'Star',
        'DX Proto Glow', 'DX Classic Glow', 'DX',
        'Pro KC', 'KC Pro', 'R-Pro', 'Pro',
        'Halo Nexus', 'Nexus',
        'Metal Flake Champion', 'GStar', 'G-Star', 'XT'
    ];
    
    let plastic = null;
    let remainingText = description.trim();
    
    // Find plastic type
    for (const plasticType of plasticTypes) {
        // Use word boundaries to prevent partial matches (e.g., "Pro" in "Approach")
        const regex = new RegExp(`\\b${plasticType}\\b`, 'gi');
        if (regex.test(description)) {
            plastic = plasticType;
            // Remove plastic from description to get mold
            remainingText = description.replace(regex, '').trim();
            break;
        }
    }
    
    // Extract mold name (remove Innova disc type classifications - these are ignored for matching)
    let mold = remainingText
        .replace(/\s+(Putt & Approach|Fairway Distance Driver|Distance Driver|Fairway Driver|Mid-Range|Midrange|Specialty Disc|Putter|Driver|Approach)(\s+Disc)?$/i, '')
        .replace(/\s+Disc$/i, '') // Remove standalone "Disc" at the end
        .replace(/\s+with\s+.*/i, '') // Remove "with Burst Logo" etc
        .replace(/\s+Overmold\s*-?\s*$/i, '') // Remove "Overmold" and trailing dashes
        .replace(/\s*-\s*$/i, '') // Remove trailing dashes
        .trim();
    
    return {
        plastic: plastic,
        mold: mold,
        original: description
    };
}

// Parse weight range from Innova matrix_option_1
function parseInnovaWeights(weightStr) {
    if (!weightStr) return null;

    const str = weightStr.toString().toLowerCase().trim();

    // Handle "max weight" or similar
    if (str.includes('max') || str.includes('minimum') || str.includes('varies')) {
        return { error: 'unparseable', original: weightStr };
    }

    // Handle "<139" format (means up to 139g, typically 100-139g for discs)
    const lessThanMatch = str.match(/^<\s*(\d+)/);
    if (lessThanMatch) {
        const maxWeight = parseInt(lessThanMatch[1]);
        return {
            min: 100, // Assume minimum disc weight is 100g
            max: maxWeight,
            original: weightStr,
            note: 'parsed_less_than'
        };
    }

    // Handle ">175" format (means 175g and up, typically 175-180g for discs)
    const greaterThanMatch = str.match(/^>\s*(\d+)/);
    if (greaterThanMatch) {
        const minWeight = parseInt(greaterThanMatch[1]);
        return {
            min: minWeight,
            max: 180, // Assume maximum disc weight is 180g
            original: weightStr,
            note: 'parsed_greater_than'
        };
    }

    // Look for pattern like "173-175" or "173-175g"
    const rangeMatch = str.match(/(\d+)\s*-\s*(\d+)/);
    if (rangeMatch) {
        return {
            min: parseInt(rangeMatch[1]),
            max: parseInt(rangeMatch[2]),
            original: weightStr
        };
    }

    // Look for single number like "175g" or "175"
    const singleMatch = str.match(/(\d+)/);
    if (singleMatch) {
        const weight = parseInt(singleMatch[1]);
        return {
            min: weight,
            max: weight,
            original: weightStr
        };
    }

    return { error: 'unparseable', original: weightStr };
}

// Enhanced matching using relational data
async function enhancedInnovaMatching() {
    try {
        console.log('Starting enhanced Innova matching using relational data...');
        
        // Get OSLs with their MPS data (exclude already verified matches)
        // Note: Currently fits in 1000 limit, but chunking for future-proofing
        const { data: oslData, error: oslError } = await supabase
            .from('t_order_sheet_lines')
            .select(`
                id, vendor_internal_id, g_code, min_weight, max_weight, color_id, mps_id,
                vendor_match_verified, vendor_match_verified_at,
                t_mps!inner (
                    id, g_code,
                    t_molds!inner (id, mold),
                    t_plastics!inner (id, plastic),
                    t_stamps!inner (id, stamp)
                ),
                t_colors!inner (id, color)
            `)
            .eq('vendor_id', 2)
            .not('vendor_internal_id', 'is', null)
            .is('vendor_match_verified', null); // Only unverified matches (NULL values only)

        if (oslError) {
            throw new Error(`Error fetching OSL data: ${oslError.message}`);
        }

        console.log(`Found ${oslData.length} Innova OSLs with MPS data`);
        
        // Get all Innova data with chunking (Supabase has 1000 record limit)
        console.log('Fetching all Innova data...');
        let innovaData = [];
        let offset = 0;
        const chunkSize = 1000;

        while (true) {
            const { data: chunk, error: chunkError } = await supabase
                .from('it_innova_order_sheet_lines')
                .select('internal_id, description, matrix_option_1')
                .range(offset, offset + chunkSize - 1);

            if (chunkError) {
                throw new Error(`Error fetching Innova data chunk: ${chunkError.message}`);
            }

            if (!chunk || chunk.length === 0) {
                break; // No more data
            }

            innovaData = innovaData.concat(chunk);
            offset += chunkSize;

            console.log(`Loaded ${innovaData.length} Innova records so far...`);

            if (chunk.length < chunkSize) {
                break; // Last chunk
            }
        }

        console.log(`Found ${innovaData.length} total Innova records`);
        
        // Create lookup map for Innova data
        const innovaMap = {};
        innovaData.forEach(record => {
            innovaMap[record.internal_id] = record;
        });
        
        // Enhanced validation results
        const enhancedResults = [];
        let exactMatches = 0;
        let moldMatches = 0;
        let weightMatches = 0;
        let noMatches = 0;

        // Weight parsing stats
        let lessThanParsed = 0;
        let greaterThanParsed = 0;
        let rangeParsed = 0;
        let unparseable = 0;
        
        for (const osl of oslData) {
            const innovaRecord = innovaMap[osl.vendor_internal_id];
            
            if (!innovaRecord) {
                enhancedResults.push({
                    osl_id: osl.id,
                    mps_id: osl.mps_id,
                    vendor_internal_id: osl.vendor_internal_id,
                    osl_mold: osl.t_mps.t_molds.mold,
                    osl_mold_normalized: normalizeMoldName(osl.t_mps.t_molds.mold),
                    osl_plastic: osl.t_mps.t_plastics.plastic,
                    osl_plastic_normalized: normalizePlasticName(osl.t_mps.t_plastics.plastic),
                    osl_stamp: osl.t_mps.t_stamps.stamp,
                    osl_weights: `${osl.min_weight}-${osl.max_weight}`,
                    osl_color: osl.t_colors.color,
                    innova_description: null,
                    innova_parsed: null,
                    innova_mold_normalized: null,
                    innova_plastic_normalized: null,
                    innova_weights: null,
                    mold_match: false,
                    plastic_match: false,
                    weight_match: 'BROKEN_LINK',
                    confidence_score: 0,
                    status: '🔴 BROKEN_LINK',
                    recommendation: 'CLEAR_CONNECTION'
                });
                continue;
            }
            
            // Parse Innova description
            const innovaParsed = parseInnovaDescription(innovaRecord.description);
            const innovaWeights = parseInnovaWeights(innovaRecord.matrix_option_1);

            // Track weight parsing stats
            if (innovaWeights) {
                if (innovaWeights.error) {
                    unparseable++;
                } else if (innovaWeights.note === 'parsed_less_than') {
                    lessThanParsed++;
                } else if (innovaWeights.note === 'parsed_greater_than') {
                    greaterThanParsed++;
                } else {
                    rangeParsed++;
                }
            }
            
            // Mold matching with normalization
            const oslMoldNormalized = normalizeMoldName(osl.t_mps.t_molds.mold);
            const innovaMoldNormalized = normalizeMoldName(innovaParsed?.mold);
            const moldMatch = oslMoldNormalized?.toLowerCase() === innovaMoldNormalized?.toLowerCase();

            // Plastic matching with normalization
            const oslPlasticNormalized = normalizePlasticName(osl.t_mps.t_plastics.plastic);
            const innovaPlasticNormalized = normalizePlasticName(innovaParsed?.plastic);
            const plasticMatch = oslPlasticNormalized?.toLowerCase() === innovaPlasticNormalized?.toLowerCase();
            
            let weightMatch = 'NO_MATCH';
            if (innovaWeights && !innovaWeights.error) {
                if (osl.min_weight === innovaWeights.min && osl.max_weight === innovaWeights.max) {
                    weightMatch = 'EXACT_MATCH';
                } else if (osl.min_weight <= innovaWeights.max && osl.max_weight >= innovaWeights.min) {
                    weightMatch = 'OVERLAP';
                } else {
                    weightMatch = 'NO_OVERLAP';
                }
            } else if (innovaWeights?.error) {
                weightMatch = 'UNPARSEABLE';
            }
            
            // Calculate confidence score
            let confidenceScore = 0;
            if (moldMatch) confidenceScore += 40;
            if (plasticMatch) confidenceScore += 30;
            if (weightMatch === 'EXACT_MATCH') confidenceScore += 30;
            else if (weightMatch === 'OVERLAP') confidenceScore += 15;
            
            // Determine status and recommendation
            let status, recommendation;
            if (moldMatch && plasticMatch && weightMatch === 'EXACT_MATCH') {
                status = '🟢 PERFECT_MATCH';
                recommendation = 'AUTO_CONFIRM';
                exactMatches++;
            } else if (moldMatch && (plasticMatch || weightMatch === 'EXACT_MATCH')) {
                status = '🟡 GOOD_MATCH';
                recommendation = 'REVIEW_CONFIRM';
                moldMatches++;
            } else if (moldMatch || weightMatch === 'EXACT_MATCH') {
                status = '🟠 PARTIAL_MATCH';
                recommendation = 'MANUAL_REVIEW';
                weightMatches++;
            } else {
                status = '🔴 POOR_MATCH';
                recommendation = 'LIKELY_INCORRECT';
                noMatches++;
            }
            
            enhancedResults.push({
                osl_id: osl.id,
                mps_id: osl.mps_id,
                vendor_internal_id: osl.vendor_internal_id,
                osl_mold: osl.t_mps.t_molds.mold,
                osl_mold_normalized: oslMoldNormalized,
                osl_plastic: osl.t_mps.t_plastics.plastic,
                osl_plastic_normalized: oslPlasticNormalized,
                osl_stamp: osl.t_mps.t_stamps.stamp,
                osl_weights: `${osl.min_weight}-${osl.max_weight}`,
                osl_color: osl.t_colors.color,
                innova_description: innovaRecord.description,
                innova_parsed: innovaParsed,
                innova_mold_normalized: innovaMoldNormalized,
                innova_plastic_normalized: innovaPlasticNormalized,
                innova_weights: innovaWeights?.error ? innovaWeights.original :
                    `${innovaWeights?.min}-${innovaWeights?.max}${innovaWeights?.note ? ` (${innovaWeights.note})` : ''}`,
                mold_match: moldMatch,
                plastic_match: plasticMatch,
                weight_match: weightMatch,
                confidence_score: confidenceScore,
                status: status,
                recommendation: recommendation
            });
        }
        
        console.log('\n=== ENHANCED MATCHING RESULTS ===');
        console.log(`Perfect matches (🟢): ${exactMatches}`);
        console.log(`Good matches (🟡): ${moldMatches}`);
        console.log(`Partial matches (🟠): ${weightMatches}`);
        console.log(`Poor matches (🔴): ${noMatches}`);

        console.log('\n=== WEIGHT PARSING STATS ===');
        console.log(`"<139" format parsed: ${lessThanParsed}`);
        console.log(`">175" format parsed: ${greaterThanParsed}`);
        console.log(`Range format parsed: ${rangeParsed}`);
        console.log(`Unparseable weights: ${unparseable}`);
        
        return {
            success: true,
            totalProcessed: oslData.length,
            perfectMatches: exactMatches,
            goodMatches: moldMatches,
            partialMatches: weightMatches,
            poorMatches: noMatches,
            enhancedResults: enhancedResults
        };
        
    } catch (error) {
        console.error('Enhanced matching failed:', error);
        return {
            success: false,
            error: error.message
        };
    }
}

// Store enhanced results in memory
let lastEnhancedResults = null;

async function runEnhancedMatching() {
    const result = await enhancedInnovaMatching();
    if (result.success) {
        lastEnhancedResults = result.enhancedResults;
    }
    return result;
}

function getEnhancedResults() {
    return lastEnhancedResults;
}

// Alias for admin server compatibility
function getEnhancedValidationResults() {
    return lastEnhancedResults;
}

// Run if executed directly
if (process.argv[1] && process.argv[1].endsWith('enhancedInnovaMatching.js')) {
    console.log('Running enhanced Innova matching...');
    runEnhancedMatching()
        .then(result => {
            if (result.success) {
                console.log('Enhanced matching completed successfully');
                console.log(`Perfect matches: ${result.perfectMatches}`);
                console.log(`Good matches: ${result.goodMatches}`);
                console.log(`Partial matches: ${result.partialMatches}`);
                console.log(`Poor matches: ${result.poorMatches}`);
                
                // Show first few perfect matches
                if (result.enhancedResults) {
                    const perfectMatches = result.enhancedResults.filter(r => r.status === '🟢 PERFECT_MATCH');
                    if (perfectMatches.length > 0) {
                        console.log('\nFirst 3 perfect matches:');
                        perfectMatches.slice(0, 3).forEach((record, index) => {
                            console.log(`${index + 1}. OSL ${record.osl_id}: ${record.osl_mold} ${record.osl_plastic} ↔ ${record.innova_description}`);
                        });
                    }
                }
                
                process.exit(0);
            } else {
                console.error('Enhanced matching failed:', result.error);
                process.exit(1);
            }
        })
        .catch(error => {
            console.error('Unexpected error:', error);
            process.exit(1);
        });
}

export { runEnhancedMatching, getEnhancedResults, getEnhancedValidationResults };
