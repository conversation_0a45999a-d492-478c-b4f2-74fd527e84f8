import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

dotenv.config();

const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_KEY;
const supabase = createClient(supabaseUrl, supabaseKey);

async function testRejectionsTable() {
    try {
        console.log('Testing t_vendor_match_rejections table...');
        
        // Try to query the table to see if it exists
        const { data, error } = await supabase
            .from('t_vendor_match_rejections')
            .select('*')
            .limit(1);
        
        if (error) {
            if (error.message.includes('does not exist')) {
                console.log('❌ Table does not exist. Please run:');
                console.log('   create_vendor_match_rejections_table.sql');
                console.log('   in your Supabase SQL editor');
            } else {
                console.log('❌ Error accessing table:', error.message);
            }
        } else {
            console.log('✅ Table exists and is accessible');
            console.log(`   Current records: ${data.length}`);
            
            // Test insert (will rollback)
            console.log('Testing insert...');
            const { data: insertData, error: insertError } = await supabase
                .from('t_vendor_match_rejections')
                .insert({
                    osl_id: 99999,
                    vendor_id: 2,
                    vendor_internal_id: 99999,
                    rejected_by: 'test'
                })
                .select();
            
            if (insertError) {
                console.log('❌ Insert test failed:', insertError.message);
            } else {
                console.log('✅ Insert test successful');
                
                // Clean up test record
                await supabase
                    .from('t_vendor_match_rejections')
                    .delete()
                    .eq('osl_id', 99999)
                    .eq('vendor_internal_id', 99999);
                
                console.log('✅ Test record cleaned up');
            }
        }
        
    } catch (error) {
        console.error('Test failed:', error);
    }
}

testRejectionsTable();
