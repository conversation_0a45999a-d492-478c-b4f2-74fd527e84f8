// processDeleteSkuFromShopifyTask.js
import dotenv from 'dotenv';
dotenv.config();

import { createClient } from '@supabase/supabase-js';
import fetch from 'node-fetch';

// Initialize Supabase client
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_KEY;
const supabase = createClient(supabaseUrl, supabaseKey);

// Shopify credentials
const shopifyEndpoint = process.env.SHOPIFY_ENDPOINT;
const shopifyAccessToken = process.env.SHOPIFY_ACCESS_TOKEN;

if (!shopifyEndpoint || !shopifyAccessToken) {
  console.error('ERROR: Missing Shopify endpoint or access token in environment variables.');
  process.exit(1);
}

/**
 * Execute a GraphQL request to Shopify
 * @param {string} query - The GraphQL query or mutation
 * @param {Object} variables - Variables for the GraphQL query
 * @returns {Promise<Object>} - The response data
 */
async function executeShopifyGraphQL(query, variables = {}) {
  try {
    const response = await fetch(shopifyEndpoint, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-Shopify-Access-Token': shopifyAccessToken
      },
      body: JSON.stringify({ query, variables })
    });

    if (!response.ok) {
      throw new Error(`HTTP error! Status: ${response.status} ${response.statusText}`);
    }

    const result = await response.json();

    if (result.errors) {
      throw new Error(`GraphQL errors: ${JSON.stringify(result.errors)}`);
    }

    return result.data;
  } catch (error) {
    console.error('Error executing Shopify GraphQL request:', error);
    throw error;
  }
}

/**
 * Find a product variant by SKU using GraphQL
 * @param {string} sku - The SKU to search for
 * @returns {Promise<Object|null>} - The variant and product information or null if not found
 */
async function findVariantBySku(sku) {
  try {
    console.log(`[processDeleteSkuFromShopifyTask] Finding variant by SKU: ${sku}`);

    const query = `
      query getVariantBySku($query: String!) {
        productVariants(first: 1, query: $query) {
          edges {
            node {
              id
              sku
              product {
                id
                title
                handle
              }
            }
          }
        }
      }
    `;

    const variables = {
      query: `sku:${sku}`
    };

    const result = await executeShopifyGraphQL(query, variables);

    if (!result.productVariants.edges.length) {
      console.log(`[processDeleteSkuFromShopifyTask] No variant found with SKU: ${sku}`);
      return null;
    }

    const variant = result.productVariants.edges[0].node;
    
    console.log(`[processDeleteSkuFromShopifyTask] Found variant ${variant.id} for SKU: ${sku}`);
    console.log(`[processDeleteSkuFromShopifyTask] Product: ${variant.product.title} (${variant.product.id})`);

    return {
      variantId: variant.id,
      sku: variant.sku,
      product: {
        id: variant.product.id,
        title: variant.product.title,
        handle: variant.product.handle
      }
    };
  } catch (error) {
    console.error(`[processDeleteSkuFromShopifyTask] Error finding variant by SKU ${sku}:`, error);
    throw error;
  }
}

/**
 * Delete a product by its ID using GraphQL
 * @param {string} productId - The Shopify product ID (gid://shopify/Product/...)
 * @returns {Promise<Object>} - The deletion result
 */
async function deleteProduct(productId) {
  try {
    console.log(`[processDeleteSkuFromShopifyTask] Deleting product: ${productId}`);

    const mutation = `
      mutation productDelete($input: ProductDeleteInput!) {
        productDelete(input: $input) {
          deletedProductId
          userErrors {
            field
            message
          }
        }
      }
    `;

    const variables = {
      input: {
        id: productId
      }
    };

    const result = await executeShopifyGraphQL(mutation, variables);

    if (result.productDelete.userErrors && result.productDelete.userErrors.length > 0) {
      const errors = result.productDelete.userErrors.map(err => `${err.field}: ${err.message}`).join(', ');
      throw new Error(`Shopify deletion errors: ${errors}`);
    }

    console.log(`[processDeleteSkuFromShopifyTask] Successfully deleted product: ${result.productDelete.deletedProductId}`);
    
    return {
      success: true,
      deletedProductId: result.productDelete.deletedProductId
    };
  } catch (error) {
    console.error(`[processDeleteSkuFromShopifyTask] Error deleting product ${productId}:`, error);
    throw error;
  }
}

/**
 * Process a delete_sku_from_shopify task
 * @param {Object} task - The task object from the queue
 * @param {Object} context - Context object with supabase, updateTaskStatus, and logError functions
 */
export default async function processDeleteSkuFromShopifyTask(task, { supabase, updateTaskStatus, logError }) {
  console.log(`[processDeleteSkuFromShopifyTask] Processing task ${task.id} to delete SKU from Shopify`);

  try {
    // Update task to 'processing' status
    await updateTaskStatus(task.id, 'processing');

    // Extract payload
    const payload = task.payload;
    if (!payload || !payload.sku) {
      throw new Error('Task payload must contain a SKU to delete');
    }

    const { sku, reason } = payload;
    
    console.log(`[processDeleteSkuFromShopifyTask] Deleting Shopify product for SKU: ${sku}`);
    if (reason) {
      console.log(`[processDeleteSkuFromShopifyTask] Deletion reason: ${reason}`);
    }

    // Step 1: Find the variant by SKU
    const variantInfo = await findVariantBySku(sku);
    
    if (!variantInfo) {
      // SKU not found in Shopify - this might be expected
      console.log(`[processDeleteSkuFromShopifyTask] SKU ${sku} not found in Shopify - nothing to delete`);
      
      await updateTaskStatus(task.id, 'completed', {
        message: `SKU ${sku} not found in Shopify - nothing to delete`,
        sku: sku,
        found: false,
        reason: reason || 'No reason provided'
      });
      return;
    }

    // Step 2: Delete the product
    const deletionResult = await deleteProduct(variantInfo.product.id);
    
    // Step 3: Mark task as completed
    await updateTaskStatus(task.id, 'completed', {
      message: `Successfully deleted product for SKU ${sku} from Shopify`,
      sku: sku,
      deletedProductId: deletionResult.deletedProductId,
      productTitle: variantInfo.product.title,
      productHandle: variantInfo.product.handle,
      reason: reason || 'No reason provided'
    });

    console.log(`[processDeleteSkuFromShopifyTask] Successfully completed deletion task for SKU: ${sku}`);

  } catch (error) {
    const errorMessage = `Failed to delete SKU ${task.payload?.sku || 'unknown'} from Shopify: ${error.message}`;
    console.error(`[processDeleteSkuFromShopifyTask] ${errorMessage}`);
    
    await logError(errorMessage, `Processing delete_sku_from_shopify task ${task.id}`);
    
    await updateTaskStatus(task.id, 'error', {
      message: errorMessage,
      error: error.message,
      sku: task.payload?.sku || 'unknown',
      reason: task.payload?.reason || 'No reason provided'
    });
  }
}

/**
 * Helper function to enqueue a delete SKU from Shopify task
 * @param {Object} supabase - Supabase client
 * @param {string} sku - The SKU to delete
 * @param {string} reason - Optional reason for deletion
 * @param {Date} scheduledAt - Optional scheduled time (defaults to 5 minutes from now)
 */
export async function enqueueDeleteSkuFromShopifyTask(supabase, sku, reason = null, scheduledAt = null) {
  if (!sku) {
    throw new Error('SKU is required to enqueue delete SKU from Shopify task');
  }
  
  const now = new Date();
  const defaultScheduledAt = new Date(now.getTime() + 5 * 60 * 1000); // 5 minutes from now
  
  const task = {
    task_type: 'delete_sku_from_shopify',
    payload: {
      sku: sku,
      reason: reason || 'Scheduled deletion'
    },
    status: 'pending',
    scheduled_at: (scheduledAt || defaultScheduledAt).toISOString(),
    created_at: now.toISOString(),
    enqueued_by: 'enqueueDeleteSkuFromShopifyTask'
  };
  
  console.log(`[enqueueDeleteSkuFromShopifyTask] Enqueueing delete SKU from Shopify task for SKU: ${sku}`);
  
  const { data, error } = await supabase
    .from('t_task_queue')
    .insert(task)
    .select()
    .single();
  
  if (error) {
    console.error(`[enqueueDeleteSkuFromShopifyTask] Error enqueueing task:`, error);
    throw error;
  }
  
  console.log(`[enqueueDeleteSkuFromShopifyTask] Successfully enqueued task ${data.id} for SKU: ${sku}`);
  return data;
}
