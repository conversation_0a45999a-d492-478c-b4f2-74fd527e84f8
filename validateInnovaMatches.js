import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

// Initialize Supabase client
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_KEY;
const supabase = createClient(supabaseUrl, supabaseKey);

// String similarity function (Jaro-Winkler-like)
function calculateStringSimilarity(str1, str2) {
    if (!str1 || !str2) return 0;
    
    str1 = str1.toLowerCase().trim();
    str2 = str2.toLowerCase().trim();
    
    if (str1 === str2) return 100;
    
    // Simple similarity based on common words and character overlap
    const words1 = str1.split(/\s+/);
    const words2 = str2.split(/\s+/);
    
    let commonWords = 0;
    words1.forEach(word1 => {
        if (words2.some(word2 => word2.includes(word1) || word1.includes(word2))) {
            commonWords++;
        }
    });
    
    const wordSimilarity = (commonWords / Math.max(words1.length, words2.length)) * 100;
    
    // Character-level similarity
    const longer = str1.length > str2.length ? str1 : str2;
    const shorter = str1.length > str2.length ? str2 : str1;
    
    let matches = 0;
    for (let i = 0; i < shorter.length; i++) {
        if (longer.includes(shorter[i])) matches++;
    }
    
    const charSimilarity = (matches / longer.length) * 100;
    
    // Weighted average
    return Math.round((wordSimilarity * 0.7 + charSimilarity * 0.3));
}

// Parse weight range from string like "173-175g" or "173-175"
function parseWeightRange(weightStr) {
    if (!weightStr) return null;
    
    const str = weightStr.toString().toLowerCase().trim();
    
    // Handle "max weight" or similar
    if (str.includes('max') || str.includes('minimum') || str.includes('varies')) {
        return { error: 'unparseable', original: weightStr };
    }
    
    // Look for pattern like "173-175" or "173-175g"
    const rangeMatch = str.match(/(\d+)\s*-\s*(\d+)/);
    if (rangeMatch) {
        return {
            min: parseInt(rangeMatch[1]),
            max: parseInt(rangeMatch[2]),
            original: weightStr
        };
    }
    
    // Look for single number like "175g" or "175"
    const singleMatch = str.match(/(\d+)/);
    if (singleMatch) {
        const weight = parseInt(singleMatch[1]);
        return {
            min: weight,
            max: weight,
            original: weightStr
        };
    }
    
    return { error: 'unparseable', original: weightStr };
}

// Compare weight ranges
function compareWeights(oslMin, oslMax, innovaWeights) {
    if (!innovaWeights || innovaWeights.error) {
        return innovaWeights?.error === 'unparseable' ? 'UNPARSEABLE' : 'NO_INNOVA_WEIGHTS';
    }
    
    if (!oslMin || !oslMax) {
        return 'NO_OSL_WEIGHTS';
    }
    
    const { min: innovaMin, max: innovaMax } = innovaWeights;
    
    // Exact match
    if (oslMin === innovaMin && oslMax === innovaMax) {
        return 'EXACT_MATCH';
    }
    
    // Check for overlap
    if (oslMin <= innovaMax && oslMax >= innovaMin) {
        return 'OVERLAP';
    }
    
    return 'NO_OVERLAP';
}

// Determine status based on similarity and weight match
function determineStatus(stringSimilarity, weightMatch) {
    if (weightMatch === 'EXACT_MATCH' && stringSimilarity >= 85) {
        return '🟢 HIGH_CONFIDENCE';
    }
    
    if (weightMatch === 'OVERLAP' && stringSimilarity >= 70) {
        return '🟡 MEDIUM_CONFIDENCE';
    }
    
    if (weightMatch === 'UNPARSEABLE' || stringSimilarity < 70 || weightMatch === 'NO_OVERLAP') {
        return '🟠 LOW_CONFIDENCE';
    }
    
    return '⚪ NEEDS_REVIEW';
}

async function validateExistingMatches(autoClearBrokenLinks = false) {
    try {
        console.log('Starting validation of existing OSL ↔ Innova matches...');
        console.log(`Auto-clear broken links: ${autoClearBrokenLinks ? 'ENABLED' : 'DISABLED'}`);
        
        // Get all Innova OSLs with vendor_internal_id (vendor_id = 2)
        const { data: oslData, error: oslError } = await supabase
            .from('t_order_sheet_lines')
            .select('id, vendor_internal_id, g_code, min_weight, max_weight, vendor_id')
            .eq('vendor_id', 2)
            .not('vendor_internal_id', 'is', null);
        
        if (oslError) {
            throw new Error(`Error fetching OSL data: ${oslError.message}`);
        }
        
        console.log(`Found ${oslData.length} Innova OSLs (vendor_id=2) with vendor_internal_id`);
        
        // Get all Innova data
        const { data: innovaData, error: innovaError } = await supabase
            .from('it_innova_order_sheet_lines')
            .select('internal_id, description, matrix_option_1');
        
        if (innovaError) {
            throw new Error(`Error fetching Innova data: ${innovaError.message}`);
        }
        
        console.log(`Found ${innovaData.length} Innova records`);
        
        // Create lookup map for Innova data
        const innovaMap = {};
        innovaData.forEach(record => {
            innovaMap[record.internal_id] = record;
        });
        
        // Validate each OSL
        const validationResults = [];
        let validMatches = 0;
        let questionableMatches = 0;
        let brokenLinks = 0;
        let autoCleared = 0;
        
        for (const osl of oslData) {
            const innovaRecord = innovaMap[osl.vendor_internal_id];
            
            if (!innovaRecord) {
                // Broken link - only clear if auto-clear is enabled
                if (autoClearBrokenLinks) {
                    const { error: updateError } = await supabase
                        .from('t_order_sheet_lines')
                        .update({ vendor_internal_id: null })
                        .eq('id', osl.id)
                        .eq('vendor_id', 2);  // SAFETY: Only update Innova records

                    if (updateError) {
                        console.error(`Error clearing broken link for OSL ${osl.id}:`, updateError);
                    } else {
                        autoCleared++;
                    }
                } else {
                    console.log(`Broken link found for OSL ${osl.id} (vendor_internal_id=${osl.vendor_internal_id}) - NOT auto-clearing`);
                }
                
                validationResults.push({
                    osl_id: osl.id,
                    vendor_internal_id: osl.vendor_internal_id,
                    g_code: osl.g_code,
                    min_weight: osl.min_weight,
                    max_weight: osl.max_weight,
                    innova_description: null,
                    innova_weights: null,
                    string_similarity: 0,
                    weight_match: 'BROKEN_LINK',
                    status: '🔴 BROKEN_LINK',
                    action: autoClearBrokenLinks ? 'AUTO_CLEARED' : 'FLAGGED_FOR_REVIEW'
                });
                
                brokenLinks++;
                continue;
            }
            
            // Calculate similarity
            const stringSimilarity = calculateStringSimilarity(osl.g_code, innovaRecord.description);
            const innovaWeights = parseWeightRange(innovaRecord.matrix_option_1);
            const weightMatch = compareWeights(osl.min_weight, osl.max_weight, innovaWeights);
            const status = determineStatus(stringSimilarity, weightMatch);
            
            validationResults.push({
                osl_id: osl.id,
                vendor_internal_id: osl.vendor_internal_id,
                g_code: osl.g_code,
                min_weight: osl.min_weight,
                max_weight: osl.max_weight,
                innova_description: innovaRecord.description,
                innova_weights: innovaWeights?.error ? innovaWeights.original : `${innovaWeights?.min}-${innovaWeights?.max}`,
                string_similarity: stringSimilarity,
                weight_match: weightMatch,
                status: status,
                action: 'NONE'
            });
            
            if (status.includes('HIGH_CONFIDENCE') || status.includes('MEDIUM_CONFIDENCE')) {
                validMatches++;
            } else {
                questionableMatches++;
            }
        }
        
        // Store validation results (you might want to create a table for this)
        console.log('Validation completed');
        console.log(`Total matches processed: ${oslData.length}`);
        console.log(`Valid matches: ${validMatches}`);
        console.log(`Questionable matches: ${questionableMatches}`);
        console.log(`Broken links: ${brokenLinks}`);
        console.log(`Auto-cleared: ${autoCleared}`);
        
        return {
            success: true,
            totalMatches: oslData.length,
            validMatches,
            questionableMatches,
            brokenLinks,
            autoCleared,
            validationResults
        };
        
    } catch (error) {
        console.error('Validation failed:', error);
        return {
            success: false,
            error: error.message
        };
    }
}

// Store validation results in memory for now (in production, you'd want a proper table)
let lastValidationResults = null;

async function runValidation(autoClearBrokenLinks = false) {
    const result = await validateExistingMatches(autoClearBrokenLinks);
    if (result.success) {
        lastValidationResults = result.validationResults;
    }
    return result;
}

function getValidationResults() {
    return lastValidationResults;
}

// Run validation if this file is executed directly
if (process.argv[1] && process.argv[1].endsWith('validateInnovaMatches.js')) {
    console.log('Running Innova match validation...');
    console.log('⚠️  SAFETY MODE: Auto-clearing is DISABLED by default');
    console.log('   Use runValidation(true) to enable auto-clearing of broken links');
    runValidation(false)  // SAFETY: Default to no auto-clearing
        .then(result => {
            if (result.success) {
                console.log('Validation completed successfully');
                console.log(`Total matches: ${result.totalMatches}`);
                console.log(`Valid matches: ${result.validMatches}`);
                console.log(`Questionable matches: ${result.questionableMatches}`);
                console.log(`Broken links: ${result.brokenLinks}`);
                console.log(`Auto-cleared: ${result.autoCleared}`);

                // Show first few results
                if (result.validationResults && result.validationResults.length > 0) {
                    console.log('\nFirst 5 validation results:');
                    result.validationResults.slice(0, 5).forEach((record, index) => {
                        console.log(`${index + 1}. OSL ${record.osl_id}: ${record.status} (${record.string_similarity}% similarity)`);
                    });
                }

                process.exit(0);
            } else {
                console.error('Validation failed:', result.error);
                process.exit(1);
            }
        })
        .catch(error => {
            console.error('Unexpected error:', error);
            process.exit(1);
        });
}

export { runValidation, getValidationResults };
