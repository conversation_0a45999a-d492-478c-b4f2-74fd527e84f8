import XLSX from 'xlsx';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Path to the Excel file
const excelFilePath = path.join(__dirname, 'data', 'external data', 'discraftstock.xlsx');

console.log('Examining Discraft Excel file:', excelFilePath);

try {
    // Read the workbook
    const workbook = XLSX.readFile(excelFilePath);
    
    console.log('\nWorksheet names:');
    workbook.SheetNames.forEach((name, index) => {
        console.log(`${index + 1}. ${name}`);
    });
    
    // Examine each sheet
    for (const sheetName of workbook.SheetNames) {
        console.log(`\n=== Sheet: ${sheetName} ===`);
        const worksheet = workbook.Sheets[sheetName];
        
        // Get the range of the worksheet
        const range = XLSX.utils.decode_range(worksheet['!ref']);
        console.log(`Range: ${worksheet['!ref']} (${range.e.r + 1} rows, ${range.e.c + 1} columns)`);
        
        // Get headers (first row)
        console.log('\nColumn Headers:');
        const headers = [];
        for (let col = range.s.c; col <= range.e.c; col++) {
            const cellAddress = XLSX.utils.encode_cell({ r: 0, c: col });
            const cell = worksheet[cellAddress];
            const header = cell ? cell.v : `Column_${col}`;
            headers.push(header);
            console.log(`${col + 1}. ${header}`);
        }
        
        // Show first few rows of data
        console.log('\nFirst 5 data rows:');
        for (let row = 1; row <= Math.min(5, range.e.r); row++) {
            const rowData = {};
            for (let col = range.s.c; col <= range.e.c; col++) {
                const cellAddress = XLSX.utils.encode_cell({ r: row, c: col });
                const cell = worksheet[cellAddress];
                const header = headers[col - range.s.c];
                rowData[header] = cell ? cell.v : null;
            }
            console.log(`Row ${row}:`, rowData);
        }

        // Look for product data sections
        console.log('\n=== Looking for Product Data ===');

        // Check rows 20-100 for product information
        console.log('\nExamining rows 20-100 for product data:');
        for (let row = 20; row <= Math.min(100, range.e.r); row++) {
            const rowData = {};
            let hasData = false;

            for (let col = range.s.c; col <= range.e.c; col++) {
                const cellAddress = XLSX.utils.encode_cell({ r: row, c: col });
                const cell = worksheet[cellAddress];
                if (cell && cell.v !== null && cell.v !== undefined && cell.v !== '') {
                    const header = headers[col - range.s.c] || `Col_${col}`;
                    rowData[header] = cell.v;
                    hasData = true;
                }
            }

            if (hasData) {
                console.log(`Row ${row + 1}:`, rowData);

                // Stop after showing 10 rows with data
                if (Object.keys(rowData).length > 0) {
                    const dataCount = Object.keys(rowData).filter(key => rowData[key] !== null).length;
                    if (dataCount >= 3) { // Only show rows with substantial data
                        console.log(`  -> This row has ${dataCount} data fields`);
                    }
                }
            }
        }
        
        // Check for merged cells
        if (worksheet['!merges'] && worksheet['!merges'].length > 0) {
            console.log('\nMerged Cells:');
            worksheet['!merges'].forEach((merge, index) => {
                console.log(`Merge ${index + 1}: ${XLSX.utils.encode_range(merge)}`);
            });
        }
        
        // Check for empty rows at the beginning
        let emptyRowsAtStart = 0;
        for (let row = range.s.r; row <= range.e.r; row++) {
            let isEmpty = true;
            for (let col = range.s.c; col <= range.e.c; col++) {
                const cellAddress = XLSX.utils.encode_cell({ r: row, c: col });
                if (worksheet[cellAddress]) {
                    isEmpty = false;
                    break;
                }
            }
            if (isEmpty) {
                emptyRowsAtStart++;
            } else {
                break;
            }
        }
        if (emptyRowsAtStart > 0) {
            console.log(`\nEmpty rows at start: ${emptyRowsAtStart}`);
        }
        
        // Look for potential data sections
        console.log('\nPotential data sections:');
        let currentSection = null;
        let sectionStartRow = null;
        
        for (let row = range.s.r; row <= range.e.r; row++) {
            // Check if this row might be a section header
            const firstCellAddress = XLSX.utils.encode_cell({ r: row, c: range.s.c });
            const firstCell = worksheet[firstCellAddress];
            
            if (firstCell && typeof firstCell.v === 'string' && firstCell.v.trim() !== '') {
                // This might be a section header
                if (currentSection) {
                    console.log(`  Section "${currentSection}" from row ${sectionStartRow + 1} to ${row}`);
                }
                currentSection = firstCell.v;
                sectionStartRow = row;
            }
        }
        
        if (currentSection) {
            console.log(`  Section "${currentSection}" from row ${sectionStartRow + 1} to ${range.e.r + 1}`);
        }
    }
    
    // Additional analysis for complex structures
    console.log('\n=== Additional Structure Analysis ===');
    
    // Check if there are multiple tables in a single sheet
    for (const sheetName of workbook.SheetNames) {
        const worksheet = workbook.Sheets[sheetName];
        const range = XLSX.utils.decode_range(worksheet['!ref']);
        
        // Look for empty columns that might separate tables
        const emptyColumns = [];
        for (let col = range.s.c; col <= range.e.c; col++) {
            let isEmpty = true;
            for (let row = range.s.r; row <= range.e.r; row++) {
                const cellAddress = XLSX.utils.encode_cell({ r: row, c: col });
                if (worksheet[cellAddress]) {
                    isEmpty = false;
                    break;
                }
            }
            if (isEmpty) {
                emptyColumns.push(col);
            }
        }
        
        if (emptyColumns.length > 0) {
            console.log(`\nSheet "${sheetName}" may contain multiple tables separated by empty columns:`);
            console.log(`  Empty columns: ${emptyColumns.map(col => XLSX.utils.encode_col(col)).join(', ')}`);
        }
    }
    
} catch (error) {
    console.error('Error examining Excel file:', error);
}