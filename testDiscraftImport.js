import XLSX from 'xlsx';
import { createClient } from '@supabase/supabase-js';
import path from 'path';
import { fileURLToPath } from 'url';
import dotenv from 'dotenv';
import fs from 'fs';

// Load environment variables
dotenv.config();

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Initialize Supabase client
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_KEY;

if (!supabaseUrl || !supabaseKey) {
    console.error('❌ Missing Supabase environment variables');
    console.error('Please ensure SUPABASE_URL and SUPABASE_KEY are set in your .env file');
    process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

// Robust mapping utilities
function createProductKey(rawLine, rawModel, weightRange) {
    const line = rawLine || 'NO_LINE';
    const model = rawModel || 'NO_MODEL';
    return `${line}|${model}|${weightRange}`;
}

function calculateFileHash(filePath) {
    // Simple hash based on file stats and first few rows
    // In production, you might want a more sophisticated hash
    const stats = fs.statSync(filePath);
    return `${stats.size}_${stats.mtime.getTime()}`;
}

// Parsing rules based on your specifications
function standardizePlasticName(rawPlastic, rawModel) {
    // Handle McBeth/Pierce lines with Hard/Soft variants
    if (rawPlastic === 'McBeth') {
        if (rawModel && rawModel.startsWith('Hard ')) {
            return 'Putter Line Hard';
        } else if (rawModel && rawModel.startsWith('Soft ')) {
            return 'Putter Line Soft';
        } else if (rawModel && rawModel.startsWith('NEW - ')) {
            return 'ESP';
        }
        return 'Putter Line Hard'; // Default for McBeth
    }

    if (rawPlastic === 'Pierce') {
        if (rawModel && rawModel.startsWith('Hard ')) {
            return 'Putter Line Hard';
        } else if (rawModel && rawModel.startsWith('Soft ')) {
            return 'Putter Line Soft';
        } else if (rawModel && rawModel.startsWith('NEW - ')) {
            return 'ESP';
        }
        return 'Putter Line Soft'; // Default for Pierce
    }

    const plasticMap = {
        'Z': 'Elite Z',
        'Jawbreaker': 'Jawbreaker',
        'Fuzed': 'Elite Z FuZed Line with Saw Pattern',
        'PM Z Lite': 'Elite Z Lite',
        'PP Z Lite': 'Elite Z Lite',
        'McBeth FD': 'Elite Z Fly-Dyed',
        'Pierce FD': 'Elite Z Fly-Dyed',
        'Z Fly Dye': 'Elite Z Fly-Dyed',
        'Z Glo': 'Elite Z Glow',
        'ESP': 'ESP',
        'Recycle ESP': 'Recycle ESP',
        'X': 'Elite X',
        'Z Lite': 'Elite Z Lite',
        'PS Z': 'Elite Z',
        'RW Z': 'Elite Z',
        'PP Z': 'Elite Z'
        // Add more mappings as needed
    };

    return plasticMap[rawPlastic] || rawPlastic;
}

function parseMoldAndStamp(rawModel, rawPlastic) {
    // Handle special cases
    if (rawModel.includes('Discontinued - ')) {
        return {
            mold_name: rawModel.replace('Discontinued - ', '').trim(),
            stamp_name: 'Discontinued'
        };
    }

    if (rawModel.includes('Retired stamp- ')) {
        return {
            mold_name: rawModel.replace('Retired stamp- ', '').trim(),
            stamp_name: 'Retired'
        };
    }

    if (rawModel.includes('Retired stamp - ')) {
        return {
            mold_name: rawModel.replace('Retired stamp - ', '').trim(),
            stamp_name: 'Retired'
        };
    }

    // Handle Saw Pattern case
    if (rawModel.includes(' - Saw Pattern')) {
        return {
            mold_name: rawModel.replace(' - Saw Pattern', '').trim(),
            stamp_name: 'Stock'
        };
    }

    // Handle Fly Dye models
    if (rawModel.includes('Fly Dye Z ')) {
        const moldName = rawModel.replace('Fly Dye Z ', '').trim();
        if (rawPlastic === 'McBeth FD') {
            return {
                mold_name: moldName,
                stamp_name: moldName === 'Anax' ? 'Paul McBeth Large Signature' : 'PM Logo Stock Stamp'
            };
        } else if (rawPlastic === 'Pierce FD') {
            return {
                mold_name: moldName,
                stamp_name: 'PP Logo Stock Stamp'
            };
        }
        return {
            mold_name: moldName,
            stamp_name: 'Stock'
        };
    }

    // Handle NEW - models (ESP line)
    if (rawModel.includes('NEW - ')) {
        return {
            mold_name: rawModel.replace('NEW - ', '').trim(),
            stamp_name: rawPlastic === 'McBeth' ? 'PM Logo Stock Stamp' :
                       rawPlastic === 'Pierce' ? 'PP Logo Stock Stamp' : 'Stock'
        };
    }

    // Handle specific signature plastic lines
    if (rawPlastic === 'PM Z Lite') {
        return {
            mold_name: rawModel.trim(),
            stamp_name: 'PM Logo Stock Stamp'
        };
    }

    if (rawPlastic === 'PP Z Lite') {
        return {
            mold_name: rawModel.trim(),
            stamp_name: 'PP Logo Stock Stamp'
        };
    }

    if (rawPlastic === 'McBeth') {
        // Remove "Hard" or "Soft" from model name for McBeth line
        let moldName = rawModel.trim();
        if (moldName.startsWith('Hard ')) {
            moldName = moldName.replace('Hard ', '');
        } else if (moldName.startsWith('Soft ')) {
            moldName = moldName.replace('Soft ', '');
        }
        return {
            mold_name: moldName,
            stamp_name: 'PM Logo Stock Stamp'
        };
    }

    if (rawPlastic === 'Pierce') {
        // Remove "Hard" or "Soft" from model name for Pierce line
        let moldName = rawModel.trim();
        if (moldName.startsWith('Hard ')) {
            moldName = moldName.replace('Hard ', '');
        } else if (moldName.startsWith('Soft ')) {
            moldName = moldName.replace('Soft ', '');
        }
        return {
            mold_name: moldName,
            stamp_name: 'PP Logo Stock Stamp'
        };
    }

    // Handle special signature models
    if (rawModel === 'PS Buzzz SS') {
        return {
            mold_name: 'Buzzz SS',
            stamp_name: 'Paige Shue - 2018 World Champion'
        };
    }

    if (rawModel === 'RW Nuke') {
        return {
            mold_name: 'Nuke',
            stamp_name: 'Ricky Wysocki - 2x World Champion Signature'
        };
    }

    if (rawModel === 'Raptor' && rawPlastic === 'PP Z') {
        return {
            mold_name: 'Raptor',
            stamp_name: 'PP 29190 5X Paige Pierce World Champion'
        };
    }

    if (rawModel === 'PP Sol') {
        return {
            mold_name: 'Sol',
            stamp_name: 'PP 29190 5X Paige Pierce World Champion'
        };
    }

    // Handle Paul McBeth signature discs (in model name)
    if (rawModel.includes('Paul McBeth ')) {
        return {
            mold_name: rawModel.replace('Paul McBeth ', '').trim(),
            stamp_name: 'PM Logo Stock Stamp'
        };
    }

    // Handle Paige Pierce signature discs (in model name)
    if (rawModel.includes('Paige Pierce ')) {
        return {
            mold_name: rawModel.replace('Paige Pierce ', '').trim(),
            stamp_name: 'PM Logo Stock Stamp'
        };
    }

    // Default case - just the mold name with Stock stamp
    return {
        mold_name: rawModel.trim(),
        stamp_name: 'Stock'
    };
}

function getWeightRange(columnIndex) {
    // Based on the weight headers at row 131
    const weightMappings = {
        12: { min: 150, max: 150, name: '150g', letter: 'L' },
        13: { min: 160, max: 166, name: '160-166g', letter: 'M' },
        14: { min: 167, max: 169, name: '167-169g', letter: 'N' },
        15: { min: 170, max: 172, name: '170-172g', letter: 'O' },
        16: { min: 173, max: 174, name: '173-174g', letter: 'P' },
        17: { min: 175, max: 176, name: '175-176g', letter: 'Q' },
        18: { min: 177, max: 180, name: '177+', letter: 'R' }
    };

    return weightMappings[columnIndex] || null;
}

function parseSpecialSections(rowData, row) {
    const col1 = rowData.col_1?.toString().trim() || '';
    const col4 = rowData.col_4?.toString().trim() || '';

    // Skip summer cleaning/clearance sections
    if (col1.includes('Summer cleaning') ||
        col1.includes('SALE ITEMS') ||
        col1.includes('Random Golf discs') ||
        col4.includes('SALE ITEMS')) {
        console.log(`Skipping clearance section at row ${row + 1}: ${col1 || col4}`);
        return { skip: true };
    }

    // Handle sections with no separate line column
    if (!col1 && col4) {
        // Model column contains both line and model info
        console.log(`Special section (no line column) at row ${row + 1}: ${col4}`);

        // Parse combined line/model for special cases
        if (col4.includes('Paul McBeth Proto')) {
            return {
                skip: false,
                rawLine: 'Paul McBeth Proto',
                rawModel: col4,
                isSpecialSection: true
            };
        }

        // For other special sections, use the full model as both line and model
        return {
            skip: false,
            rawLine: 'SPECIAL',
            rawModel: col4,
            isSpecialSection: true
        };
    }

    return {
        skip: false,
        rawLine: col1,
        rawModel: col4,
        isSpecialSection: false
    };
}

async function testImportRows131to281() {
    try {
        console.log('🚀 Starting test import for rows 131-281 (skipping 143-147)...');
        
        const filePath = path.join(__dirname, 'data', 'external data', 'discraftstock.xlsx');
        console.log(`📁 Reading Excel file: ${filePath}`);

        // Calculate file hash for change detection
        const fileHash = calculateFileHash(filePath);
        console.log(`📊 File hash: ${fileHash}`);

        // Read the Excel file
        const workbook = XLSX.readFile(filePath);
        const sheetName = 'Order Form';
        const worksheet = workbook.Sheets[sheetName];
        
        if (!worksheet) {
            throw new Error(`Sheet "${sheetName}" not found`);
        }
        
        const range = XLSX.utils.decode_range(worksheet['!ref']);
        console.log(`Sheet range: ${XLSX.utils.encode_range(range)}`);
        
        const importData = [];
        const batchId = generateUUID();
        
        // Process rows 131-281, skipping 143-147 (Excel rows 132-282, since we're 0-based)
        for (let row = 130; row <= 280; row++) { // 0-based, so 130 = Excel row 131
            // Skip rows 143-147 (Excel rows 144-148)
            if (row >= 142 && row <= 146) {
                console.log(`\n--- Skipping row ${row + 1} (Excel row ${row + 1}) as requested ---`);
                continue;
            }

            console.log(`\n--- Processing row ${row + 1} (Excel row ${row + 1}) ---`);
            
            const rowData = {};
            
            // Read all columns for this row
            for (let col = 0; col <= range.e.c; col++) {
                const cellAddress = XLSX.utils.encode_cell({ r: row, c: col });
                const cell = worksheet[cellAddress];
                if (cell && cell.v !== null && cell.v !== undefined && cell.v !== '') {
                    rowData[`col_${col}`] = cell.v;
                }
            }
            
            console.log('Raw row data:', rowData);

            // Parse special sections and handle skips
            const sectionInfo = parseSpecialSections(rowData, row);
            if (sectionInfo.skip) {
                continue;
            }

            const rawPlastic = sectionInfo.rawLine;
            const rawModel = sectionInfo.rawModel;

            // Skip if no meaningful content
            if (!rawPlastic && !rawModel) {
                console.log('Skipping row - no meaningful content');
                continue;
            }

            // Skip header rows
            if (rawPlastic === 'Line' || rawModel === 'Model') {
                console.log('Skipping header row');
                continue;
            }
            
            const plasticName = standardizePlasticName(rawPlastic, rawModel);
            const { mold_name, stamp_name } = parseMoldAndStamp(rawModel, rawPlastic);
            
            console.log(`Parsed: ${plasticName} ${mold_name} ${stamp_name ? '(' + stamp_name + ')' : ''}`);
            
            // Check each weight column for orderable items
            for (let col = 12; col <= 18; col++) {
                const weightData = getWeightRange(col);
                if (!weightData) continue;
                
                const status = rowData[`col_${col}`];
                if (status === 9 || status === '9' || (status && status.toString().toLowerCase() === 'out')) {
                    console.log(`  Found orderable item: ${weightData.name} - Status: ${status}`);
                    
                    // Determine color and stamp for special cases
                    let colorName = 'Varies'; // Default color
                    let finalStampName = stamp_name;

                    // Handle ESP White special case
                    const vendorDesc = rowData.col_19 || '';
                    if (vendorDesc.includes('White/Blank Top/Bottom stamp')) {
                        colorName = 'White';
                        finalStampName = 'Dye Line Blank Top Bottom';
                    }

                    // Create robust mapping key
                    const mappingKey = createProductKey(rawPlastic, rawModel, weightData.name);

                    const productRecord = {
                        mold_name: mold_name,
                        plastic_name: plasticName,
                        min_weight: weightData.min,
                        max_weight: weightData.max,
                        color_name: colorName,
                        stamp_name: finalStampName,
                        vendor_product_code: `${plasticName}_${mold_name}_${weightData.min}-${weightData.max}`.replace(/\s+/g, '_'),
                        is_orderable: true,
                        is_currently_available: status === 9 || status === '9',
                        cost_price: null, // Will extract from description if available

                        // ROBUST MAPPING - Content-based key
                        excel_mapping_key: mappingKey,
                        excel_row_hint: row + 1, // Hint for speed, but not relied upon
                        excel_column: weightData.letter,

                        // Raw content for validation and rebuilding
                        raw_line_type: rawPlastic,
                        raw_model: rawModel,
                        raw_weight_range: weightData.name,

                        // File tracking
                        import_file_hash: fileHash,
                        import_batch_id: batchId,
                        vendor_description: rowData.col_19 || ''
                    };
                    
                    // Extract pricing if available
                    const description = productRecord.vendor_description || '';
                    const costMatch = description.match(/cost\s*\$?(\d+\.?\d*)/i);
                    if (costMatch) {
                        productRecord.cost_price = parseFloat(costMatch[1]);
                    }
                    
                    importData.push(productRecord);
                }
            }
        }
        
        console.log(`\n📊 Parsed ${importData.length} orderable products`);
        
        if (importData.length === 0) {
            console.log('No orderable products found in the specified range');
            return;
        }
        
        // Show sample of parsed data
        console.log('\n📋 Sample parsed records:');
        importData.slice(0, 5).forEach((record, index) => {
            console.log(`${index + 1}. ${record.plastic_name} ${record.mold_name} ${record.min_weight}-${record.max_weight}g ${record.stamp_name ? '(' + record.stamp_name + ')' : ''} - ${record.is_currently_available ? 'Available' : 'Out of Stock'}`);
        });
        
        // Import to database
        console.log('\n💾 Importing to database...');
        await importToDatabase(importData, batchId);
        
    } catch (error) {
        console.error('❌ Error in test import:', error);
        throw error;
    }
}

async function importToDatabase(records, batchId) {
    try {
        // Clear existing test data
        console.log('🧹 Clearing existing data...');
        const { error: deleteError } = await supabase
            .from('it_discraft_order_sheet_lines')
            .delete()
            .neq('id', 0); // Delete all records
        
        if (deleteError) {
            console.warn('Warning: Could not clear existing data:', deleteError.message);
        }
        
        // Insert new data
        console.log(`📥 Inserting ${records.length} records...`);
        
        const { data, error } = await supabase
            .from('it_discraft_order_sheet_lines')
            .insert(records)
            .select('id');
        
        if (error) {
            console.error('❌ Error inserting data:', error);
            throw error;
        }
        
        console.log(`✅ Successfully imported ${records.length} records`);
        console.log(`📦 Import batch ID: ${batchId}`);
        
        // Show summary
        await showImportSummary(batchId);
        
    } catch (error) {
        console.error('❌ Error importing to database:', error);
        throw error;
    }
}

async function showImportSummary(batchId) {
    try {
        const { data: summary, error } = await supabase
            .from('it_discraft_order_sheet_lines')
            .select('plastic_name, mold_name, stamp_name, is_currently_available')
            .eq('import_batch_id', batchId);
        
        if (error) {
            console.warn('Could not generate summary:', error.message);
            return;
        }
        
        console.log('\n📊 Import Summary:');
        
        const byPlastic = {};
        const byMold = {};
        let availableCount = 0;
        let outOfStockCount = 0;
        
        summary.forEach(record => {
            byPlastic[record.plastic_name] = (byPlastic[record.plastic_name] || 0) + 1;
            byMold[record.mold_name] = (byMold[record.mold_name] || 0) + 1;
            
            if (record.is_currently_available) {
                availableCount++;
            } else {
                outOfStockCount++;
            }
        });
        
        console.log('\nBy Plastic Type:');
        Object.entries(byPlastic).forEach(([plastic, count]) => {
            console.log(`  ${plastic}: ${count} products`);
        });
        
        console.log('\nBy Mold:');
        Object.entries(byMold).forEach(([mold, count]) => {
            console.log(`  ${mold}: ${count} products`);
        });
        
        console.log('\nBy Availability:');
        console.log(`  Available now: ${availableCount} products`);
        console.log(`  Out of stock: ${outOfStockCount} products`);
        
    } catch (error) {
        console.warn('Error generating summary:', error.message);
    }
}

function generateUUID() {
    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
        const r = Math.random() * 16 | 0;
        const v = c == 'x' ? r : (r & 0x3 | 0x8);
        return v.toString(16);
    });
}

// Run the test import
testImportRows131to281()
    .then(() => {
        console.log('\n🎉 Test import completed successfully!');
        process.exit(0);
    })
    .catch((error) => {
        console.error('\n❌ Test import failed:', error);
        process.exit(1);
    });
