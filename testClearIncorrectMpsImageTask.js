// testClearIncorrectMpsImageTask.js - Test script for clear_incorrect_mps_image task
import { createClient } from '@supabase/supabase-js';
import { enqueueClearIncorrectMpsImageTask } from './enqueueClearIncorrectMpsImageTask.js';
import dotenv from 'dotenv';
dotenv.config();

// Initialize Supabase client
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY;
const supabase = createClient(supabaseUrl, supabaseKey);

async function testClearIncorrectMpsImageTask() {
  console.log('=== Testing Clear Incorrect MPS Image Task ===\n');
  
  // Get command line arguments
  const mpsId = process.argv[2];
  
  if (!mpsId) {
    console.error('Usage: node testClearIncorrectMpsImageTask.js <id>');
    console.error('Example: node testClearIncorrectMpsImageTask.js 12345');
    process.exit(1);
  }
  
  try {
    // Step 1: Check if there's currently a t_images record for this MPS
    console.log(`Step 1: Checking for existing t_images record for MPS ID ${mpsId}...`);
    const { data: existingRecord, error: checkError } = await supabase
      .from('t_images')
      .select('*')
      .eq('table_name', 't_mps')
      .eq('record_id', parseInt(mpsId))
      .maybeSingle();
    
    if (checkError) {
      console.error(`Error checking for existing record: ${checkError.message}`);
      return;
    }
    
    if (existingRecord) {
      console.log(`✓ Found existing t_images record:`, existingRecord);
    } else {
      console.log(`ℹ No existing t_images record found for MPS ID ${mpsId}`);
      console.log(`  This is fine - the task will handle this gracefully.`);
    }
    
    // Step 2: Enqueue the task
    console.log(`\nStep 2: Enqueueing clear_incorrect_mps_image task for MPS ID ${mpsId}...`);
    const task = await enqueueClearIncorrectMpsImageTask(
      supabase, 
      mpsId, 
      'Test deletion via test script'
    );
    
    console.log(`✓ Task enqueued successfully with ID: ${task.id}`);
    console.log(`  Task type: ${task.task_type}`);
    console.log(`  Status: ${task.status}`);
    console.log(`  Scheduled at: ${task.scheduled_at}`);
    console.log(`  Payload:`, task.payload);
    
    // Step 3: Monitor the task status
    console.log(`\nStep 3: Monitoring task status...`);
    console.log(`You can monitor the task progress by checking the t_task_queue table:`);
    console.log(`  SELECT * FROM t_task_queue WHERE id = ${task.id};`);
    console.log(`\nOr run the task queue worker to process it:`);
    console.log(`  node taskQueueWorker.js`);
    
    // Step 4: Wait a moment and check if the task has been processed
    console.log(`\nStep 4: Checking task status after a brief delay...`);
    
    // Wait 2 seconds
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    const { data: taskStatus, error: statusError } = await supabase
      .from('t_task_queue')
      .select('*')
      .eq('id', task.id)
      .single();
    
    if (statusError) {
      console.error(`Error checking task status: ${statusError.message}`);
      return;
    }
    
    console.log(`Current task status: ${taskStatus.status}`);
    if (taskStatus.result) {
      console.log(`Task result:`, taskStatus.result);
    }
    
    if (taskStatus.status === 'pending') {
      console.log(`\n⏳ Task is still pending. Run the worker to process it:`);
      console.log(`   node taskQueueWorker.js`);
    } else if (taskStatus.status === 'completed') {
      console.log(`\n✅ Task completed successfully!`);
      
      // Verify the t_images record was deleted
      const { data: verifyRecord, error: verifyError } = await supabase
        .from('t_images')
        .select('*')
        .eq('table_name', 't_mps')
        .eq('record_id', parseInt(mpsId))
        .maybeSingle();
      
      if (verifyError) {
        console.error(`Error verifying deletion: ${verifyError.message}`);
      } else if (verifyRecord) {
        console.log(`⚠️  Warning: t_images record still exists after task completion`);
      } else {
        console.log(`✓ Confirmed: t_images record has been deleted`);
      }
    } else if (taskStatus.status === 'error') {
      console.log(`\n❌ Task failed with error:`, taskStatus.result);
    }
    
  } catch (error) {
    console.error(`Test failed: ${error.message}`);
    console.error(error);
  }
}

// Run the test
testClearIncorrectMpsImageTask();
