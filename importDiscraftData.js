import XLSX from 'xlsx';
import { createClient } from '@supabase/supabase-js';
import path from 'path';
import { fileURLToPath } from 'url';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Initialize Supabase client
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_KEY;

if (!supabaseUrl || !supabaseKey) {
    console.error('❌ Missing Supabase environment variables');
    console.error('Please ensure SUPABASE_URL and SUPABASE_KEY are set in your .env file');
    process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

async function importDiscraftData() {
    try {
        console.log('🚀 Starting Discraft data import...');
        console.log('Current directory:', __dirname);

        const filePath = path.join(__dirname, 'data', 'external data', 'discraftstock.xlsx');
        console.log(`📁 Reading Discraft Excel file: ${filePath}`);
        
        // Read the Excel file
        const workbook = XLSX.readFile(filePath);
        const sheetName = 'Order Form';
        const worksheet = workbook.Sheets[sheetName];
        
        if (!worksheet) {
            throw new Error(`Sheet "${sheetName}" not found in the Excel file`);
        }
        
        // Get the range of the worksheet
        const range = XLSX.utils.decode_range(worksheet['!ref']);
        console.log(`Sheet range: ${XLSX.utils.encode_range(range)} (${range.e.r + 1} rows, ${range.e.c + 1} columns)`);
        
        // Parse the data
        const importData = [];
        let currentSection = '';
        let batchId = generateUUID();
        
        // Start from row 23 where product headers begin (based on our analysis)
        for (let row = 23; row <= range.e.r; row++) {
            const rowData = {};

            // Read all columns for this row
            for (let col = range.s.c; col <= range.e.c; col++) {
                const cellAddress = XLSX.utils.encode_cell({ r: row, c: col });
                const cell = worksheet[cellAddress];
                if (cell && cell.v !== null && cell.v !== undefined && cell.v !== '') {
                    rowData[`col_${col}`] = cell.v;
                }
            }

            // Skip empty rows
            if (Object.keys(rowData).length === 0) continue;
            
            // Check if this is a header row (skip it)
            if (rowData.col_1 === 'Line' && rowData.col_4 === 'Model') {
                continue;
            }

            // Check if this is a section header or informational text
            if (rowData.col_1 && typeof rowData.col_1 === 'string') {
                const lineValue = rowData.col_1.toString().trim();

                // Skip informational rows
                if (lineValue.includes('news') || lineValue.includes('forces') ||
                    lineValue.includes('distributing') || lineValue.includes('blend') ||
                    lineValue.includes('Golf Discs') || lineValue.includes('Recently added') ||
                    lineValue.includes('Transparent') || lineValue.includes('premium') ||
                    lineValue.includes('Baseline') || lineValue.includes('rubber') ||
                    lineValue.includes('ultra-premuim') || lineValue.includes('contrasting')) {
                    continue;
                }

                // Detect section headers (but don't skip them, use them for context)
                if (lineValue.includes('Tour') || lineValue.includes('DGA') ||
                    lineValue.includes('Spark') || lineValue.includes('ProLine') ||
                    lineValue.includes('D-Line') || lineValue.includes('Stone') ||
                    lineValue.includes('Atmos') || lineValue.includes('Signature')) {
                    currentSection = lineValue;
                    continue;
                }
            }
            
            // Parse product data rows - look for rows with line type and model
            if (rowData.col_1 && rowData.col_4) { // Line type and Model columns
                const lineType = rowData.col_1.toString().trim();
                const model = rowData.col_4.toString().trim();

                // Skip if this looks like a header or section
                if (lineType === 'Line' || model === 'Model') continue;

                // Get weight availability data
                const weight150 = rowData.col_12 ? rowData.col_12.toString().trim() : null;
                const weight160_166 = rowData.col_13 ? rowData.col_13.toString().trim() : null;
                const weight167_169 = rowData.col_14 ? rowData.col_14.toString().trim() : null;
                const weight170_172 = rowData.col_15 ? rowData.col_15.toString().trim() : null;
                const weight173_174 = rowData.col_16 ? rowData.col_16.toString().trim() : null;
                const weight175_176 = rowData.col_17 ? rowData.col_17.toString().trim() : null;
                const weight177_plus = rowData.col_18 ? rowData.col_18.toString().trim() : null;

                // Check if this product is orderable (has '9' or 'out' in any weight column)
                const weightValues = [weight150, weight160_166, weight167_169, weight170_172, weight173_174, weight175_176, weight177_plus];
                const isOrderable = weightValues.some(w => w === '9' || (w && w.toLowerCase() === 'out'));

                // Only process orderable items
                if (!isOrderable) continue;

                const productRecord = {
                    line_type: lineType,
                    model: model,
                    description: rowData.col_19 || rowData.col_12 || '', // Description column
                    recently_added: rowData.col_10 === '√',
                    is_orderable: true,

                    // Weight status columns
                    weight_150_status: weight150,
                    weight_160_166_status: weight160_166,
                    weight_167_169_status: weight167_169,
                    weight_170_172_status: weight170_172,
                    weight_173_174_status: weight173_174,
                    weight_175_176_status: weight175_176,
                    weight_177_plus_status: weight177_plus,

                    // Availability flags
                    weight_150_available: weight150 === '9',
                    weight_160_166_available: weight160_166 === '9',
                    weight_167_169_available: weight167_169 === '9',
                    weight_170_172_available: weight170_172 === '9',
                    weight_173_174_available: weight173_174 === '9',
                    weight_175_176_available: weight175_176 === '9',
                    weight_177_plus_available: weight177_plus === '9',

                    notes: rowData.col_19 || '', // Notes/description
                    section_header: currentSection,
                    row_number: row + 1, // Excel row number (1-based)
                    import_batch_id: batchId
                };

                // Extract pricing if available from description
                const description = productRecord.description || '';
                const costMatch = description.match(/cost\s*\$?(\d+\.?\d*)/i);
                const msrpMatch = description.match(/msrp\s*\$?(\d+\.?\d*)/i);

                if (costMatch) {
                    productRecord.cost_price = parseFloat(costMatch[1]);
                }
                if (msrpMatch) {
                    productRecord.retail_price = parseFloat(msrpMatch[1]);
                }

                importData.push(productRecord);
            }
        }
        
        console.log(`\nParsed ${importData.length} product records`);
        
        // Show sample of parsed data
        console.log('\nSample parsed records:');
        importData.slice(0, 5).forEach((record, index) => {
            const availableWeights = [];
            if (record.weight_150_available) availableWeights.push('150g');
            if (record.weight_160_166_available) availableWeights.push('160-166g');
            if (record.weight_167_169_available) availableWeights.push('167-169g');
            if (record.weight_170_172_available) availableWeights.push('170-172g');
            if (record.weight_173_174_available) availableWeights.push('173-174g');
            if (record.weight_175_176_available) availableWeights.push('175-176g');
            if (record.weight_177_plus_available) availableWeights.push('177g+');

            console.log(`${index + 1}. ${record.line_type} - ${record.model} (Available: ${availableWeights.join(', ') || 'None currently, but orderable'})`);
        });
        
        if (importData.length === 0) {
            console.log('No product data found to import');
            return;
        }
        
        // Import to database
        console.log('\nImporting data to database...');
        await importToDatabase(importData, batchId);
        
    } catch (error) {
        console.error('Error importing Discraft data:', error);
        throw error;
    }
}

async function importToDatabase(records, batchId) {
    try {
        // Clear existing data (optional - comment out if you want to keep historical data)
        console.log('Clearing existing Discraft data...');
        const { error: deleteError } = await supabase
            .from('it_discraft_order_sheet_lines')
            .delete()
            .neq('id', 0); // Delete all records
        
        if (deleteError) {
            console.warn('Warning: Could not clear existing data:', deleteError.message);
        }
        
        // Insert new data in batches
        const batchSize = 100;
        let insertedCount = 0;
        
        for (let i = 0; i < records.length; i += batchSize) {
            const batch = records.slice(i, i + batchSize);
            
            console.log(`Inserting batch ${Math.floor(i / batchSize) + 1}/${Math.ceil(records.length / batchSize)} (${batch.length} records)...`);
            
            const { data, error } = await supabase
                .from('it_discraft_order_sheet_lines')
                .insert(batch)
                .select('id');
            
            if (error) {
                console.error(`Error inserting batch starting at index ${i}:`, error);
                throw error;
            }
            
            insertedCount += batch.length;
            console.log(`Successfully inserted ${insertedCount}/${records.length} records`);
        }
        
        console.log(`\n✅ Successfully imported ${insertedCount} Discraft product records`);
        console.log(`📦 Import batch ID: ${batchId}`);
        
        // Show summary statistics
        await showImportSummary(batchId);
        
    } catch (error) {
        console.error('Error importing to database:', error);
        throw error;
    }
}

async function showImportSummary(batchId) {
    try {
        // Get summary statistics
        const { data: summary, error } = await supabase
            .from('it_discraft_order_sheet_lines')
            .select('line_type, weight_150_available, weight_160_166_available, weight_167_169_available, weight_170_172_available, weight_173_174_available, weight_175_176_available, weight_177_plus_available')
            .eq('import_batch_id', batchId);

        if (error) {
            console.warn('Could not generate summary:', error.message);
            return;
        }

        console.log('\n📊 Import Summary:');

        // Group by line type
        const byLineType = {};
        let totalAvailableNow = 0;
        let totalOrderableButOut = 0;

        summary.forEach(record => {
            byLineType[record.line_type] = (byLineType[record.line_type] || 0) + 1;

            // Check if any weights are currently available (marked with '9')
            const hasAvailableWeights = record.weight_150_available ||
                                      record.weight_160_166_available ||
                                      record.weight_167_169_available ||
                                      record.weight_170_172_available ||
                                      record.weight_173_174_available ||
                                      record.weight_175_176_available ||
                                      record.weight_177_plus_available;

            if (hasAvailableWeights) {
                totalAvailableNow++;
            } else {
                totalOrderableButOut++;
            }
        });

        console.log('\nBy Product Line:');
        Object.entries(byLineType).forEach(([lineType, count]) => {
            console.log(`  ${lineType}: ${count} products`);
        });

        console.log('\nBy Availability:');
        console.log(`  Available now (marked with '9'): ${totalAvailableNow} products`);
        console.log(`  Orderable but currently out: ${totalOrderableButOut} products`);
        
    } catch (error) {
        console.warn('Error generating summary:', error.message);
    }
}

function generateUUID() {
    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
        const r = Math.random() * 16 | 0;
        const v = c == 'x' ? r : (r & 0x3 | 0x8);
        return v.toString(16);
    });
}

// Run the import if this file is executed directly
const isMainModule = process.argv[1] && fileURLToPath(import.meta.url) === process.argv[1];

if (isMainModule) {
    console.log('✅ Running Discraft import directly...');
    importDiscraftData()
        .then(() => {
            console.log('\n🎉 Discraft data import completed successfully!');
            process.exit(0);
        })
        .catch((error) => {
            console.error('\n❌ Discraft data import failed:', error);
            process.exit(1);
        });
}

export { importDiscraftData };
