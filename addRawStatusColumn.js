import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

dotenv.config();

const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_KEY;
const supabase = createClient(supabaseUrl, supabaseKey);

async function addRawStatusColumn() {
    try {
        console.log('🔄 Adding raw_status column for resilient import...');
        
        const alterQuery = 'ALTER TABLE public.it_discraft_order_sheet_lines ADD COLUMN IF NOT EXISTS raw_status TEXT';
        
        const { error } = await supabase.rpc('exec_sql', { sql_query: alterQuery });
        
        if (error && !error.message.includes('already exists')) {
            console.error('❌ Error adding column:', error);
        } else {
            console.log('✅ Successfully added raw_status column');
        }
        
    } catch (error) {
        console.error('❌ Error:', error);
    }
}

addRawStatusColumn();
