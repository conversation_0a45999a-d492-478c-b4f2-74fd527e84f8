import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

dotenv.config();

const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_KEY;
const supabase = createClient(supabaseUrl, supabaseKey);

async function testChunking() {
    try {
        console.log('🧪 Testing chunked data retrieval...\n');
        
        // Test 1: Get total count
        console.log('1. Getting total product count...');
        const { count: totalProducts, error: totalError } = await supabase
            .from('it_discraft_order_sheet_lines')
            .select('*', { count: 'exact', head: true });
        
        if (totalError) {
            console.error('Error getting total count:', totalError);
            return;
        }
        
        console.log(`✅ Total products: ${totalProducts}`);
        
        // Test 2: Get all products in chunks
        console.log('\n2. Getting all products in chunks...');
        let allProducts = [];
        let offset = 0;
        const chunkSize = 1000;
        
        while (true) {
            const { data: productChunk, error: chunkError } = await supabase
                .from('it_discraft_order_sheet_lines')
                .select('id, plastic_name, mold_name, stamp_name')
                .range(offset, offset + chunkSize - 1);
            
            if (chunkError) {
                console.error('Error getting chunk:', chunkError);
                break;
            }
            
            if (!productChunk || productChunk.length === 0) break;
            
            allProducts = allProducts.concat(productChunk);
            console.log(`   Retrieved chunk: ${productChunk.length} products (total: ${allProducts.length})`);
            
            if (productChunk.length < chunkSize) break;
            offset += chunkSize;
        }
        
        console.log(`✅ Retrieved all ${allProducts.length} products via chunking`);
        
        // Test 3: Count matched vs unmatched
        console.log('\n3. Counting matched vs unmatched...');
        const { count: matchedCount, error: matchedError } = await supabase
            .from('it_discraft_order_sheet_lines')
            .select('*', { count: 'exact', head: true })
            .not('calculated_mps_id', 'is', null);
        
        if (matchedError) {
            console.error('Error getting matched count:', matchedError);
            return;
        }
        
        const unmatchedCount = totalProducts - matchedCount;
        const successRate = ((matchedCount / totalProducts) * 100).toFixed(2);
        
        console.log(`✅ Matched: ${matchedCount}`);
        console.log(`✅ Unmatched: ${unmatchedCount}`);
        console.log(`✅ Success rate: ${successRate}%`);
        
        // Verify chunking got all records
        if (allProducts.length === totalProducts) {
            console.log('\n🎉 Chunking works perfectly - got all records!');
        } else {
            console.log(`\n❌ Chunking issue - got ${allProducts.length} but expected ${totalProducts}`);
        }
        
    } catch (error) {
        console.error('❌ Test error:', error);
    }
}

testChunking();
