// Import Amazon Fulfilled Inventory Report
// This is a truncate and replace import (snapshot data)

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

export async function importAmazonFulfilledInventory(supabase) {
  console.log('[importAmazonFulfilledInventory] Starting Amazon Fulfilled Inventory import');

  try {
    // Define the file path
    const filePath = path.join(__dirname, 'data', 'external data', 'Amazon Fulfilled Inventory Report.txt');
    
    // Check if file exists
    if (!fs.existsSync(filePath)) {
      throw new Error(`File not found: ${filePath}`);
    }

    console.log(`[importAmazonFulfilledInventory] Reading file: ${filePath}`);

    // Read and parse the file
    const fileContent = fs.readFileSync(filePath, 'utf-8');
    const lines = fileContent.split('\n').filter(line => line.trim() !== '');

    if (lines.length === 0) {
      throw new Error('File is empty or contains no valid data');
    }

    // Parse header line (tab-separated)
    const headers = lines[0].split('\t').map(h => h.trim());
    console.log(`[importAmazonFulfilledInventory] Found ${headers.length} columns: ${headers.join(', ')}`);

    // Expected headers based on the file structure
    const expectedHeaders = [
      'seller-sku',
      'fulfillment-channel-sku', 
      'asin',
      'condition-type',
      'Warehouse-Condition-code',
      'Quantity Available'
    ];

    // Validate headers
    const missingHeaders = expectedHeaders.filter(expected => 
      !headers.some(header => header.toLowerCase() === expected.toLowerCase())
    );

    if (missingHeaders.length > 0) {
      console.warn(`[importAmazonFulfilledInventory] Missing expected headers: ${missingHeaders.join(', ')}`);
    }

    // Parse data rows
    const dataRows = [];
    let skippedRows = 0;

    for (let i = 1; i < lines.length; i++) {
      const line = lines[i].trim();
      if (!line) continue;

      const values = line.split('\t');
      
      // Ensure we have enough values (pad with empty strings if needed)
      while (values.length < headers.length) {
        values.push('');
      }

      try {
        // Map the data to our database columns
        const row = {
          seller_sku: values[0] || null,
          fulfillment_channel_sku: values[1] || null,
          asin: values[2] || null,
          condition_type: values[3] || null,
          warehouse_condition_code: values[4] || null,
          quantity_available: parseInt(values[5]) || 0
        };

        // Basic validation - require seller_sku
        if (!row.seller_sku) {
          skippedRows++;
          console.warn(`[importAmazonFulfilledInventory] Skipping row ${i + 1}: missing seller_sku`);
          continue;
        }

        dataRows.push(row);
      } catch (error) {
        skippedRows++;
        console.warn(`[importAmazonFulfilledInventory] Error parsing row ${i + 1}: ${error.message}`);
      }
    }

    console.log(`[importAmazonFulfilledInventory] Parsed ${dataRows.length} valid rows, skipped ${skippedRows} rows`);

    if (dataRows.length === 0) {
      throw new Error('No valid data rows found to import');
    }

    // Truncate existing data (this is a snapshot import)
    console.log('[importAmazonFulfilledInventory] Truncating existing data...');
    const { error: truncateError } = await supabase
      .from('it_amaz_fulfilled_inventory_report')
      .delete()
      .neq('id', 0); // Delete all records

    if (truncateError) {
      throw new Error(`Failed to truncate existing data: ${truncateError.message}`);
    }

    // Import data in chunks of 1000 records
    const chunkSize = 1000;
    let totalImported = 0;

    for (let i = 0; i < dataRows.length; i += chunkSize) {
      const chunk = dataRows.slice(i, i + chunkSize);
      
      console.log(`[importAmazonFulfilledInventory] Importing chunk ${Math.floor(i / chunkSize) + 1} (${chunk.length} records)`);

      const { data, error } = await supabase
        .from('it_amaz_fulfilled_inventory_report')
        .insert(chunk);

      if (error) {
        throw new Error(`Failed to import chunk starting at row ${i + 1}: ${error.message}`);
      }

      totalImported += chunk.length;
    }

    const message = `Successfully imported ${totalImported} records into it_amaz_fulfilled_inventory_report (truncate and replace)`;
    const details = `File: Amazon Fulfilled Inventory Report.txt\nTotal records: ${totalImported}\nSkipped rows: ${skippedRows}\nImport type: Truncate and replace`;

    console.log(`[importAmazonFulfilledInventory] ${message}`);

    return {
      success: true,
      message: message,
      details: details,
      importCount: totalImported,
      skippedCount: skippedRows
    };

  } catch (error) {
    console.error(`[importAmazonFulfilledInventory] Error: ${error.message}`);
    throw error;
  }
}
