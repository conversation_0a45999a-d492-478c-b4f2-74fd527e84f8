// Debug the Bottom Stamped parsing
const testDescription = 'Star Bottom Stamped Mako 3 Mid-Range (White)';

console.log('🔍 Debugging Bottom Stamped parsing...');
console.log(`Input: "${testDescription}"`);

// Test different regex patterns
const patterns = [
    /^(.+?)\s+Bottom\s+Stamped\s+(.+?)\s+(.*?)\s*\((\w+)\)$/i,
    /(.+?)\s+Bottom\s+Stamped\s+(.+?)\s+.*?\s*\((\w+)\)$/i,
    /(.+?)\s+Bottom\s+Stamped\s+(.+?)\s+\w+.*?\s*\((\w+)\)$/i,
    /(.+?)\s+Bottom\s+Stamped\s+(.+?)\s+Mid-Range\s*\((\w+)\)$/i
];

patterns.forEach((pattern, index) => {
    const match = testDescription.match(pattern);
    console.log(`\nPattern ${index + 1}: ${pattern}`);
    if (match) {
        console.log('✅ MATCH!');
        console.log('Groups:', match.slice(1));
    } else {
        console.log('❌ No match');
    }
});

// Manual parsing approach
console.log('\n🔧 Manual parsing approach:');
let text = testDescription;
console.log(`Original: "${text}"`);

// Look for pattern: Plastic + "Bottom Stamped" + Mold + Type + (Color)
if (text.includes('Bottom Stamped') && text.includes('(') && text.includes(')')) {
    console.log('✅ Contains Bottom Stamped pattern');
    
    // Extract color from parentheses
    const colorMatch = text.match(/\((\w+)\)$/);
    if (colorMatch) {
        const color = colorMatch[1];
        console.log(`Color: "${color}"`);
        
        // Remove color part
        text = text.replace(/\s*\([^)]+\)$/, '');
        console.log(`After removing color: "${text}"`);
        
        // Split on "Bottom Stamped"
        const parts = text.split(/\s+Bottom\s+Stamped\s+/i);
        if (parts.length === 2) {
            const plastic = parts[0].trim();
            const moldAndType = parts[1].trim();
            
            // Remove disc type from mold
            const mold = moldAndType.replace(/\s+(Mid-Range|Distance Driver|Fairway Driver|Putter|Driver)$/i, '').trim();
            
            console.log(`Plastic: "${plastic}"`);
            console.log(`Mold: "${mold}"`);
            console.log(`Stamp: "Bottom Stamped ${color}"`);
        }
    }
}
