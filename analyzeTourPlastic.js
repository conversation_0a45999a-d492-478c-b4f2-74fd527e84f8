import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

dotenv.config();

const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_KEY;
const supabase = createClient(supabaseUrl, supabaseKey);

async function analyzeTourPlastic() {
    try {
        console.log('🔍 Analyzing "Tour" plastic products...\n');
        
        // Get all Tour products
        const { data: tourProducts, error: tourError } = await supabase
            .from('it_discraft_order_sheet_lines')
            .select('plastic_name, mold_name, stamp_name, vendor_description, cost_price, is_currently_available')
            .eq('plastic_name', 'Tour')
            .limit(20);
        
        if (tourError) {
            console.error('Error getting Tour products:', tourError);
            return;
        }
        
        console.log(`Found ${tourProducts.length} "Tour" products:`);
        tourProducts.forEach((product, index) => {
            console.log(`${index + 1}. "${product.plastic_name}" | "${product.mold_name}" | "${product.stamp_name}"`);
            console.log(`   Description: ${product.vendor_description || 'N/A'}`);
            console.log(`   Price: $${product.cost_price || 'N/A'}, Available: ${product.is_currently_available ? 'Yes' : 'No'}`);
            console.log('');
        });
        
        // Check what Discraft plastics you have in your database
        console.log('🎯 Your existing Discraft plastics:');
        const { data: dbPlastics, error: dbError } = await supabase
            .from('t_plastics')
            .select('id, plastic, description')
            .eq('brand_id', 6)
            .order('plastic');
        
        if (!dbError && dbPlastics) {
            dbPlastics.forEach((plastic, index) => {
                console.log(`${index + 1}. "${plastic.plastic}" (ID: ${plastic.id})`);
                if (plastic.description) {
                    console.log(`   Description: ${plastic.description}`);
                }
            });
        }
        
        console.log('\n💡 Recommendations:');
        console.log('1. Research what "Tour" plastic is in Discraft\'s lineup');
        console.log('2. Map "Tour" to the closest existing plastic in your database');
        console.log('3. Or create a new "Tour" plastic entry');
        console.log('\nCommon Discraft "Tour" series plastics are often premium blends.');
        console.log('Consider mapping to ESP or Elite Z if those are your premium Discraft plastics.');
        
    } catch (error) {
        console.error('❌ Error analyzing Tour plastic:', error);
    }
}

analyzeTourPlastic();
