import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

dotenv.config();

const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_KEY;
const supabase = createClient(supabaseUrl, supabaseKey);

async function calculateMpsIds() {
    try {
        console.log('🔄 Calculating MPS IDs for Discraft products...');
        
        // Get all Discraft products for resetting
        const { data: allProducts, error: allProductsError } = await supabase
            .from('it_discraft_order_sheet_lines')
            .select('id');
        
        if (allProductsError) {
            console.error('Error getting all products:', allProductsError);
            return { success: false, error: allProductsError.message };
        }

        // Reset all calculated_mps_id to NULL first (with WHERE clause)
        console.log('🧹 Resetting existing MPS IDs...');
        for (const product of allProducts) {
            await supabase
                .from('it_discraft_order_sheet_lines')
                .update({ calculated_mps_id: null })
                .eq('id', product.id);
        }

        // Get all Discraft products
        console.log('📊 Getting Discraft products...');
        const { data: discraftProducts, error: discraftError } = await supabase
            .from('it_discraft_order_sheet_lines')
            .select('id, plastic_name, mold_name, stamp_name');
        
        if (discraftError) {
            console.error('Error getting Discraft products:', discraftError);
            return { success: false, error: discraftError.message };
        }

        // Get all Discraft MPS records
        console.log('📊 Getting Discraft MPS records...');
        const { data: mpsRecords, error: mpsError } = await supabase
            .from('t_mps')
            .select(`id, t_plastics!inner(plastic, brand_id), t_molds!inner(mold, brand_id), t_stamps!inner(stamp)`)
            .eq('active', true)
            .eq('t_plastics.brand_id', 6)
            .eq('t_molds.brand_id', 6);
        
        if (mpsError) {
            console.error('Error getting MPS records:', mpsError);
            return { success: false, error: mpsError.message };
        }

        // Create lookup map
        const mpsMap = new Map();
        mpsRecords.forEach(mps => {
            const key = `${mps.t_plastics.plastic.trim()}|${mps.t_molds.mold.trim()}|${mps.t_stamps.stamp.trim()}`;
            mpsMap.set(key, mps.id);
        });

        // Match products to MPS records
        console.log('🔍 Matching products to MPS records...');
        let matchedCount = 0;
        
        for (const product of discraftProducts) {
            const key = `${product.plastic_name.trim()}|${product.mold_name.trim()}|${product.stamp_name.trim()}`;
            const mpsId = mpsMap.get(key);
            
            if (mpsId) {
                await supabase
                    .from('it_discraft_order_sheet_lines')
                    .update({ calculated_mps_id: mpsId })
                    .eq('id', product.id);
                matchedCount++;
            }
        }
        
        const failedCount = discraftProducts.length - matchedCount;
        const successRate = ((matchedCount / discraftProducts.length) * 100).toFixed(2);
        
        console.log('✅ MPS ID calculation completed!');
        console.log(`📊 Results:`);
        console.log(`   • Total products: ${discraftProducts.length}`);
        console.log(`   • Successfully matched: ${matchedCount}`);
        console.log(`   • Failed to match: ${failedCount}`);
        console.log(`   • Success rate: ${successRate}%`);
        
        return {
            success: true,
            totalProducts: discraftProducts.length,
            calculatedCount: matchedCount,
            failedCount: failedCount,
            successRate: parseFloat(successRate)
        };
        
    } catch (error) {
        console.error('❌ Error calculating MPS IDs:', error);
        return { success: false, error: error.message };
    }
}

// Test the function
calculateMpsIds().then(result => {
    console.log('\n🎯 Final result:', result);
});
