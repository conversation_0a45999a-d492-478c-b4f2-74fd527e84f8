import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

dotenv.config();

const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_KEY;
const supabase = createClient(supabaseUrl, supabaseKey);

async function testRobustMapping() {
    try {
        console.log('🔍 Testing robust mapping keys...\n');
        
        // Check if we have the new mapping keys
        const { data: sampleProducts, error } = await supabase
            .from('it_discraft_order_sheet_lines')
            .select('*')
            .limit(5);
        
        if (error) throw error;
        
        if (sampleProducts.length === 0) {
            console.log('❌ No products found in database');
            return;
        }
        
        console.log('📊 Sample product structure:');
        const firstProduct = sampleProducts[0];
        Object.keys(firstProduct).forEach(key => {
            console.log(`  ${key}: ${firstProduct[key]}`);
        });
        
        // Check for robust mapping fields
        console.log('\n🔍 Checking for robust mapping fields:');
        const hasMapping = firstProduct.hasOwnProperty('excel_mapping_key');
        const hasFileHash = firstProduct.hasOwnProperty('import_file_hash');
        const hasRawWeight = firstProduct.hasOwnProperty('raw_weight_range');
        
        console.log(`  excel_mapping_key: ${hasMapping ? '✅ Present' : '❌ Missing'}`);
        console.log(`  import_file_hash: ${hasFileHash ? '✅ Present' : '❌ Missing'}`);
        console.log(`  raw_weight_range: ${hasRawWeight ? '✅ Present' : '❌ Missing'}`);
        
        if (hasMapping) {
            console.log('\n📋 Sample mapping keys:');
            sampleProducts.forEach((product, index) => {
                console.log(`  ${index + 1}. ${product.excel_mapping_key || 'NULL'}`);
            });
        }
        
    } catch (error) {
        console.error('❌ Error testing robust mapping:', error);
    }
}

testRobustMapping();
