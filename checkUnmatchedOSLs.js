import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

dotenv.config();

const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_KEY;
const supabase = createClient(supabaseUrl, supabaseKey);

async function checkUnmatchedOSLs() {
    try {
        console.log('=== CHECKING UNMATCHED INNOVA OSLs ===\n');
        
        // Get count of unmatched OSLs (vendor_internal_id = NULL)
        const { count: unmatchedCount, error: unmatchedError } = await supabase
            .from('t_order_sheet_lines')
            .select('*', { count: 'exact', head: true })
            .eq('vendor_id', 2)
            .is('vendor_internal_id', null);
        
        if (unmatchedError) {
            console.error('Error getting unmatched count:', unmatchedError);
            return;
        }
        
        console.log(`📊 Unmatched Innova OSLs (vendor_internal_id = NULL): ${unmatchedCount}`);
        
        // Get count of matched OSLs for comparison
        const { count: matchedCount, error: matchedError } = await supabase
            .from('t_order_sheet_lines')
            .select('*', { count: 'exact', head: true })
            .eq('vendor_id', 2)
            .not('vendor_internal_id', 'is', null);
        
        if (matchedError) {
            console.error('Error getting matched count:', matchedError);
            return;
        }
        
        console.log(`✅ Matched Innova OSLs (vendor_internal_id set): ${matchedCount}`);
        console.log(`📈 Total Innova OSLs: ${unmatchedCount + matchedCount}`);
        console.log(`📊 Unmatched percentage: ${((unmatchedCount / (unmatchedCount + matchedCount)) * 100).toFixed(1)}%`);
        
        // Get a sample of unmatched OSLs to see what we're working with
        const { data: sampleOSLs, error: sampleError } = await supabase
            .from('t_order_sheet_lines')
            .select(`
                id, g_code, mps_id,
                t_mps!inner (
                    t_molds!inner (mold),
                    t_plastics!inner (plastic),
                    t_stamps!inner (stamp)
                )
            `)
            .eq('vendor_id', 2)
            .is('vendor_internal_id', null)
            .limit(10);
        
        if (sampleError) {
            console.error('Error getting sample:', sampleError);
            return;
        }
        
        console.log('\n=== SAMPLE UNMATCHED OSLs ===');
        sampleOSLs.forEach(osl => {
            console.log(`OSL ${osl.id}: ${osl.t_mps.t_molds.mold} ${osl.t_mps.t_plastics.plastic} ${osl.t_mps.t_stamps.stamp} (MPS: ${osl.mps_id})`);
        });
        
        if (unmatchedCount > 0) {
            console.log(`\n🚀 Ready to build Phase 2 matching for ${unmatchedCount} unmatched OSLs!`);
        } else {
            console.log('\n✅ All OSLs are already matched! No Phase 2 needed.');
        }
        
    } catch (error) {
        console.error('Check failed:', error);
    }
}

checkUnmatchedOSLs();
