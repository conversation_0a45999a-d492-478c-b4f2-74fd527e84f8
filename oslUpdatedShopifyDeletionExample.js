// oslUpdatedShopifyDeletionExample.js
// Example demonstrating the OSL updated Shopify deletion workflow

/*
=== Example Workflow: OSL Weight Range Update ===

Initial State:
- OSL ID: 16890
- Weight Range: 170-175g  
- Color: Blue (ID: 6)
- MPS: Innova Champion Destroyer
- Shopify Status: Uploaded (shopify_uploaded_at: '2024-01-15T08:00:00Z')
- Shopify SKU: OS16890

User Action:
- Admin updates weight range from 170-175g to 165-180g

System Response:
1. Database trigger fires on t_order_sheet_lines UPDATE
2. Enqueues osl_updated task with payload: {"id": 16890, "old_data": {...}}

Task Processing (processOslUpdatedTask.js):
3. Checks OSL record: shopify_uploaded_at IS NOT NULL
4. Generates SKU: "OS16890"
5. Enqueues delete_sku_from_shopify task:
   {
     "task_type": "delete_sku_from_shopify",
     "payload": {
       "sku": "OS16890", 
       "reason": "OSL updated - removing previous version from Shopify"
     },
     "scheduled_at": "2024-01-15T10:30:00Z" // Immediate
   }
6. Updates OSL record:
   - shopify_uploaded_at = NULL
   - shopify_product_uploaded_notes = "Reset due to OSL update - previous version deleted from Shopify"
7. Creates child tasks:
   - osl_updated_unlink_discs (immediate)
   - set_inv_osl_to_0 (15 seconds delay)
   - generate_osl_fields (immediate)
   - match_osl_to_discs (30 seconds delay)
   - check_if_osl_is_ready (45 seconds delay)

Shopify Deletion Processing (processDeleteSkuFromShopifyTask.js):
8. Finds product variant with SKU "OS16890" in Shopify
9. Gets product ID from variant
10. Deletes entire product using productDelete mutation
11. Logs success: "Successfully deleted product for SKU OS16890 from Shopify"

Continued OSL Processing:
12. Unlinks existing discs from OSL
13. Sets inventory to 0
14. Regenerates OSL fields with new weight range
15. Re-matches discs (now includes 165-169g and 176-180g discs)
16. Checks if OSL is ready for re-upload

Final State:
- OSL ID: 16890
- Weight Range: 165-180g (updated)
- Color: Blue (unchanged)
- MPS: Innova Champion Destroyer (unchanged)
- Shopify Status: NULL (ready for re-upload)
- Old Shopify product: DELETED
- New discs matched: All blue Destroyers weighing 165-180g

Re-upload Trigger:
17. When check_if_osl_is_ready determines OSL is ready
18. New publish_product_osl task is enqueued
19. New Shopify product created with updated weight range
20. shopify_uploaded_at set to new timestamp
*/

// Example monitoring queries you can run:

const monitoringQueries = {
  // Check if OSL update triggered Shopify deletion
  checkOslUpdateWithDeletion: `
    SELECT 
      id,
      status,
      result->>'message' as message,
      result->>'shopify_deletion_enqueued' as deletion_enqueued,
      created_at
    FROM t_task_queue 
    WHERE task_type = 'osl_updated' 
      AND payload->>'id' = '16890'
    ORDER BY created_at DESC 
    LIMIT 1;
  `,

  // Find the deletion task that was created
  findDeletionTask: `
    SELECT 
      id,
      status,
      payload,
      result,
      created_at,
      processed_at
    FROM t_task_queue 
    WHERE task_type = 'delete_sku_from_shopify' 
      AND payload->>'sku' = 'OS16890'
    ORDER BY created_at DESC 
    LIMIT 1;
  `,

  // Check current OSL status
  checkOslStatus: `
    SELECT 
      id,
      min_weight,
      max_weight,
      color_id,
      shopify_uploaded_at,
      shopify_product_uploaded_notes,
      updated_at
    FROM t_order_sheet_lines 
    WHERE id = 16890;
  `,

  // Check which discs are now matched to this OSL
  checkMatchedDiscs: `
    SELECT 
      d.id,
      d.weight,
      d.color_id,
      d.sold_date,
      d.order_sheet_line_id,
      c.color
    FROM t_discs d
    LEFT JOIN t_colors c ON d.color_id = c.id
    WHERE d.order_sheet_line_id = 16890
      AND d.sold_date IS NULL
    ORDER BY d.weight;
  `,

  // Check inventory for this OSL
  checkInventory: `
    SELECT 
      id,
      available_quantity,
      updated_at
    FROM t_inv_osl 
    WHERE id = 16890;
  `
};

// Example task queue entries you would see:

const exampleTaskQueueEntries = [
  {
    id: 50001,
    task_type: 'osl_updated',
    payload: {
      id: 16890,
      old_data: {
        min_weight: 170,
        max_weight: 175,
        shopify_uploaded_at: '2024-01-15T08:00:00Z'
      }
    },
    status: 'completed',
    result: {
      message: 'Successfully created 5 child tasks for OSL id=16890. Shopify deletion enqueued.',
      osl_id: 16890,
      shopify_deletion_enqueued: true,
      tasks: [
        { task_type: 'osl_updated_unlink_discs', task_id: 50002 },
        { task_type: 'set_inv_osl_to_0', task_id: 50003 },
        { task_type: 'generate_osl_fields', task_id: 50004 },
        { task_type: 'match_osl_to_discs', task_id: 50005 },
        { task_type: 'check_if_osl_is_ready', task_id: 50006 }
      ]
    }
  },
  {
    id: 50007,
    task_type: 'delete_sku_from_shopify',
    payload: {
      sku: 'OS16890',
      reason: 'OSL updated - removing previous version from Shopify'
    },
    status: 'completed',
    result: {
      message: 'Successfully deleted product for SKU OS16890 from Shopify',
      sku: 'OS16890',
      deletedProductId: 'gid://shopify/Product/8723341705404',
      productTitle: 'Innova Champion Destroyer - Blue - 170-175g',
      productHandle: 'innova-champion-destroyer-blue-170-175g',
      reason: 'OSL updated - removing previous version from Shopify'
    }
  }
];

// Example database state changes:

const databaseStateChanges = {
  before: {
    t_order_sheet_lines: {
      id: 16890,
      min_weight: 170,
      max_weight: 175,
      color_id: 6,
      shopify_uploaded_at: '2024-01-15T08:00:00Z',
      shopify_product_uploaded_notes: 'Success! Shopify product/variant processed for OSL via publishProductOSL.js'
    },
    matched_discs_count: 12, // Discs weighing 170-175g
    t_inv_osl: {
      id: 16890,
      available_quantity: 12
    }
  },
  
  after: {
    t_order_sheet_lines: {
      id: 16890,
      min_weight: 165, // Updated
      max_weight: 180, // Updated  
      color_id: 6,
      shopify_uploaded_at: null, // Reset
      shopify_product_uploaded_notes: 'Reset due to OSL update - previous version deleted from Shopify'
    },
    matched_discs_count: 18, // More discs now match (165-180g range)
    t_inv_osl: {
      id: 16890,
      available_quantity: 18 // Updated after re-matching
    }
  }
};

console.log('OSL Updated Shopify Deletion Example');
console.log('=====================================');
console.log('');
console.log('This file demonstrates the workflow when an OSL is updated');
console.log('and needs to be removed from Shopify before re-uploading.');
console.log('');
console.log('See the comments above for the complete step-by-step process.');
console.log('');
console.log('Monitoring queries:', JSON.stringify(monitoringQueries, null, 2));
console.log('');
console.log('Example task queue entries:', JSON.stringify(exampleTaskQueueEntries, null, 2));
console.log('');
console.log('Database state changes:', JSON.stringify(databaseStateChanges, null, 2));

export { 
  monitoringQueries, 
  exampleTaskQueueEntries, 
  databaseStateChanges 
};
