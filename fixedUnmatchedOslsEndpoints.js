// Fixed endpoints for unmatched OSLs - replace the broken ones in adminserver.js

// First endpoint fix (around line 1365)
app.get('/api/discraft/review-unmatched-products', async (req, res) => {
  console.log('[adminServer] Getting unmatched Discraft products and OSLs');
  try {
    const { data: unmatchedProducts, error: productsError } = await supabase
      .from('it_discraft_order_sheet_lines')
      .select('plastic_name, mold_name, stamp_name, vendor_description, min_weight, max_weight, is_currently_available')
      .is('calculated_mps_id', null)
      .eq('is_orderable', true)
      .limit(50);
    if (productsError) throw productsError;
    
    // Get matched MPS IDs first
    const { data: matchedMpsIds, error: matchedError } = await supabase
      .from('it_discraft_order_sheet_lines')
      .select('calculated_mps_id')
      .not('calculated_mps_id', 'is', null);
    
    if (matchedError) throw matchedError;
    
    const matchedIds = matchedMpsIds.map(row => row.calculated_mps_id);
    
    const { data: unmatchedOsls, error: oslsError } = await supabase
      .from('t_order_sheet_lines')
      .select(`id, mps_id, min_weight, max_weight, color_id, t_mps!inner(t_plastics!inner(plastic), t_molds!inner(mold), t_stamps!inner(stamp))`)
      .eq('vendor_id', 9)
      .not('mps_id', 'in', `(${matchedIds.length > 0 ? matchedIds.join(',') : '0'})`)
      .limit(50);
    if (oslsError) throw oslsError;
    res.json({
      success: true,
      unmatchedProducts: unmatchedProducts || [],
      unmatchedOsls: unmatchedOsls || []
    });
  } catch (err) {
    console.error('[adminServer] Exception getting unmatched products:', err);
    res.status(500).json({ success: false, error: err.message });
  }
});

// Second endpoint fix (around line 1391)
app.get('/api/discraft/review-unmatched-osls', async (req, res) => {
  console.log('[adminServer] Getting unmatched Discraft OSLs');
  try {
    // Get matched MPS IDs first
    const { data: matchedMpsIds, error: matchedError } = await supabase
      .from('it_discraft_order_sheet_lines')
      .select('calculated_mps_id')
      .not('calculated_mps_id', 'is', null);
    
    if (matchedError) throw matchedError;
    
    const matchedIds = matchedMpsIds.map(row => row.calculated_mps_id);
    
    const { data: unmatchedOsls, error: oslsError } = await supabase
      .from('t_order_sheet_lines')
      .select(`id, mps_id, min_weight, max_weight, color_id, t_colors(color), t_mps!inner(t_plastics!inner(plastic), t_molds!inner(mold), t_stamps!inner(stamp))`)
      .eq('vendor_id', 9)
      .not('mps_id', 'in', `(${matchedIds.length > 0 ? matchedIds.join(',') : '0'})`)
      .limit(50);
    if (oslsError) throw oslsError;

    // Get total count of unmatched OSLs
    const { count: totalCount, error: countError } = await supabase
      .from('t_order_sheet_lines')
      .select('*', { count: 'exact', head: true })
      .eq('vendor_id', 9)
      .not('mps_id', 'in', `(${matchedIds.length > 0 ? matchedIds.join(',') : '0'})`);

    if (countError) throw countError;
    const oslsWithColors = unmatchedOsls.map(osl => ({ ...osl, color_name: osl.t_colors ? osl.t_colors.color : null }));
    const combinations = {};
    oslsWithColors.forEach(osl => {
      const mps = osl.t_mps;
      const key = `${mps.t_plastics.plastic}|${mps.t_molds.mold}|${mps.t_stamps.stamp}`;
      if (!combinations[key]) {
        combinations[key] = { plastic: mps.t_plastics.plastic, mold: mps.t_molds.mold, stamp: mps.t_stamps.stamp, count: 0 };
      }
      combinations[key].count++;
    });
    const commonMismatches = Object.values(combinations).sort((a, b) => b.count - a.count).slice(0, 10);
    res.json({ success: true, unmatchedOsls: oslsWithColors, totalUnmatchedOsls: totalCount || 0, commonMismatches });
  } catch (err) {
    console.error('[adminServer] Exception getting unmatched OSLs:', err);
    res.status(500).json({ success: false, error: err.message });
  }
});

console.log('✅ Fixed unmatched OSLs endpoints created!');
console.log('📋 Key changes made:');
console.log('1. Added matched MPS IDs retrieval before OSL queries');
console.log('2. Replaced broken SQL subquery with proper array join');
console.log('3. Fixed both endpoints to use consistent logic');
console.log('4. No more "invalid input syntax for type integer" errors');
