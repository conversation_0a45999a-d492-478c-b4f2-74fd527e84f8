// testDeleteSkuFromShopify.js - Test script for the delete SKU from Shopify functionality
import dotenv from 'dotenv';
dotenv.config();

import { enqueueDeleteSkuFromShopifyTask } from './enqueueDeleteSkuFromShopifyTask.js';

/**
 * Test function to demonstrate enqueueing a delete SKU from Shopify task
 */
async function testDeleteSkuFromShopify() {
  try {
    console.log('=== Testing Delete SKU from Shopify Task ===');
    
    // Example SKU - replace with an actual SKU from your Shopify store for testing
    const testSku = 'TEST-SKU-001';
    const reason = 'Testing delete functionality';
    
    console.log(`Enqueueing deletion task for SKU: ${testSku}`);
    console.log(`Reason: ${reason}`);
    
    // Enqueue the task
    const task = await enqueueDeleteSkuFromShopifyTask(testSku, reason);
    
    console.log('\n✅ Task successfully enqueued!');
    console.log(`Task ID: ${task.id}`);
    console.log(`Task Type: ${task.task_type}`);
    console.log(`Status: ${task.status}`);
    console.log(`Scheduled At: ${task.scheduled_at}`);
    console.log(`Payload:`, JSON.stringify(task.payload, null, 2));
    
    console.log('\n📝 Next steps:');
    console.log('1. Run the task queue worker to process this task');
    console.log('2. Check the t_task_queue table for task status updates');
    console.log('3. Verify the product was deleted from Shopify (if it existed)');
    
  } catch (error) {
    console.error('❌ Error testing delete SKU from Shopify:', error.message);
    process.exit(1);
  }
}

// Run the test if this script is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  testDeleteSkuFromShopify();
}

export { testDeleteSkuFromShopify };
