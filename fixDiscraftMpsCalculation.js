// Quick fix for the MPS calculation endpoint
// This replaces the broken stored procedure call with working logic

const fixedCalculateMpsEndpoint = `
app.post('/api/discraft/calculate-mps', async (req, res) => {
  console.log('[adminServer] Calculating Discraft MPS IDs');
  try {
    // Get all Discraft products for resetting
    const { data: allProducts, error: allProductsError } = await supabase
      .from('it_discraft_order_sheet_lines')
      .select('id');
    
    if (allProductsError) {
      console.error('[adminServer] Error getting all products:', allProductsError);
      return res.status(500).json({ error: allProductsError.message });
    }
    
    // Reset calculated_mps_id to NULL for all products (batch approach)
    const batchSize = 100;
    for (let i = 0; i < allProducts.length; i += batchSize) {
      const batch = allProducts.slice(i, i + batchSize);
      const ids = batch.map(p => p.id);
      
      const { error: resetError } = await supabase
        .from('it_discraft_order_sheet_lines')
        .update({ calculated_mps_id: null })
        .in('id', ids);
      
      if (resetError) {
        console.error('[adminServer] Error resetting batch:', resetError);
        return res.status(500).json({ error: resetError.message });
      }
    }
    
    // Get products for matching
    const { data: discraftProducts, error: discraftError } = await supabase
      .from('it_discraft_order_sheet_lines')
      .select('id, plastic_name, mold_name, stamp_name');
    
    if (discraftError) {
      console.error('[adminServer] Error getting Discraft products:', discraftError);
      return res.status(500).json({ error: discraftError.message });
    }
    
    // Get Discraft MPS records
    const { data: mpsRecords, error: mpsError } = await supabase
      .from('t_mps')
      .select(\`id, t_plastics!inner(plastic, brand_id), t_molds!inner(mold, brand_id), t_stamps!inner(stamp)\`)
      .eq('active', true)
      .eq('t_plastics.brand_id', 6)
      .eq('t_molds.brand_id', 6);
    
    if (mpsError) {
      console.error('[adminServer] Error getting MPS records:', mpsError);
      return res.status(500).json({ error: mpsError.message });
    }
    
    // Create lookup map
    const mpsMap = new Map();
    mpsRecords.forEach(mps => {
      const key = \`\${mps.t_plastics.plastic.trim()}|\${mps.t_molds.mold.trim()}|\${mps.t_stamps.stamp.trim()}\`;
      mpsMap.set(key, mps.id);
    });
    
    // Match products in batches
    let matchedCount = 0;
    for (let i = 0; i < discraftProducts.length; i += batchSize) {
      const batch = discraftProducts.slice(i, i + batchSize);
      
      for (const product of batch) {
        const key = \`\${product.plastic_name.trim()}|\${product.mold_name.trim()}|\${product.stamp_name.trim()}\`;
        const mpsId = mpsMap.get(key);
        
        if (mpsId) {
          const { error } = await supabase
            .from('it_discraft_order_sheet_lines')
            .update({ calculated_mps_id: mpsId })
            .eq('id', product.id);
          
          if (!error) matchedCount++;
        }
      }
    }
    
    const failedCount = discraftProducts.length - matchedCount;
    const successRate = ((matchedCount / discraftProducts.length) * 100).toFixed(2);
    
    res.json({
      success: true,
      totalProducts: discraftProducts.length,
      calculatedCount: matchedCount,
      failedCount: failedCount,
      successRate: parseFloat(successRate)
    });
  } catch (err) {
    console.error('[adminServer] Exception calculating MPS IDs:', err);
    res.status(500).json({ success: false, error: err.message });
  }
});
`;

console.log('Copy this endpoint to replace the broken one in adminserver.js:');
console.log(fixedCalculateMpsEndpoint);
