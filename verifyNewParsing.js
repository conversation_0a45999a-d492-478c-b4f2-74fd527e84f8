import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

dotenv.config();

const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_KEY;
const supabase = createClient(supabaseUrl, supabaseKey);

async function verifyNewParsing() {
    try {
        console.log('🔍 Verifying new parsing features...\n');
        
        // Check Fly Dye parsing
        console.log('🎨 Fly Dye Examples:');
        const { data: flyDye, error: flyError } = await supabase
            .from('it_discraft_order_sheet_lines')
            .select('plastic_name, mold_name, stamp_name, color_name, excel_row')
            .eq('plastic_name', 'Elite Z Fly-Dyed')
            .order('excel_row')
            .limit(10);
        
        if (flyError) throw flyError;
        
        flyDye.forEach(item => {
            console.log(`  Row ${item.excel_row}: ${item.plastic_name} ${item.mold_name} (${item.stamp_name}) - Color: ${item.color_name}`);
        });
        
        // Check Z Glow parsing
        console.log('\n💡 Z Glow Examples:');
        const { data: glow, error: glowError } = await supabase
            .from('it_discraft_order_sheet_lines')
            .select('plastic_name, mold_name, stamp_name, color_name, excel_row')
            .eq('plastic_name', 'Elite Z Glow')
            .order('excel_row')
            .limit(5);
        
        if (glowError) throw glowError;
        
        glow.forEach(item => {
            console.log(`  Row ${item.excel_row}: ${item.plastic_name} ${item.mold_name} (${item.stamp_name}) - Color: ${item.color_name}`);
        });
        
        // Check ESP parsing
        console.log('\n🎯 ESP Examples:');
        const { data: esp, error: espError } = await supabase
            .from('it_discraft_order_sheet_lines')
            .select('plastic_name, mold_name, stamp_name, color_name, excel_row')
            .eq('plastic_name', 'ESP')
            .order('excel_row')
            .limit(10);
        
        if (espError) throw espError;
        
        esp.forEach(item => {
            console.log(`  Row ${item.excel_row}: ${item.plastic_name} ${item.mold_name} (${item.stamp_name}) - Color: ${item.color_name}`);
        });
        
        // Check special stamps
        console.log('\n🏆 Special Stamp Examples:');
        const { data: stamps, error: stampError } = await supabase
            .from('it_discraft_order_sheet_lines')
            .select('plastic_name, mold_name, stamp_name, excel_row')
            .in('stamp_name', [
                'Paul McBeth Large Signature',
                'Dye Line Blank Top Bottom'
            ])
            .order('excel_row');
        
        if (stampError) throw stampError;
        
        stamps.forEach(item => {
            console.log(`  Row ${item.excel_row}: ${item.plastic_name} ${item.mold_name} (${item.stamp_name})`);
        });
        
        // Summary by plastic type
        console.log('\n📊 New Plastic Types Summary:');
        const { data: summary, error: summaryError } = await supabase
            .from('it_discraft_order_sheet_lines')
            .select('plastic_name, is_currently_available')
            .in('plastic_name', ['Elite Z Fly-Dyed', 'Elite Z Glow', 'ESP'])
            .order('plastic_name');
        
        if (summaryError) throw summaryError;
        
        const plasticStats = {};
        summary.forEach(item => {
            if (!plasticStats[item.plastic_name]) {
                plasticStats[item.plastic_name] = { total: 0, available: 0 };
            }
            plasticStats[item.plastic_name].total++;
            if (item.is_currently_available) {
                plasticStats[item.plastic_name].available++;
            }
        });
        
        Object.entries(plasticStats).forEach(([plastic, counts]) => {
            console.log(`  ${plastic}: ${counts.available}/${counts.total} available`);
        });
        
    } catch (error) {
        console.error('❌ Error verifying parsing:', error);
    }
}

verifyNewParsing();
