// Debug DX Bottom Stamped parsing
import { parseInnovaDescription } from './phase2InnovaMatching.js';

console.log('🔍 Debugging DX Bottom Stamped parsing...');

const testDesc = 'DX Aviar Putt & Approach - Bottom Stamped';
console.log(`Input: "${testDesc}"`);

const result = parseInnovaDescription(testDesc);
console.log('Result:', result);

console.log('\n🔧 Expected:');
console.log('Plastic: "DX"');
console.log('Mold: "Aviar Putter"');
console.log('Stamp: "Bottom Stamped"');

console.log('\n🔧 Actual:');
console.log(`Plastic: "${result?.plastic}"`);
console.log(`Mold: "${result?.mold}"`);
console.log(`Stamp: "${result?.stamp}"`);

// Test the split manually
console.log('\n🔧 Manual split test:');
const parts = testDesc.split(/\s+-\s+Bottom\s+Stamped/i);
console.log('Split parts:', parts);
if (parts.length >= 1) {
    const moldAndPlastic = parts[0].trim();
    console.log(`moldAndPlastic: "${moldAndPlastic}"`);
}

// Test the mold extraction manually
console.log('\n🔧 Manual mold extraction test:');
let testText = 'DX Aviar Putt & Approach';
console.log(`Starting with: "${testText}"`);

testText = testText.replace(/\s+(Putt & Approach|Fairway Distance Driver|Distance Driver|Fairway Driver|Mid-Range|Midrange|Specialty Disc|Putter|Driver|Approach)(\s+Disc)?$/i, '');
console.log(`After removing disc types: "${testText}"`);

testText = testText.replace(/\s+-\s+Bottom\s+Stamped.*$/i, '');
console.log(`After removing Bottom Stamped: "${testText}"`);

if (testText.trim() === 'Aviar') {
    testText = 'Aviar Putter';
    console.log(`After Aviar conversion: "${testText}"`);
}
