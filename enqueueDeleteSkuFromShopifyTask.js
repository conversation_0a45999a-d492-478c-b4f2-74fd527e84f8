// enqueueDeleteSkuFromShopifyTask.js
import dotenv from 'dotenv';
dotenv.config();

import { createClient } from '@supabase/supabase-js';

// Initialize Supabase client
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('ERROR: Missing Supabase URL or key in environment variables.');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

/**
 * Helper function to enqueue a delete SKU from Shopify task
 * @param {string} sku - The SKU to delete
 * @param {string} reason - Optional reason for deletion
 * @param {Date} scheduledAt - Optional scheduled time (defaults to 5 minutes from now)
 * @returns {Promise<Object>} The created task data
 */
export async function enqueueDeleteSkuFromShopifyTask(sku, reason = null, scheduledAt = null) {
  if (!sku) {
    throw new Error('SKU is required to enqueue delete SKU from Shopify task');
  }
  
  const now = new Date();
  const defaultScheduledAt = new Date(now.getTime() + 5 * 60 * 1000); // 5 minutes from now
  
  const task = {
    task_type: 'delete_sku_from_shopify',
    payload: {
      sku: sku,
      reason: reason || 'Scheduled deletion'
    },
    status: 'pending',
    scheduled_at: (scheduledAt || defaultScheduledAt).toISOString(),
    created_at: now.toISOString(),
    enqueued_by: 'enqueueDeleteSkuFromShopifyTask'
  };
  
  console.log(`[enqueueDeleteSkuFromShopifyTask] Enqueueing delete SKU from Shopify task for SKU: ${sku}`);
  
  const { data, error } = await supabase
    .from('t_task_queue')
    .insert(task)
    .select()
    .single();
  
  if (error) {
    console.error(`[enqueueDeleteSkuFromShopifyTask] Error enqueueing task:`, error);
    throw error;
  }
  
  console.log(`[enqueueDeleteSkuFromShopifyTask] Successfully enqueued task ${data.id} for SKU: ${sku}`);
  return data;
}

/**
 * Standalone function that can be called directly or used as a module
 * @param {string} sku - The SKU to delete
 * @param {string} reason - Optional reason for deletion
 */
async function main() {
  // Check if this script is being run directly
  if (process.argv.length < 3) {
    console.error('Usage: node enqueueDeleteSkuFromShopifyTask.js <sku> [reason]');
    console.error('Example: node enqueueDeleteSkuFromShopifyTask.js "D12345" "Product discontinued"');
    process.exit(1);
  }

  const sku = process.argv[2];
  const reason = process.argv[3] || 'Manual deletion via script';

  try {
    const task = await enqueueDeleteSkuFromShopifyTask(sku, reason);
    console.log(`Successfully enqueued deletion task for SKU: ${sku}`);
    console.log(`Task ID: ${task.id}`);
    console.log(`Scheduled for: ${task.scheduled_at}`);
  } catch (error) {
    console.error(`Failed to enqueue deletion task for SKU ${sku}:`, error.message);
    process.exit(1);
  }
}

// Run main function if this script is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  main();
}
