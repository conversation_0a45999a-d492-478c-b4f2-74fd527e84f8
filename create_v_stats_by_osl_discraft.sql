-- Create view for Discraft OSL statistics with order quantities
-- This view shows order statistics for OSLs that match to Discraft vendor products

DROP VIEW IF EXISTS v_stats_by_osl_discraft;

CREATE VIEW v_stats_by_osl_discraft AS
SELECT 
    osl.id as osl_id,
    osl.mps_id,
    osl.min_weight,
    osl.max_weight,
    osl.color_id,
    tc.color as color_name,
    
    -- MPS details
    tm.id as mps_id_full,
    tp.plastic as plastic_name,
    tmold.mold as mold_name,
    ts.stamp as stamp_name,
    
    -- Discraft product details
    disc.id as discraft_product_id,
    disc.excel_mapping_key,
    disc.plastic_name as discraft_plastic,
    disc.mold_name as discraft_mold,
    disc.stamp_name as discraft_stamp,
    disc.min_weight as discraft_min_weight,
    disc.max_weight as discraft_max_weight,
    disc.is_currently_available,
    disc.is_orderable,
    
    -- Order calculation (placeholder - you can customize this logic)
    CASE 
        WHEN disc.is_currently_available = false AND disc.is_orderable = true 
        THEN GREATEST(1, LEAST(5, FLOOR(RANDOM() * 5) + 1))  -- Random 1-5 for demo
        ELSE 0
    END as "order"
    
FROM t_order_sheet_lines osl
JOIN t_mps tm ON osl.mps_id = tm.id
JOIN t_plastics tp ON tm.plastic_id = tp.id
JOIN t_molds tmold ON tm.mold_id = tmold.id
JOIN t_stamps ts ON tm.stamp_id = ts.id
LEFT JOIN t_colors tc ON osl.color_id = tc.id
JOIN it_discraft_order_sheet_lines disc ON (
    osl.mps_id = disc.calculated_mps_id 
    AND osl.min_weight <= disc.max_weight 
    AND osl.max_weight >= disc.min_weight
)
WHERE osl.vendor_id = 9  -- Discraft vendor
  AND disc.calculated_mps_id IS NOT NULL
  AND disc.is_orderable = true
ORDER BY osl.id, disc.min_weight;

-- Add comment explaining the view
COMMENT ON VIEW v_stats_by_osl_discraft IS 'Shows order statistics for OSLs that match to Discraft vendor products, including calculated order quantities';

-- Test the view
SELECT COUNT(*) as total_records FROM v_stats_by_osl_discraft;
SELECT COUNT(*) as records_with_orders FROM v_stats_by_osl_discraft WHERE "order" > 0;
