import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

// Initialize Supabase client
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_KEY;
const supabase = createClient(supabaseUrl, supabaseKey);

async function debugBrokenLink() {
    try {
        console.log('=== DEBUGGING BROKEN LINK: OSL 18003 ===\n');
        
        // Get the specific OSL record
        const { data: oslRecord, error: oslError } = await supabase
            .from('t_order_sheet_lines')
            .select('id, mps_id, vendor_internal_id, vendor_id')
            .eq('id', 18003)
            .single();
        
        if (oslError) {
            console.error('Error fetching OSL:', oslError);
            return;
        }
        
        console.log('OSL Record:');
        console.log(`  OSL ID: ${oslRecord.id}`);
        console.log(`  MPS ID: ${oslRecord.mps_id}`);
        console.log(`  vendor_internal_id: ${oslRecord.vendor_internal_id} (type: ${typeof oslRecord.vendor_internal_id})`);
        console.log(`  vendor_id: ${oslRecord.vendor_id}`);
        
        // Check if there's an Innova record with this internal_id
        const { data: innovaRecord, error: innovaError } = await supabase
            .from('it_innova_order_sheet_lines')
            .select('internal_id, description, matrix_option_1')
            .eq('internal_id', oslRecord.vendor_internal_id)
            .single();
        
        if (innovaError) {
            console.log('\n❌ Innova Record NOT FOUND:');
            console.log(`  Searched for internal_id = ${oslRecord.vendor_internal_id}`);
            console.log(`  Error: ${innovaError.message}`);
            
            // Try to find similar records
            console.log('\n🔍 Searching for similar Innova records...');
            const { data: similarRecords, error: searchError } = await supabase
                .from('it_innova_order_sheet_lines')
                .select('internal_id, description, matrix_option_1')
                .ilike('description', '%Leopard%')
                .ilike('description', '%Star%')
                .limit(5);
            
            if (!searchError && similarRecords.length > 0) {
                console.log('Similar records found:');
                similarRecords.forEach(record => {
                    console.log(`  ID: ${record.internal_id} → ${record.description} (${record.matrix_option_1})`);
                });
            }
        } else {
            console.log('\n✅ Innova Record FOUND:');
            console.log(`  internal_id: ${innovaRecord.internal_id} (type: ${typeof innovaRecord.internal_id})`);
            console.log(`  description: ${innovaRecord.description}`);
            console.log(`  matrix_option_1: ${innovaRecord.matrix_option_1}`);
        }
        
        // Check the total count of Innova records
        const { count: totalInnova } = await supabase
            .from('it_innova_order_sheet_lines')
            .select('*', { count: 'exact', head: true });
        
        console.log(`\n📊 Total Innova records in database: ${totalInnova}`);
        
        // Check if there are any records with internal_id around 6292
        const { data: nearbyRecords, error: nearbyError } = await supabase
            .from('it_innova_order_sheet_lines')
            .select('internal_id, description')
            .gte('internal_id', 6290)
            .lte('internal_id', 6295)
            .order('internal_id');
        
        if (!nearbyError && nearbyRecords.length > 0) {
            console.log('\n🔍 Records with internal_id near 6292:');
            nearbyRecords.forEach(record => {
                console.log(`  ${record.internal_id}: ${record.description}`);
            });
        } else {
            console.log('\n❌ No records found with internal_id near 6292');
        }
        
    } catch (error) {
        console.error('Debug failed:', error);
    }
}

// Run the debug
debugBrokenLink();
