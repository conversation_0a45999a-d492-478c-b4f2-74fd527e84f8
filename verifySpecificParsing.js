import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

dotenv.config();

const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_KEY;
const supabase = createClient(supabaseUrl, supabaseKey);

async function verifySpecificParsing() {
    try {
        console.log('🔍 Verifying specific parsing examples...\n');
        
        // Check signature series parsing
        console.log('🏆 Signature Series Examples:');
        
        const { data: signatures, error: sigError } = await supabase
            .from('it_discraft_order_sheet_lines')
            .select('plastic_name, mold_name, stamp_name, excel_row')
            .in('stamp_name', [
                'PM Logo Stock Stamp', 
                'PP Logo Stock Stamp', 
                '<PERSON> - 2018 World Champion',
                'Ricky W<PERSON>i - 2x World Champion Signature',
                'PP 29190 5X Paige Pierce World Champion'
            ])
            .order('excel_row');
        
        if (sigError) throw sigError;
        
        signatures.forEach(item => {
            console.log(`  Row ${item.excel_row}: ${item.plastic_name} ${item.mold_name} (${item.stamp_name})`);
        });
        
        // Check specific plastic mappings
        console.log('\n🧪 Plastic Mapping Examples:');
        
        const { data: plastics, error: plasticError } = await supabase
            .from('it_discraft_order_sheet_lines')
            .select('plastic_name, mold_name, raw_line_type, excel_row')
            .in('raw_line_type', ['McBeth', 'Pierce', 'X', 'Z Lite', 'PS Z', 'RW Z', 'PP Z'])
            .order('excel_row')
            .limit(10);
        
        if (plasticError) throw plasticError;
        
        plastics.forEach(item => {
            console.log(`  Row ${item.excel_row}: "${item.raw_line_type}" → "${item.plastic_name}" (${item.mold_name})`);
        });
        
        // Check special model parsing
        console.log('\n🎯 Special Model Parsing:');
        
        const { data: models, error: modelError } = await supabase
            .from('it_discraft_order_sheet_lines')
            .select('mold_name, stamp_name, raw_model, excel_row')
            .in('raw_model', ['PS Buzzz SS', 'RW Nuke', 'PP Sol', 'Hard Luna', 'Soft Fierce'])
            .order('excel_row');
        
        if (modelError) throw modelError;
        
        models.forEach(item => {
            console.log(`  Row ${item.excel_row}: "${item.raw_model}" → Mold: "${item.mold_name}", Stamp: "${item.stamp_name}"`);
        });
        
        // Summary stats
        console.log('\n📊 Summary Statistics:');
        
        const { data: stats, error: statsError } = await supabase
            .from('it_discraft_order_sheet_lines')
            .select('plastic_name, is_currently_available')
            .order('plastic_name');
        
        if (statsError) throw statsError;
        
        const plasticStats = {};
        stats.forEach(item => {
            if (!plasticStats[item.plastic_name]) {
                plasticStats[item.plastic_name] = { total: 0, available: 0 };
            }
            plasticStats[item.plastic_name].total++;
            if (item.is_currently_available) {
                plasticStats[item.plastic_name].available++;
            }
        });
        
        Object.entries(plasticStats).forEach(([plastic, counts]) => {
            console.log(`  ${plastic}: ${counts.available}/${counts.total} available`);
        });
        
    } catch (error) {
        console.error('❌ Error verifying parsing:', error);
    }
}

verifySpecificParsing();
