import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

dotenv.config();

const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_KEY;
const supabase = createClient(supabaseUrl, supabaseKey);

async function testUnmatchedOslsFix() {
    try {
        console.log('🧪 Testing fixed unmatched OSLs logic...\n');
        
        // Step 1: Get matched MPS IDs (the correct way)
        console.log('1. Getting matched MPS IDs...');
        const { data: matchedMpsIds, error: matchedError } = await supabase
            .from('it_discraft_order_sheet_lines')
            .select('calculated_mps_id')
            .not('calculated_mps_id', 'is', null);
        
        if (matchedError) {
            console.error('Error getting matched MPS IDs:', matchedError);
            return;
        }
        
        const matchedIds = matchedMpsIds.map(row => row.calculated_mps_id);
        console.log(`✅ Found ${matchedIds.length} matched MPS IDs`);
        console.log(`   Sample IDs: ${matchedIds.slice(0, 5).join(', ')}...`);
        
        // Step 2: Get unmatched OSLs (the correct way)
        console.log('\n2. Getting unmatched OSLs...');
        const { data: unmatchedOsls, error: oslsError } = await supabase
            .from('t_order_sheet_lines')
            .select(`id, mps_id, min_weight, max_weight, color_id, t_mps!inner(t_plastics!inner(plastic), t_molds!inner(mold), t_stamps!inner(stamp))`)
            .eq('vendor_id', 9)
            .not('mps_id', 'in', `(${matchedIds.length > 0 ? matchedIds.join(',') : '0'})`)
            .limit(10);
        
        if (oslsError) {
            console.error('Error getting unmatched OSLs:', oslsError);
            return;
        }
        
        console.log(`✅ Found ${unmatchedOsls.length} unmatched OSLs`);
        
        // Step 3: Show sample results
        console.log('\n3. Sample unmatched OSLs:');
        unmatchedOsls.forEach((osl, index) => {
            const mps = osl.t_mps;
            console.log(`   ${index + 1}. OSL ${osl.id}: "${mps.t_plastics.plastic}" | "${mps.t_molds.mold}" | "${mps.t_stamps.stamp}"`);
        });
        
        // Step 4: Get total count
        console.log('\n4. Getting total count...');
        const { count: totalCount, error: countError } = await supabase
            .from('t_order_sheet_lines')
            .select('*', { count: 'exact', head: true })
            .eq('vendor_id', 9)
            .not('mps_id', 'in', `(${matchedIds.length > 0 ? matchedIds.join(',') : '0'})`);
        
        if (countError) {
            console.error('Error getting total count:', countError);
            return;
        }
        
        console.log(`✅ Total unmatched OSLs: ${totalCount}`);
        
        console.log('\n🎉 Fixed unmatched OSLs logic test completed successfully!');
        console.log('✅ No SQL syntax errors');
        console.log('✅ Proper array handling for IN clause');
        console.log('✅ Ready to replace the broken endpoints');
        
        return {
            success: true,
            matchedMpsIds: matchedIds.length,
            unmatchedOsls: unmatchedOsls.length,
            totalUnmatchedOsls: totalCount
        };
        
    } catch (error) {
        console.error('❌ Test error:', error);
        return { success: false, error: error.message };
    }
}

testUnmatchedOslsFix().then(result => {
    console.log('\n🎯 Test result:', result);
});
