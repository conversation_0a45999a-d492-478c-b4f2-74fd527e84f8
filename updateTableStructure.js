import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

dotenv.config();

const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_KEY;
const supabase = createClient(supabaseUrl, supabaseKey);

async function updateTableStructure() {
    try {
        console.log('🔄 Updating table structure for robust mapping...');
        
        // Add new columns for robust mapping
        const alterQueries = [
            'ALTER TABLE public.it_discraft_order_sheet_lines ADD COLUMN IF NOT EXISTS excel_mapping_key TEXT',
            'ALTER TABLE public.it_discraft_order_sheet_lines ADD COLUMN IF NOT EXISTS excel_row_hint INTEGER',
            'ALTER TABLE public.it_discraft_order_sheet_lines ADD COLUMN IF NOT EXISTS raw_weight_range TEXT',
            'ALTER TABLE public.it_discraft_order_sheet_lines ADD COLUMN IF NOT EXISTS import_file_hash TEXT',
            
            // Rename existing excel_row to excel_row_hint if it exists
            'ALTER TABLE public.it_discraft_order_sheet_lines RENAME COLUMN excel_row TO excel_row_hint',
            
            // Create indexes
            'CREATE INDEX IF NOT EXISTS idx_discraft_mapping_key ON public.it_discraft_order_sheet_lines(excel_mapping_key)',
            'CREATE INDEX IF NOT EXISTS idx_discraft_file_hash ON public.it_discraft_order_sheet_lines(import_file_hash)'
        ];
        
        for (const query of alterQueries) {
            try {
                console.log(`Executing: ${query}`);
                const { error } = await supabase.rpc('exec_sql', { sql_query: query });
                if (error && !error.message.includes('already exists') && !error.message.includes('does not exist')) {
                    console.warn(`Warning: ${error.message}`);
                } else {
                    console.log('✅ Success');
                }
            } catch (err) {
                console.warn(`Warning: ${err.message}`);
            }
        }
        
        console.log('✅ Table structure updated successfully');
        
    } catch (error) {
        console.error('❌ Error updating table structure:', error);
    }
}

updateTableStructure();
