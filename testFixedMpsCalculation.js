import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

dotenv.config();

const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_KEY;
const supabase = createClient(supabaseUrl, supabaseKey);

async function testFixedMpsCalculation() {
    try {
        console.log('🧪 Testing fixed MPS calculation logic...\n');
        
        // Simulate the fixed endpoint logic
        console.log('1. Getting all products in chunks...');
        let allProducts = [];
        let offset = 0;
        const chunkSize = 1000;
        
        while (true) {
            const { data: productChunk, error: chunkError } = await supabase
                .from('it_discraft_order_sheet_lines')
                .select('id')
                .range(offset, offset + chunkSize - 1);
            
            if (chunkError) {
                console.error('Error getting products chunk:', chunkError);
                return;
            }
            
            if (!productChunk || productChunk.length === 0) break;
            
            allProducts = allProducts.concat(productChunk);
            console.log(`   Retrieved chunk: ${productChunk.length} products (total: ${allProducts.length})`);
            
            if (productChunk.length < chunkSize) break;
            offset += chunkSize;
        }
        
        console.log(`✅ Successfully retrieved all ${allProducts.length} products`);
        
        // Test getting products with details in chunks
        console.log('\n2. Getting product details in chunks...');
        let allDiscraftProducts = [];
        offset = 0;
        
        while (true) {
            const { data: productChunk, error: discraftError } = await supabase
                .from('it_discraft_order_sheet_lines')
                .select('id, plastic_name, mold_name, stamp_name')
                .range(offset, offset + chunkSize - 1);
            
            if (discraftError) {
                console.error('Error getting Discraft products:', discraftError);
                return;
            }
            
            if (!productChunk || productChunk.length === 0) break;
            
            allDiscraftProducts = allDiscraftProducts.concat(productChunk);
            
            if (productChunk.length < chunkSize) break;
            offset += chunkSize;
        }
        
        console.log(`✅ Successfully retrieved all ${allDiscraftProducts.length} product details`);
        
        // Test getting MPS records
        console.log('\n3. Getting MPS records...');
        const { data: mpsRecords, error: mpsError } = await supabase
            .from('t_mps')
            .select(`id, t_plastics!inner(plastic, brand_id), t_molds!inner(mold, brand_id), t_stamps!inner(stamp)`)
            .eq('active', true)
            .eq('t_plastics.brand_id', 6)
            .eq('t_molds.brand_id', 6);
        
        if (mpsError) {
            console.error('Error getting MPS records:', mpsError);
            return;
        }
        
        console.log(`✅ Successfully retrieved ${mpsRecords.length} MPS records`);
        
        // Test matching logic
        console.log('\n4. Testing matching logic...');
        const mpsMap = new Map();
        mpsRecords.forEach(mps => {
            const key = `${mps.t_plastics.plastic.trim()}|${mps.t_molds.mold.trim()}|${mps.t_stamps.stamp.trim()}`;
            mpsMap.set(key, mps.id);
        });
        
        let matchedCount = 0;
        let sampleMatches = [];
        
        for (const product of allDiscraftProducts.slice(0, 100)) { // Test first 100
            const key = `${product.plastic_name.trim()}|${product.mold_name.trim()}|${product.stamp_name.trim()}`;
            const mpsId = mpsMap.get(key);
            
            if (mpsId) {
                matchedCount++;
                if (sampleMatches.length < 5) {
                    sampleMatches.push({
                        product: `${product.plastic_name} | ${product.mold_name} | ${product.stamp_name}`,
                        mpsId: mpsId
                    });
                }
            }
        }
        
        console.log(`✅ Sample matching test: ${matchedCount}/100 products matched`);
        console.log('Sample matches:');
        sampleMatches.forEach(match => {
            console.log(`   "${match.product}" → MPS ${match.mpsId}`);
        });
        
        // Estimate full success rate
        const estimatedSuccessRate = ((matchedCount / 100) * 100).toFixed(1);
        console.log(`📊 Estimated success rate: ${estimatedSuccessRate}%`);
        
        console.log('\n🎉 Fixed MPS calculation logic test completed successfully!');
        console.log('✅ All chunking works correctly');
        console.log('✅ No Supabase 1000-record limits hit');
        console.log('✅ Matching logic works properly');
        console.log('✅ Ready to replace the broken endpoint');
        
        return {
            success: true,
            totalProducts: allProducts.length,
            totalProductDetails: allDiscraftProducts.length,
            totalMpsRecords: mpsRecords.length,
            sampleMatchRate: estimatedSuccessRate
        };
        
    } catch (error) {
        console.error('❌ Test error:', error);
        return { success: false, error: error.message };
    }
}

testFixedMpsCalculation().then(result => {
    console.log('\n🎯 Test result:', result);
});
