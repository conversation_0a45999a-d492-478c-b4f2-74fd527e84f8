import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

// Initialize Supabase client
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_KEY;
const supabase = createClient(supabaseUrl, supabaseKey);

async function getAccurateCounts() {
    try {
        console.log('Getting accurate vendor connection counts (avoiding 1000 limit)...\n');
        
        // Get unique vendor IDs first
        const { data: vendorData, error: vendorError } = await supabase
            .from('t_order_sheet_lines')
            .select('vendor_id')
            .not('vendor_id', 'is', null);
        
        if (vendorError) {
            throw new Error(`Error fetching vendor data: ${vendorError.message}`);
        }
        
        const uniqueVendors = [...new Set(vendorData.map(r => r.vendor_id))];
        console.log(`Found ${uniqueVendors.length} unique vendors: ${uniqueVendors.join(', ')}\n`);
        
        console.log('=== ACCURATE VENDOR CONNECTION COUNTS ===');
        
        for (const vendorId of uniqueVendors) {
            // Total count for this vendor
            const { count: totalCount, error: totalError } = await supabase
                .from('t_order_sheet_lines')
                .select('*', { count: 'exact', head: true })
                .eq('vendor_id', vendorId);
            
            // Count with connections
            const { count: withConnectionCount, error: withError } = await supabase
                .from('t_order_sheet_lines')
                .select('*', { count: 'exact', head: true })
                .eq('vendor_id', vendorId)
                .not('vendor_internal_id', 'is', null);
            
            if (totalError || withError) {
                console.log(`❌ Error getting counts for vendor ${vendorId}`);
                continue;
            }
            
            const withoutConnection = totalCount - withConnectionCount;
            
            console.log(`Vendor ID ${vendorId}:`);
            console.log(`  Total OSLs: ${totalCount}`);
            console.log(`  With vendor_internal_id: ${withConnectionCount}`);
            console.log(`  Without vendor_internal_id: ${withoutConnection}`);
            console.log('');
        }
        
        // Get count of recently cleared records (all vendors)
        const { count: recentlyClearedCount, error: recentError } = await supabase
            .from('t_order_sheet_lines')
            .select('*', { count: 'exact', head: true })
            .gte('updated_at', '2025-06-05T15:30:00')
            .is('vendor_internal_id', null);
        
        if (!recentError) {
            console.log(`=== RECENTLY CLEARED CONNECTIONS ===`);
            console.log(`Total records with NULL vendor_internal_id updated since 15:30: ${recentlyClearedCount}`);
        }
        
        // Get count of recently cleared Innova records specifically
        const { count: innovaClearedCount, error: innovaError } = await supabase
            .from('t_order_sheet_lines')
            .select('*', { count: 'exact', head: true })
            .eq('vendor_id', 2)
            .gte('updated_at', '2025-06-05T15:30:00')
            .is('vendor_internal_id', null);
        
        if (!innovaError) {
            console.log(`Innova records (vendor_id=2) cleared since 15:30: ${innovaClearedCount}`);
        }
        
        // Check if any non-Innova vendors were affected
        const nonInnovaVendors = uniqueVendors.filter(v => v !== 2);
        let nonInnovaAffected = 0;
        
        for (const vendorId of nonInnovaVendors) {
            const { count: clearedCount, error } = await supabase
                .from('t_order_sheet_lines')
                .select('*', { count: 'exact', head: true })
                .eq('vendor_id', vendorId)
                .gte('updated_at', '2025-06-05T15:30:00')
                .is('vendor_internal_id', null);
            
            if (!error && clearedCount > 0) {
                console.log(`⚠️  Vendor ${vendorId} had ${clearedCount} connections cleared!`);
                nonInnovaAffected += clearedCount;
            }
        }
        
        if (nonInnovaAffected === 0) {
            console.log(`✅ No non-Innova vendors were affected`);
        } else {
            console.log(`❌ ${nonInnovaAffected} non-Innova connections were incorrectly cleared!`);
        }
        
        return {
            success: true,
            totalCleared: recentlyClearedCount,
            innovaCleared: innovaClearedCount,
            nonInnovaAffected: nonInnovaAffected
        };
        
    } catch (error) {
        console.error('Count check failed:', error);
        return {
            success: false,
            error: error.message
        };
    }
}

// Run if executed directly
if (process.argv[1] && process.argv[1].endsWith('getAccurateCounts.js')) {
    console.log('Getting accurate counts...');
    getAccurateCounts()
        .then(result => {
            if (result.success) {
                console.log('\n✅ Count check completed successfully');
                process.exit(0);
            } else {
                console.error('❌ Count check failed:', result.error);
                process.exit(1);
            }
        })
        .catch(error => {
            console.error('Unexpected error:', error);
            process.exit(1);
        });
}

export { getAccurateCounts };
