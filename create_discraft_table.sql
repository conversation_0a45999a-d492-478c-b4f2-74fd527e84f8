-- Drop existing table if it exists
DROP TABLE IF EXISTS public.it_discraft_order_sheet_lines;

-- Create table for Discraft order sheet lines import (matching local order sheet structure)
CREATE TABLE IF NOT EXISTS public.it_discraft_order_sheet_lines (
    id SERIAL PRIMARY KEY,

    -- Core product fields (match your schema exactly)
    mold_name TEXT,
    plastic_name TEXT,
    min_weight INTEGER,
    max_weight INTEGER,
    color_name TEXT,
    stamp_name TEXT,

    vendor_product_code TEXT,
    is_orderable BOOLEAN DEFAULT true,
    is_currently_available BOOLEAN,

    -- Pricing
    cost_price DECIMAL(10,2),

    -- ROBUST Excel mapping (for order write-back)
    excel_mapping_key TEXT, -- Content-based key: "line|model|weight_range"
    excel_row_hint INTEGER, -- Hint for speed, but not relied upon
    excel_column TEXT,

    -- Raw content for validation and rebuilding
    raw_line_type TEXT,
    raw_model TEXT,
    raw_weight_range TEXT,

    -- File tracking for change detection
    import_file_hash TEXT,
    import_batch_id UUID DEFAULT gen_random_uuid(),
    created_at TIMESTAMP DEFAULT NOW(),

    -- Additional data
    vendor_description TEXT,
    raw_status TEXT -- Original status from Excel (9, out, empty, etc.)
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_discraft_mold_name ON public.it_discraft_order_sheet_lines(mold_name);
CREATE INDEX IF NOT EXISTS idx_discraft_plastic_name ON public.it_discraft_order_sheet_lines(plastic_name);
CREATE INDEX IF NOT EXISTS idx_discraft_weight_range ON public.it_discraft_order_sheet_lines(min_weight, max_weight);
CREATE INDEX IF NOT EXISTS idx_discraft_orderable ON public.it_discraft_order_sheet_lines(is_orderable);
CREATE INDEX IF NOT EXISTS idx_discraft_available ON public.it_discraft_order_sheet_lines(is_currently_available);
CREATE INDEX IF NOT EXISTS idx_discraft_mapping_key ON public.it_discraft_order_sheet_lines(excel_mapping_key);
CREATE INDEX IF NOT EXISTS idx_discraft_file_hash ON public.it_discraft_order_sheet_lines(import_file_hash);
CREATE INDEX IF NOT EXISTS idx_discraft_import_batch ON public.it_discraft_order_sheet_lines(import_batch_id);
CREATE INDEX IF NOT EXISTS idx_discraft_created_at ON public.it_discraft_order_sheet_lines(created_at);

-- Add updated_at trigger (optional - removed updated_at column from main table)
-- CREATE OR REPLACE FUNCTION update_updated_at_column()
-- RETURNS TRIGGER AS $$
-- BEGIN
--     NEW.updated_at = NOW();
--     RETURN NEW;
-- END;
-- $$ language 'plpgsql';

-- Add comments for documentation
COMMENT ON TABLE public.it_discraft_order_sheet_lines IS 'Imported data from Discraft order form Excel files';
COMMENT ON COLUMN public.it_discraft_order_sheet_lines.mold_name IS 'Disc model name: Buzzz, Force, Luna, etc.';
COMMENT ON COLUMN public.it_discraft_order_sheet_lines.plastic_name IS 'Plastic type: ESP, Z, Big Z, etc.';
COMMENT ON COLUMN public.it_discraft_order_sheet_lines.min_weight IS 'Minimum weight in grams for this product';
COMMENT ON COLUMN public.it_discraft_order_sheet_lines.max_weight IS 'Maximum weight in grams for this product';
COMMENT ON COLUMN public.it_discraft_order_sheet_lines.vendor_product_code IS 'Discraft internal product code';
COMMENT ON COLUMN public.it_discraft_order_sheet_lines.is_orderable IS 'True if product can be ordered (marked with 9 or out)';
COMMENT ON COLUMN public.it_discraft_order_sheet_lines.is_currently_available IS 'True if currently in stock (marked with 9)';
COMMENT ON COLUMN public.it_discraft_order_sheet_lines.excel_mapping_key IS 'Content-based key for robust Excel mapping: line|model|weight_range';
COMMENT ON COLUMN public.it_discraft_order_sheet_lines.excel_row_hint IS 'Row number hint for speed (not relied upon for accuracy)';
COMMENT ON COLUMN public.it_discraft_order_sheet_lines.excel_column IS 'Column letter in Excel file for order write-back';
COMMENT ON COLUMN public.it_discraft_order_sheet_lines.import_file_hash IS 'Hash of source Excel file for change detection';
COMMENT ON COLUMN public.it_discraft_order_sheet_lines.import_batch_id IS 'UUID to track import batches';
