// Debug Valkyrie parsing issue

function parseInnovaDescription(description) {
    if (!description) return null;
    
    // Common Innova plastic types (order matters - longer names first)
    const plasticTypes = [
        'Champion I-Dye', 'Champion Proto Glow', 'Champion Classic Glow', 'Champion', 
        'Star I-Dye', 'Halo Star', 'Star', 
        'DX Proto Glow', 'DX Classic Glow', 'DX', 
        'Pro KC', 'KC Pro', 'R-Pro', 'Pro',
        'Halo Nexus', 'Nexus',
        'Metal Flake Champion', 'GStar', 'G-Star', 'XT'
    ];
    
    let plastic = null;
    let remainingText = description.trim();
    
    // Find plastic type
    for (const plasticType of plasticTypes) {
        if (description.toLowerCase().includes(plasticType.toLowerCase())) {
            plastic = plasticType;
            // Remove plastic from description to get mold
            remainingText = description.replace(new RegExp(plasticType, 'gi'), '').trim();
            break;
        }
    }
    
    // Extract mold name (remove Innova disc type classifications - these are ignored for matching)
    let mold = remainingText
        .replace(/\s+(Putt & Approach|Fairway Distance Driver|Distance Driver|Fairway Driver|Mid-Range|Midrange|Putter|Driver|Approach)(\s+Disc)?$/i, '')
        .replace(/\s+Disc$/i, '') // Remove standalone "Disc" at the end
        .replace(/\s+with\s+.*/i, '') // Remove "with Burst Logo" etc
        .trim();
    
    return {
        plastic: plastic,
        mold: mold,
        original: description
    };
}

// Test the specific Valkyrie case
const testCases = [
    'Champion Classic Glow Valkyrie Fairway Distance Driver',
    'Champion Valkyrie Distance Driver',
    'Star Valkyrie Distance Driver',
    'DX Valkyrie Distance Driver'
];

console.log('=== VALKYRIE PARSING DEBUG ===\n');

testCases.forEach(description => {
    const result = parseInnovaDescription(description);
    console.log(`"${description}"`);
    console.log(`  → Plastic: "${result?.plastic || 'NOT FOUND'}"`);
    console.log(`  → Mold: "${result?.mold || 'NOT FOUND'}"`);
    console.log('');
});

// Step by step for the specific case
console.log('=== STEP BY STEP: "Champion Classic Glow Valkyrie Fairway Distance Driver" ===');
const description = 'Champion Classic Glow Valkyrie Fairway Distance Driver';

console.log(`1. Original: "${description}"`);

// Remove Champion Classic Glow
let step1 = description.replace(new RegExp('Champion Classic Glow', 'gi'), '').trim();
console.log(`2. Remove "Champion Classic Glow": "${step1}"`);

// Try to remove suffixes
let step2 = step1.replace(/\s+(Putt & Approach|Distance Driver|Fairway Driver|Mid-Range|Midrange|Putter|Driver|Approach)(\s+Disc)?$/i, '').trim();
console.log(`3. Remove suffix: "${step2}"`);

// The issue might be "Fairway Distance Driver" - let's try removing "Fairway" first
let step3 = step1.replace(/\s+Fairway\s+Distance\s+Driver$/i, '').trim();
console.log(`4. Remove "Fairway Distance Driver": "${step3}"`);

// Or maybe we need to handle compound suffixes
let step4 = step1.replace(/\s+(Fairway\s+)?(Distance\s+)?Driver$/i, '').trim();
console.log(`5. Remove compound driver suffix: "${step4}"`);
