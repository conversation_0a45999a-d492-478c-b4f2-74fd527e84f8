import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

dotenv.config();

const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_KEY;
const supabase = createClient(supabaseUrl, supabaseKey);

async function createProcedures() {
    try {
        console.log('🔄 Creating Discraft stored procedures...');
        
        // Create MPS calculation procedure
        const mpsCalcProcedure = `
CREATE OR REPLACE FUNCTION calculate_discraft_mps_ids()
RETURNS TABLE(
    total_products INTEGER,
    calculated_count INTEGER,
    failed_count INTEGER,
    success_rate NUMERIC
) AS $$
DECLARE
    total_count INTEGER;
    success_count INTEGER;
    fail_count INTEGER;
BEGIN
    -- Get total count
    SELECT COUNT(*) INTO total_count FROM it_discraft_order_sheet_lines;
    
    -- Reset all calculated_mps_id to NULL first
    UPDATE it_discraft_order_sheet_lines SET calculated_mps_id = NULL;
    
    -- Calculate MPS_ID by exact string matching
    UPDATE it_discraft_order_sheet_lines disc
    SET calculated_mps_id = mps.id
    FROM t_mps mps
    JOIN t_plastics p ON mps.plastic_id = p.id
    JOIN t_molds m ON mps.mold_id = m.id  
    JOIN t_stamps s ON mps.stamp_id = s.id
    WHERE TRIM(p.plastic) = TRIM(disc.plastic_name)
      AND TRIM(m.mold) = TRIM(disc.mold_name)
      AND TRIM(s.stamp) = TRIM(disc.stamp_name)
      AND mps.active = true;
    
    -- Get success/failure counts
    SELECT 
        COUNT(*) FILTER (WHERE calculated_mps_id IS NOT NULL),
        COUNT(*) FILTER (WHERE calculated_mps_id IS NULL)
    INTO success_count, fail_count
    FROM it_discraft_order_sheet_lines;
    
    -- Return results
    RETURN QUERY SELECT 
        total_count,
        success_count,
        fail_count,
        ROUND((success_count * 100.0) / NULLIF(total_count, 0), 2);
END;
$$ LANGUAGE plpgsql;`;

        const { error: mpsError } = await supabase.rpc('exec_sql', { sql_query: mpsCalcProcedure });
        
        if (mpsError) {
            console.error('❌ Error creating MPS calculation procedure:', mpsError);
            return;
        }
        
        console.log('✅ MPS calculation procedure created');
        
        // Create OSL matching procedure
        const oslMatchProcedure = `
CREATE OR REPLACE FUNCTION find_discraft_osl_matches()
RETURNS TABLE(
    osl_id INTEGER,
    mps_id INTEGER,
    osl_min_weight SMALLINT,
    osl_max_weight SMALLINT,
    color_id SMALLINT,
    discraft_id INTEGER,
    calculated_mps_id INTEGER,
    disc_min_weight SMALLINT,
    disc_max_weight SMALLINT,
    disc_color TEXT,
    excel_mapping_key TEXT,
    is_currently_available BOOLEAN,
    weight_status TEXT,
    color_status TEXT
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        osl.id as osl_id,
        osl.mps_id,
        osl.min_weight as osl_min_weight,
        osl.max_weight as osl_max_weight,
        osl.color_id,
        disc.id as discraft_id,
        disc.calculated_mps_id,
        disc.min_weight as disc_min_weight,
        disc.max_weight as disc_max_weight,
        disc.color_name as disc_color,
        disc.excel_mapping_key,
        disc.is_currently_available,
        -- Weight compatibility check
        CASE 
            WHEN osl.min_weight <= disc.max_weight AND osl.max_weight >= disc.min_weight 
            THEN 'COMPATIBLE'
            ELSE 'INCOMPATIBLE'
        END as weight_status,
        -- Color compatibility check
        CASE 
            WHEN osl.color_id = 23 THEN 'ANY_COLOR_OK'
            WHEN disc.color_name = 'Varies' THEN 'VARIES_OK'
            WHEN EXISTS (
                SELECT 1 FROM t_colors c 
                WHERE c.id = osl.color_id 
                AND TRIM(c.color) = TRIM(disc.color_name)
            ) THEN 'COLOR_MATCH'
            ELSE 'COLOR_MISMATCH'
        END as color_status
    FROM t_order_sheet_lines osl
    JOIN it_discraft_order_sheet_lines disc ON osl.mps_id = disc.calculated_mps_id
    WHERE osl.vendor_id = 9  -- Discraft
      AND disc.is_orderable = true
      AND disc.calculated_mps_id IS NOT NULL
    ORDER BY osl.id, disc.min_weight;
END;
$$ LANGUAGE plpgsql;`;

        const { error: oslError } = await supabase.rpc('exec_sql', { sql_query: oslMatchProcedure });
        
        if (oslError) {
            console.error('❌ Error creating OSL matching procedure:', oslError);
            return;
        }
        
        console.log('✅ OSL matching procedure created');
        console.log('🎉 All stored procedures created successfully!');
        
    } catch (error) {
        console.error('❌ Error creating stored procedures:', error);
    }
}

createProcedures();
