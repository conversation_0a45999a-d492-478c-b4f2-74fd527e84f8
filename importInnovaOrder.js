import XLSX from 'xlsx';
import path from 'path';
import { fileURLToPath } from 'url';
import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Initialize Supabase client
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_KEY;
const supabase = createClient(supabaseUrl, supabaseKey);

async function importInnovaOrderData() {
    try {
        console.log('Starting Innova order data import...');
        
        // Path to the Excel file
        const excelFilePath = path.join(__dirname, 'data', 'external data', 'innovaorderform.xlsx');
        
        // Read the workbook
        const workbook = XLSX.readFile(excelFilePath);
        
        if (!workbook.SheetNames.includes('Order_Table')) {
            throw new Error('Order_Table sheet not found in Excel file');
        }
        
        const worksheet = workbook.Sheets['Order_Table'];
        
        // Convert to JSON, focusing only on the meaningful columns (A-S)
        const range = XLSX.utils.decode_range(worksheet['!ref']);
        const jsonData = XLSX.utils.sheet_to_json(worksheet, { 
            range: `A1:S${range.e.r + 1}` // Only first 19 columns (A-S)
        });
        
        console.log(`Found ${jsonData.length} rows to import`);
        
        // Truncate existing data first
        console.log('Truncating existing data...');
        const { error: deleteError } = await supabase
            .from('it_innova_order_sheet_lines')
            .delete()
            .neq('id', 0); // Delete all records

        if (deleteError) {
            console.error('Error truncating table:', deleteError);
            throw deleteError;
        }

        console.log('Existing data truncated successfully');

        // Generate a batch ID for this import
        const batchId = crypto.randomUUID();
        console.log(`Import batch ID: ${batchId}`);

        // Transform data to match database schema
        const transformedData = jsonData.map(row => ({
            category: row.Category || null,
            sub_category: row['Sub-Category'] || null,
            description: row.Description || null,
            availability: parseIntOrNull(row.Availability),
            sku: row.SKU || null,
            internal_id: parseIntOrNull(row['Internal ID']),
            parent_id: parseIntOrNull(row['Parent ID']),
            ordered: parseIntOrNull(row.Ordered),
            matrix_type: row['Matrix Type'] || null,
            matrix_option_1: row['Matrix Option 1'] || null,
            matrix_option_2: row['Matrix Option 2'] || null,
            base_price: parseFloatOrNull(row['Base Price']),
            dealer_25: parseFloatOrNull(row['Dealer 25']),
            dealer_50: parseFloatOrNull(row['Dealer 50']),
            dealer_100: parseFloatOrNull(row['Dealer 100']),
            dealer_200: parseFloatOrNull(row['Dealer 200']),
            dealer_500: parseFloatOrNull(row['Dealer 500']),
            current_price: parseFloatOrNull(row['Current Price']),
            total_price: parseFloatOrNull(row['Total Price']),
            import_batch_id: batchId
        }));
        
        // Import in batches of 1000 records
        const batchSize = 1000;
        let totalInserted = 0;
        
        for (let i = 0; i < transformedData.length; i += batchSize) {
            const batch = transformedData.slice(i, i + batchSize);
            
            console.log(`Importing batch ${Math.floor(i / batchSize) + 1}/${Math.ceil(transformedData.length / batchSize)} (${batch.length} records)...`);
            
            const { data, error } = await supabase
                .from('it_innova_order_sheet_lines')
                .insert(batch);
            
            if (error) {
                console.error('Error inserting batch:', error);
                throw error;
            }
            
            totalInserted += batch.length;
            console.log(`Successfully inserted ${totalInserted}/${transformedData.length} records`);
        }
        
        console.log(`Import completed successfully! Total records imported: ${totalInserted}`);
        console.log(`Batch ID: ${batchId}`);
        
        return {
            success: true,
            totalRecords: totalInserted,
            batchId: batchId
        };
        
    } catch (error) {
        console.error('Import failed:', error);
        return {
            success: false,
            error: error.message
        };
    }
}

// Helper functions
function parseIntOrNull(value) {
    if (value === null || value === undefined || value === '') return null;
    const parsed = parseInt(value);
    return isNaN(parsed) ? null : parsed;
}

function parseFloatOrNull(value) {
    if (value === null || value === undefined || value === '') return null;
    const parsed = parseFloat(value);
    return isNaN(parsed) ? null : parsed;
}

// Run the import if this file is executed directly
// Check if this script is being run directly
const isMainModule = process.argv[1] && process.argv[1].endsWith('importInnovaOrder.js');

if (isMainModule) {
    console.log('Starting Innova import script...');
    importInnovaOrderData()
        .then(result => {
            if (result.success) {
                console.log('Import completed successfully');
                console.log(`Total records: ${result.totalRecords}`);
                console.log(`Batch ID: ${result.batchId}`);
                process.exit(0);
            } else {
                console.error('Import failed:', result.error);
                process.exit(1);
            }
        })
        .catch(error => {
            console.error('Unexpected error:', error);
            process.exit(1);
        });
}

export { importInnovaOrderData };
