import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

dotenv.config();

const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_KEY;
const supabase = createClient(supabaseUrl, supabaseKey);

async function checkDiscraftInDatabase() {
    try {
        console.log('🔍 Checking for Discraft products in your database...\n');
        
        // Check for Discraft plastics
        const discraftPlastics = ['Tour', 'Elite X', 'ESP', 'Titanium', 'Elite Z', 'Elite Z Lite', 'Elite Z Glow', 'Jawbreaker'];
        
        console.log('🎯 Checking Discraft Plastics:');
        for (const plastic of discraftPlastics) {
            const { data, error } = await supabase
                .from('t_plastics')
                .select('id, plastic')
                .ilike('plastic', `%${plastic}%`);
            
            if (!error && data && data.length > 0) {
                console.log(`✅ Found "${plastic}": ${data.map(p => `"${p.plastic}"`).join(', ')}`);
            } else {
                console.log(`❌ Missing "${plastic}"`);
            }
        }
        
        // Check for Discraft molds
        const discraftMolds = ['Buzzz', 'Raptor', 'Nuke', 'Cicada', 'BuzzzSS', 'Force', 'Luna', 'Zone', 'Heat', 'Thrasher'];
        
        console.log('\n🥏 Checking Discraft Molds:');
        for (const mold of discraftMolds) {
            const { data, error } = await supabase
                .from('t_molds')
                .select('id, mold')
                .ilike('mold', `%${mold}%`);
            
            if (!error && data && data.length > 0) {
                console.log(`✅ Found "${mold}": ${data.map(m => `"${m.mold}"`).join(', ')}`);
            } else {
                console.log(`❌ Missing "${mold}"`);
            }
        }
        
        // Check for "Stock" stamp
        console.log('\n🏷️ Checking for "Stock" Stamp:');
        const { data: stockStamps, error: stockError } = await supabase
            .from('t_stamps')
            .select('id, stamp')
            .ilike('stamp', '%stock%');
        
        if (!stockError && stockStamps && stockStamps.length > 0) {
            console.log(`✅ Found Stock stamps: ${stockStamps.map(s => `"${s.stamp}"`).join(', ')}`);
        } else {
            console.log(`❌ Missing "Stock" stamp`);
        }
        
        // Check if there are ANY Discraft MPS records
        console.log('\n📊 Checking for Existing Discraft MPS Records:');
        
        // Look for MPS records that might be Discraft
        const { data: mpsWithNames, error: mpsError } = await supabase
            .from('t_mps')
            .select(`
                id,
                t_plastics!inner(plastic),
                t_molds!inner(mold),
                t_stamps!inner(stamp)
            `)
            .eq('active', true)
            .limit(100);
        
        if (!mpsError && mpsWithNames) {
            // Look for potential Discraft records
            const potentialDiscraft = mpsWithNames.filter(mps => 
                discraftPlastics.some(plastic => 
                    mps.t_plastics.plastic.toLowerCase().includes(plastic.toLowerCase())
                ) ||
                discraftMolds.some(mold => 
                    mps.t_molds.mold.toLowerCase().includes(mold.toLowerCase())
                )
            );
            
            if (potentialDiscraft.length > 0) {
                console.log(`✅ Found ${potentialDiscraft.length} potential Discraft MPS records:`);
                potentialDiscraft.slice(0, 10).forEach((mps, index) => {
                    console.log(`${index + 1}. MPS ${mps.id}: "${mps.t_plastics.plastic}" | "${mps.t_molds.mold}" | "${mps.t_stamps.stamp}"`);
                });
            } else {
                console.log('❌ No obvious Discraft MPS records found');
            }
        }
        
        console.log('\n💡 Next Steps:');
        console.log('1. If Discraft products are missing, you need to create them in your database');
        console.log('2. If they exist with different names, update the parsing logic');
        console.log('3. Consider importing Discraft product catalog into your MPS system');
        
    } catch (error) {
        console.error('❌ Error checking Discraft in database:', error);
    }
}

checkDiscraftInDatabase();
