# Variant vs Product Deletion Correction

## Issue Identified

The initial implementation incorrectly deleted **entire products** from Shopify when OSLs were updated. This was problematic because multiple OSLs can share the same Shopify product.

## Problem Scenario

```
Shopify Product: "Innova Champion Destroyer"
├── Variant 1: OS16890 (Blue, 170-175g)    ← OSL 16890
├── Variant 2: OS16891 (Red, 170-175g)     ← OSL 16891  
├── Variant 3: OS16892 (Blue, 165-169g)    ← OSL 16892
└── Variant 4: OS16893 (Blue, 176-180g)    ← OSL 16893

❌ OLD BEHAVIOR: Update OSL 16890
→ Delete entire "Innova Champion Destroyer" product
→ ALL variants (OS16890, OS16891, OS16892, OS16893) are lost
→ Other OSLs lose their Shopify presence unintentionally

✅ NEW BEHAVIOR: Update OSL 16890  
→ Delete only variant OS16890
→ Keep variants OS16891, OS16892, OS16893
→ Product remains with 3 variants
→ Other OSLs are unaffected
```

## Solution Implemented

### 1. Created New Variant Deletion Task

**File**: `processDeleteVariantFromShopifyTask.js`

- **Task Type**: `delete_variant_from_shopify`
- **Method**: Uses Shopify REST API to delete specific variants
- **Endpoint**: `DELETE /admin/api/2024-01/products/{product_id}/variants/{variant_id}.json`
- **Safety**: Only deletes the specific variant, preserves other variants

### 2. Updated OSL Processing

**File**: `processOslUpdatedTask.js`

- **Changed**: Now enqueues `delete_variant_from_shopify` instead of `delete_sku_from_shopify`
- **Import**: Uses `enqueueDeleteVariantFromShopifyTask` function
- **Messaging**: Updated all messages to reflect "variant" instead of "product"

### 3. Updated Task Queue Worker

**File**: `taskQueueWorker.js`

- **Added**: Handler for `delete_variant_from_shopify` task type
- **Import**: Added `processDeleteVariantFromShopifyTask` import

## Technical Details

### Variant Deletion Process

1. **Find Variant**: Use GraphQL to find variant by SKU
2. **Check Product**: Determine how many variants the product has
3. **Delete Variant**: Use REST API to delete specific variant
4. **Log Results**: Record whether product was preserved or deleted

### API Usage

```javascript
// GraphQL to find variant
query getVariantBySku($query: String!) {
  productVariants(first: 1, query: $query) {
    edges {
      node {
        id
        sku
        product {
          id
          title
          variants(first: 10) {
            edges {
              node { id, sku }
            }
          }
        }
      }
    }
  }
}

// REST API to delete variant
DELETE /admin/api/2024-01/products/{product_id}/variants/{variant_id}.json
```

### Edge Cases Handled

1. **Last Variant**: If deleting the only variant, the product is deleted as a side effect
2. **Variant Not Found**: Gracefully handles cases where SKU doesn't exist
3. **Multiple Variants**: Preserves other variants when deleting one

## Files Created/Modified

### New Files
- `processDeleteVariantFromShopifyTask.js` - Variant deletion processor
- `testDeleteVariantFromShopify.js` - Test script for variant deletion
- `VARIANT_VS_PRODUCT_DELETION_CORRECTION.md` - This documentation

### Modified Files
- `processOslUpdatedTask.js` - Updated to use variant deletion
- `taskQueueWorker.js` - Added variant deletion task handler
- `OSL_UPDATED_SHOPIFY_DELETION_README.md` - Updated documentation

### Preserved Files
- `processDeleteSkuFromShopifyTask.js` - Still available for full product deletion when needed
- `enqueueDeleteSkuFromShopifyTask.js` - Still available for product deletion scenarios

## Benefits of Correction

1. **Data Safety**: No accidental deletion of unrelated variants
2. **Precision**: Only removes the specific OSL variant that was updated
3. **Efficiency**: Other OSLs don't need to be re-uploaded
4. **Consistency**: Maintains proper product structure in Shopify
5. **Flexibility**: Both variant and product deletion are available as needed

## When to Use Each Approach

### Use Variant Deletion (`delete_variant_from_shopify`)
- ✅ OSL updates (weight range, color changes)
- ✅ Individual OSL discontinuation
- ✅ When product has multiple variants
- ✅ Precision deletion scenarios

### Use Product Deletion (`delete_sku_from_shopify`)  
- ✅ Entire product line discontinuation
- ✅ MPS (mold/plastic/stamp) discontinuation
- ✅ When you want to remove all variants
- ✅ Single-variant products

## Testing

### Test Variant Deletion
```bash
node testDeleteVariantFromShopify.js
```

### Test OSL Updated Workflow
```bash
node testOslUpdatedShopifyDeletion.js
```

### View Structure Examples
```bash
node testDeleteVariantFromShopify.js structure
```

## Monitoring

### Check Variant Deletion Tasks
```sql
SELECT * FROM t_task_queue 
WHERE task_type = 'delete_variant_from_shopify' 
ORDER BY created_at DESC;
```

### Check OSL Updates with Variant Deletion
```sql
SELECT 
  id, status, 
  result->>'shopify_variant_deletion_enqueued' as variant_deletion,
  result->>'message' as message
FROM t_task_queue 
WHERE task_type = 'osl_updated' 
  AND result->>'shopify_variant_deletion_enqueued' = 'true'
ORDER BY created_at DESC;
```

## Migration Notes

- **Existing Tasks**: Old `delete_sku_from_shopify` tasks will continue to work
- **New OSL Updates**: Will automatically use variant deletion
- **Manual Usage**: Can still use product deletion when appropriate
- **No Data Loss**: This change only affects future OSL updates

## Summary

This correction ensures that OSL updates only affect their specific Shopify variants, not entire products. This prevents accidental deletion of other OSL variants and maintains proper product structure in your Shopify store.
