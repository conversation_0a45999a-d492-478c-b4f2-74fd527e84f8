import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_KEY;
const supabase = createClient(supabaseUrl, supabaseKey);

async function verifyImport() {
    try {
        console.log('🔍 Verifying Discraft import...\n');
        
        // Get total count
        const { count: totalCount, error: countError } = await supabase
            .from('it_discraft_order_sheet_lines')
            .select('*', { count: 'exact', head: true });
        
        if (countError) throw countError;
        
        console.log(`📊 Total orderable products imported: ${totalCount}`);
        
        // Get sample of available products
        const { data: availableNow, error: availableError } = await supabase
            .from('it_discraft_order_sheet_lines')
            .select('line_type, model, weight_150_available, weight_160_166_available, weight_167_169_available, weight_170_172_available, weight_173_174_available, weight_175_176_available, weight_177_plus_available')
            .or('weight_150_available.eq.true,weight_160_166_available.eq.true,weight_167_169_available.eq.true,weight_170_172_available.eq.true,weight_173_174_available.eq.true,weight_175_176_available.eq.true,weight_177_plus_available.eq.true')
            .limit(10);
        
        if (availableError) throw availableError;
        
        console.log('\n✅ Sample of products available now (marked with "9"):');
        availableNow.forEach((product, index) => {
            const availableWeights = [];
            if (product.weight_150_available) availableWeights.push('150g');
            if (product.weight_160_166_available) availableWeights.push('160-166g');
            if (product.weight_167_169_available) availableWeights.push('167-169g');
            if (product.weight_170_172_available) availableWeights.push('170-172g');
            if (product.weight_173_174_available) availableWeights.push('173-174g');
            if (product.weight_175_176_available) availableWeights.push('175-176g');
            if (product.weight_177_plus_available) availableWeights.push('177g+');
            
            console.log(`  ${index + 1}. ${product.line_type} ${product.model} - Available: ${availableWeights.join(', ')}`);
        });
        
        // Get sample of out of stock but orderable products
        const { data: outOfStock, error: outError } = await supabase
            .from('it_discraft_order_sheet_lines')
            .select('line_type, model, weight_150_status, weight_160_166_status, weight_167_169_status, weight_170_172_status, weight_173_174_status, weight_175_176_status, weight_177_plus_status')
            .eq('weight_150_available', false)
            .eq('weight_160_166_available', false)
            .eq('weight_167_169_available', false)
            .eq('weight_170_172_available', false)
            .eq('weight_173_174_available', false)
            .eq('weight_175_176_available', false)
            .eq('weight_177_plus_available', false)
            .limit(5);
        
        if (outError) throw outError;
        
        console.log('\n⏳ Sample of products orderable but currently out of stock:');
        outOfStock.forEach((product, index) => {
            const outWeights = [];
            if (product.weight_150_status === 'out') outWeights.push('150g');
            if (product.weight_160_166_status === 'out') outWeights.push('160-166g');
            if (product.weight_167_169_status === 'out') outWeights.push('167-169g');
            if (product.weight_170_172_status === 'out') outWeights.push('170-172g');
            if (product.weight_173_174_status === 'out') outWeights.push('173-174g');
            if (product.weight_175_176_status === 'out') outWeights.push('175-176g');
            if (product.weight_177_plus_status === 'out') outWeights.push('177g+');
            
            console.log(`  ${index + 1}. ${product.line_type} ${product.model} - Out: ${outWeights.join(', ')}`);
        });
        
        // Get line type summary
        const { data: lineTypeSummary, error: summaryError } = await supabase
            .from('it_discraft_order_sheet_lines')
            .select('line_type')
            .order('line_type');
        
        if (summaryError) throw summaryError;
        
        const lineTypeCounts = {};
        lineTypeSummary.forEach(item => {
            lineTypeCounts[item.line_type] = (lineTypeCounts[item.line_type] || 0) + 1;
        });
        
        console.log('\n📈 Products by line type:');
        Object.entries(lineTypeCounts)
            .sort(([,a], [,b]) => b - a)
            .forEach(([lineType, count]) => {
                console.log(`  ${lineType}: ${count} products`);
            });
        
        console.log('\n✅ Import verification completed!');
        console.log('\n💡 Next steps:');
        console.log('  1. You can now query orderable products with: is_orderable = true');
        console.log('  2. Filter by currently available: weight_XXX_available = true');
        console.log('  3. Filter by specific line types: line_type = "ESP", "Z", etc.');
        console.log('  4. Use this data to match against your inventory and generate orders');
        
    } catch (error) {
        console.error('❌ Error verifying import:', error);
    }
}

// Run verification
verifyImport();
