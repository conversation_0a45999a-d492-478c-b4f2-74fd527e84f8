import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

dotenv.config();

const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_KEY;
const supabase = createClient(supabaseUrl, supabaseKey);

async function debugSpecificMatch() {
    try {
        console.log('🔍 Debugging specific matching issue...\n');
        
        // Get the Discraft products that should match
        console.log('📦 Getting Discraft products (IDs 14848-14854):');
        const { data: discraftProducts, error: discraftError } = await supabase
            .from('it_discraft_order_sheet_lines')
            .select('id, plastic_name, mold_name, stamp_name, calculated_mps_id')
            .gte('id', 14848)
            .lte('id', 14854);

        if (discraftError) {
            console.error('❌ Error getting Discraft products:', discraftError);
            return;
        }

        if (discraftProducts && discraftProducts.length > 0) {
            discraftProducts.forEach(product => {
                console.log(`   ID ${product.id}: "${product.plastic_name}" | "${product.mold_name}" | "${product.stamp_name}" → MPS: ${product.calculated_mps_id || 'NULL'}`);
            });
        } else {
            console.log('   ❌ No Discraft products found in ID range 14848-14854');

            // Let's check what IDs actually exist
            console.log('\n🔍 Checking what Discraft product IDs exist around this range:');
            const { data: nearbyProducts, error: nearbyError } = await supabase
                .from('it_discraft_order_sheet_lines')
                .select('id, plastic_name, mold_name, stamp_name, calculated_mps_id')
                .gte('id', 14840)
                .lte('id', 14860)
                .order('id');

            if (!nearbyError && nearbyProducts) {
                nearbyProducts.forEach(product => {
                    console.log(`   ID ${product.id}: "${product.plastic_name}" | "${product.mold_name}" | "${product.stamp_name}" → MPS: ${product.calculated_mps_id || 'NULL'}`);
                });
            }

            // Also search for products that should match MPS 14849
            console.log('\n🔍 Searching for products that should match "Jawbreaker | Roach | Stock":');
            const { data: matchingProducts, error: matchingError } = await supabase
                .from('it_discraft_order_sheet_lines')
                .select('id, plastic_name, mold_name, stamp_name, calculated_mps_id')
                .eq('plastic_name', 'Jawbreaker')
                .eq('mold_name', 'Roach')
                .eq('stamp_name', 'Stock')
                .limit(10);

            if (!matchingError && matchingProducts) {
                if (matchingProducts.length > 0) {
                    matchingProducts.forEach(product => {
                        console.log(`   ID ${product.id}: "${product.plastic_name}" | "${product.mold_name}" | "${product.stamp_name}" → MPS: ${product.calculated_mps_id || 'NULL'}`);
                    });
                } else {
                    console.log('   ❌ No products found with "Jawbreaker | Roach | Stock"');

                    // Search for similar products
                    console.log('\n🔍 Searching for similar products:');
                    const { data: similarProducts, error: similarError } = await supabase
                        .from('it_discraft_order_sheet_lines')
                        .select('id, plastic_name, mold_name, stamp_name, calculated_mps_id')
                        .or('plastic_name.ilike.%jawbreaker%,mold_name.ilike.%roach%')
                        .limit(10);

                    if (!similarError && similarProducts) {
                        similarProducts.forEach(product => {
                            console.log(`   ID ${product.id}: "${product.plastic_name}" | "${product.mold_name}" | "${product.stamp_name}" → MPS: ${product.calculated_mps_id || 'NULL'}`);
                        });
                    }
                }
            }
        }
        
        // Get the MPS record that should match
        console.log('\n🎯 Getting MPS record (ID 14849):');
        const { data: mpsRecord, error: mpsError } = await supabase
            .from('t_mps')
            .select(`
                id, active,
                t_plastics!inner(plastic, brand_id),
                t_molds!inner(mold, brand_id),
                t_stamps!inner(stamp)
            `)
            .eq('id', 14849);
        
        if (mpsError) {
            console.error('❌ Error getting MPS record:', mpsError);
            return;
        }
        
        if (mpsRecord && mpsRecord.length > 0) {
            const mps = mpsRecord[0];
            console.log(`   MPS ${mps.id}: "${mps.t_plastics.plastic}" | "${mps.t_molds.mold}" | "${mps.t_stamps.stamp}"`);
            console.log(`   Active: ${mps.active}, Plastic Brand: ${mps.t_plastics.brand_id}, Mold Brand: ${mps.t_molds.brand_id}`);
        } else {
            console.log('   ❌ MPS record 14849 not found');
            return;
        }
        
        // Compare the strings character by character
        console.log('\n🔍 Detailed String Comparison:');
        const mps = mpsRecord[0];
        const sampleProduct = discraftProducts[0]; // Use first product as sample
        
        if (sampleProduct) {
            console.log('\n🎯 Plastic Comparison:');
            console.log(`   Vendor: "${sampleProduct.plastic_name}" (length: ${sampleProduct.plastic_name.length})`);
            console.log(`   MPS:    "${mps.t_plastics.plastic}" (length: ${mps.t_plastics.plastic.length})`);
            console.log(`   Trimmed Vendor: "${sampleProduct.plastic_name.trim()}"`);
            console.log(`   Trimmed MPS:    "${mps.t_plastics.plastic.trim()}"`);
            console.log(`   Match: ${sampleProduct.plastic_name.trim() === mps.t_plastics.plastic.trim()}`);
            
            console.log('\n🥏 Mold Comparison:');
            console.log(`   Vendor: "${sampleProduct.mold_name}" (length: ${sampleProduct.mold_name.length})`);
            console.log(`   MPS:    "${mps.t_molds.mold}" (length: ${mps.t_molds.mold.length})`);
            console.log(`   Trimmed Vendor: "${sampleProduct.mold_name.trim()}"`);
            console.log(`   Trimmed MPS:    "${mps.t_molds.mold.trim()}"`);
            console.log(`   Match: ${sampleProduct.mold_name.trim() === mps.t_molds.mold.trim()}`);
            
            console.log('\n🏷️ Stamp Comparison:');
            console.log(`   Vendor: "${sampleProduct.stamp_name}" (length: ${sampleProduct.stamp_name.length})`);
            console.log(`   MPS:    "${mps.t_stamps.stamp}" (length: ${mps.t_stamps.stamp.length})`);
            console.log(`   Trimmed Vendor: "${sampleProduct.stamp_name.trim()}"`);
            console.log(`   Trimmed MPS:    "${mps.t_stamps.stamp.trim()}"`);
            console.log(`   Match: ${sampleProduct.stamp_name.trim() === mps.t_stamps.stamp.trim()}`);
            
            // Check for hidden characters
            console.log('\n🔍 Character Code Analysis:');
            console.log('Plastic character codes:');
            console.log(`   Vendor: [${Array.from(sampleProduct.plastic_name).map(c => c.charCodeAt(0)).join(', ')}]`);
            console.log(`   MPS:    [${Array.from(mps.t_plastics.plastic).map(c => c.charCodeAt(0)).join(', ')}]`);
            
            console.log('Mold character codes:');
            console.log(`   Vendor: [${Array.from(sampleProduct.mold_name).map(c => c.charCodeAt(0)).join(', ')}]`);
            console.log(`   MPS:    [${Array.from(mps.t_molds.mold).map(c => c.charCodeAt(0)).join(', ')}]`);
            
            console.log('Stamp character codes:');
            console.log(`   Vendor: [${Array.from(sampleProduct.stamp_name).map(c => c.charCodeAt(0)).join(', ')}]`);
            console.log(`   MPS:    [${Array.from(mps.t_stamps.stamp).map(c => c.charCodeAt(0)).join(', ')}]`);
            
            // Test the exact matching logic
            console.log('\n🧪 Testing Matching Logic:');
            const vendorKey = `${sampleProduct.plastic_name.trim()}|${sampleProduct.mold_name.trim()}|${sampleProduct.stamp_name.trim()}`;
            const mpsKey = `${mps.t_plastics.plastic.trim()}|${mps.t_molds.mold.trim()}|${mps.t_stamps.stamp.trim()}`;
            
            console.log(`   Vendor Key: "${vendorKey}"`);
            console.log(`   MPS Key:    "${mpsKey}"`);
            console.log(`   Keys Match: ${vendorKey === mpsKey}`);
        }
        
        // Check if MPS is being included in our query
        console.log('\n🔍 Checking if MPS 14849 is included in our Discraft MPS query:');
        const { data: allDiscraftMps, error: allMpsError } = await supabase
            .from('t_mps')
            .select(`
                id,
                t_plastics!inner(plastic, brand_id),
                t_molds!inner(mold, brand_id),
                t_stamps!inner(stamp)
            `)
            .eq('active', true)
            .eq('t_plastics.brand_id', 6)
            .eq('t_molds.brand_id', 6)
            .eq('id', 14849);
        
        if (allMpsError) {
            console.error('❌ Error checking MPS in Discraft query:', allMpsError);
        } else if (allDiscraftMps && allDiscraftMps.length > 0) {
            console.log('✅ MPS 14849 IS included in Discraft MPS query');
        } else {
            console.log('❌ MPS 14849 is NOT included in Discraft MPS query');
            console.log('   This could be because:');
            console.log('   - active = false');
            console.log('   - plastic brand_id ≠ 6');
            console.log('   - mold brand_id ≠ 6');
        }
        
    } catch (error) {
        console.error('❌ Error debugging match:', error);
    }
}

debugSpecificMatch();
