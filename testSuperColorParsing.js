// Test the SuperColor and special cases parsing

function extractSuperColorStamp(model) {
    if (!model) return 'SuperColor';
    
    const modelStr = model.toString().trim();
    
    // Extract stamp from SuperColor Gallery descriptions
    if (modelStr.includes('SuperColor Gallery Buzzz -')) {
        const stampPart = modelStr.replace(/.*SuperColor Gallery Buzzz\s*-\s*/, '').trim();
        
        // Handle special cases
        if (stampPart.includes('Bali')) return 'SuperColor Bali';
        if (stampPart.includes('Bunksy')) return 'SuperColor Bunksy';
        if (stampPart.includes('Demise')) return 'SuperColor Demise';
        if (stampPart.includes('Earth')) return 'SuperColor Earth';
        if (stampPart.includes('Fire')) return 'SuperColor Nebula Fire';
        if (stampPart.includes('Ancient Alien')) return 'Brian Allen Ancient Alien';
        if (stampPart.includes('Astronaut')) return 'SuperColor Astronaut';
        if (stampPart.includes('Owl')) return 'SuperColor Owl';
        if (stampPart.includes('Lichten')) return 'SuperColor Lichten';
        if (stampPart.includes('Moon')) return 'SuperColor Moon';
        
        // Default: use the last word as stamp
        const words = stampPart.split(/\s+/);
        const lastWord = words[words.length - 1];
        return `SuperColor ${lastWord}`;
    }
    
    // Handle FULL FOIL SuperColor
    if (modelStr.includes('FULL FOIL SuperColor')) {
        const stampPart = modelStr.replace(/.*FULL FOIL SuperColor\s+Buzzz\s*-\s*/, '').trim();
        return `FULL FOIL SuperColor ${stampPart}`;
    }
    
    return 'SuperColor';
}

function standardizePlasticName(rawPlastic, rawModel) {
    const plastic = rawPlastic?.toString().trim() || '';
    
    // Handle SuperColor series
    if (plastic === 'SuperColor' || plastic.includes('SuperColor')) {
        return 'ESP SuperColor';
    }
    
    return plastic;
}

function parseMoldAndStamp(rawModel, rawPlastic) {
    const plastic = rawPlastic?.toString().trim() || '';
    
    // Handle SuperColor series
    if (plastic === 'SuperColor' || plastic.includes('SuperColor')) {
        return {
            mold_name: 'Buzzz', // SuperColor is always Buzzz
            stamp_name: extractSuperColorStamp(rawModel)
        };
    }
    
    return { mold_name: 'Unknown', stamp_name: 'Stock' };
}

// Test cases for SuperColor parsing
const superColorTests = [
    {
        plastic: 'SuperColor',
        model: 'SuperColor Gallery Buzzz - Bali',
        expected: 'ESP SuperColor - Buzzz - SuperColor Bali'
    },
    {
        plastic: 'SuperColor',
        model: 'SuperColor Gallery Buzzz - Bunksy',
        expected: 'ESP SuperColor - Buzzz - SuperColor Bunksy'
    },
    {
        plastic: 'SuperColor',
        model: 'SuperColor Gallery Buzzz - Demise',
        expected: 'ESP SuperColor - Buzzz - SuperColor Demise'
    },
    {
        plastic: 'SuperColor',
        model: 'SuperColor Gallery Buzzz - Earth',
        expected: 'ESP SuperColor - Buzzz - SuperColor Earth'
    },
    {
        plastic: 'SuperColor',
        model: 'SuperColor Gallery Buzzz - Fire',
        expected: 'ESP SuperColor - Buzzz - SuperColor Nebula Fire'
    },
    {
        plastic: 'SuperColor',
        model: 'SuperColor Gallery Buzzz - Ancient Alien',
        expected: 'ESP SuperColor - Buzzz - Brian Allen Ancient Alien'
    },
    {
        plastic: 'SuperColor',
        model: 'SuperColor Gallery Buzzz - Astronaut',
        expected: 'ESP SuperColor - Buzzz - SuperColor Astronaut'
    },
    {
        plastic: 'SuperColor',
        model: 'SuperColor Gallery Buzzz - Owl',
        expected: 'ESP SuperColor - Buzzz - SuperColor Owl'
    },
    {
        plastic: 'SuperColor',
        model: 'SuperColor Gallery Buzzz - Lichten',
        expected: 'ESP SuperColor - Buzzz - SuperColor Lichten'
    },
    {
        plastic: 'SuperColor',
        model: 'SuperColor Gallery Buzzz - Moon',
        expected: 'ESP SuperColor - Buzzz - SuperColor Moon'
    }
];

console.log('🧪 Testing SuperColor Parsing Logic\n');

superColorTests.forEach((testCase, index) => {
    const plastic = standardizePlasticName(testCase.plastic, testCase.model);
    const { mold_name, stamp_name } = parseMoldAndStamp(testCase.model, testCase.plastic);
    
    const result = `${plastic} - ${mold_name} - ${stamp_name}`;
    const isCorrect = result === testCase.expected;
    
    console.log(`${index + 1}. ${isCorrect ? '✅' : '❌'} ${testCase.plastic} | ${testCase.model}`);
    console.log(`   Expected: ${testCase.expected}`);
    console.log(`   Got:      ${result}`);
    if (!isCorrect) {
        console.log(`   🔍 Stamp extraction: "${extractSuperColorStamp(testCase.model)}"`);
    }
    console.log('');
});

// Test Assorted weight handling
console.log('🧪 Testing Assorted Weight Logic\n');

function getAssortedWeightRange() {
    return { min: 160, max: 180, name: 'Assorted', letter: 'ASSORTED' };
}

const assortedWeight = getAssortedWeightRange();
console.log('✅ Assorted weight range:');
console.log(`   Min: ${assortedWeight.min}g`);
console.log(`   Max: ${assortedWeight.max}g`);
console.log(`   Name: ${assortedWeight.name}`);
console.log(`   Letter: ${assortedWeight.letter}`);

console.log('\n🎯 Testing complete!');
