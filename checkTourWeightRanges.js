import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

dotenv.config();

const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_KEY;
const supabase = createClient(supabaseUrl, supabaseKey);

async function checkTourWeightRanges() {
    try {
        console.log('🔍 Checking Tour Series weight ranges...\n');
        
        // Get all Tour Series products and their weight ranges
        const { data: tourProducts, error: tourError } = await supabase
            .from('it_discraft_order_sheet_lines')
            .select('id, plastic_name, mold_name, stamp_name, min_weight, max_weight, raw_weight_range')
            .eq('plastic_name', 'Elite Z Swirl')
            .ilike('stamp_name', '%2025 Tour Series%');
        
        if (tourError) {
            console.error('Error getting Tour Series products:', tourError);
            return;
        }
        
        console.log(`Found ${tourProducts.length} Tour Series products`);
        
        // Group by weight ranges
        const weightRanges = {};
        tourProducts.forEach(product => {
            const range = `${product.min_weight}-${product.max_weight}g`;
            if (!weightRanges[range]) {
                weightRanges[range] = [];
            }
            weightRanges[range].push({
                mold: product.mold_name,
                raw_weight: product.raw_weight_range
            });
        });
        
        console.log('\n📊 Current weight ranges for Tour Series:');
        Object.entries(weightRanges).forEach(([range, products]) => {
            console.log(`\n${range}: ${products.length} products`);
            
            // Show unique molds and raw weights
            const uniqueMolds = [...new Set(products.map(p => p.mold))];
            const uniqueRawWeights = [...new Set(products.map(p => p.raw_weight))];
            
            console.log(`   Molds: ${uniqueMolds.join(', ')}`);
            console.log(`   Raw weights: ${uniqueRawWeights.join(', ')}`);
        });
        
        // Check if all should be 160-180g
        const correctRange = tourProducts.filter(p => p.min_weight === 160 && p.max_weight === 180);
        const incorrectRange = tourProducts.filter(p => !(p.min_weight === 160 && p.max_weight === 180));
        
        console.log(`\n🎯 Weight Range Analysis:`);
        console.log(`   ✅ Correct (160-180g): ${correctRange.length} products`);
        console.log(`   ❌ Incorrect (other ranges): ${incorrectRange.length} products`);
        
        if (incorrectRange.length > 0) {
            console.log('\n❌ Products with incorrect weight ranges:');
            incorrectRange.forEach(product => {
                console.log(`   ${product.mold_name}: ${product.min_weight}-${product.max_weight}g (raw: ${product.raw_weight_range})`);
            });
            
            console.log('\n💡 These should all be consolidated to 160-180g since Tour discs are "Assorted" weight');
        } else {
            console.log('\n✅ All Tour Series products have correct 160-180g weight range!');
        }
        
    } catch (error) {
        console.error('❌ Check error:', error);
    }
}

checkTourWeightRanges();
