import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

dotenv.config();

const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_KEY;
const supabase = createClient(supabaseUrl, supabaseKey);

async function verifyTourSeriesMatched() {
    try {
        console.log('🔍 Verifying Tour Series products are now matched...\n');
        
        // Check Tour Series products
        const { data: tourSeriesProducts, error: tourError } = await supabase
            .from('it_discraft_order_sheet_lines')
            .select('id, plastic_name, mold_name, stamp_name, calculated_mps_id')
            .eq('plastic_name', 'Elite Z Swirl')
            .ilike('stamp_name', '%2025 Tour Series%')
            .limit(20);
        
        if (tourError) {
            console.error('Error getting Tour Series products:', tourError);
            return;
        }
        
        console.log(`Found ${tourSeriesProducts.length} Tour Series products:`);
        
        let matchedCount = 0;
        let unmatchedCount = 0;
        
        tourSeriesProducts.forEach(product => {
            const status = product.calculated_mps_id ? '✅ MATCHED' : '❌ UNMATCHED';
            console.log(`   ${status}: "${product.mold_name}" | MPS: ${product.calculated_mps_id || 'NULL'}`);
            
            if (product.calculated_mps_id) {
                matchedCount++;
            } else {
                unmatchedCount++;
            }
        });
        
        // Get total counts
        const { count: totalTourSeries, error: totalError } = await supabase
            .from('it_discraft_order_sheet_lines')
            .select('*', { count: 'exact', head: true })
            .eq('plastic_name', 'Elite Z Swirl')
            .ilike('stamp_name', '%2025 Tour Series%');
        
        const { count: matchedTourSeries, error: matchedError } = await supabase
            .from('it_discraft_order_sheet_lines')
            .select('*', { count: 'exact', head: true })
            .eq('plastic_name', 'Elite Z Swirl')
            .ilike('stamp_name', '%2025 Tour Series%')
            .not('calculated_mps_id', 'is', null);
        
        if (!totalError && !matchedError) {
            const unmatchedTourSeries = totalTourSeries - matchedTourSeries;
            const tourSuccessRate = ((matchedTourSeries / totalTourSeries) * 100).toFixed(1);
            
            console.log(`\n📊 Tour Series Summary:`);
            console.log(`   • Total Tour Series products: ${totalTourSeries}`);
            console.log(`   • Matched: ${matchedTourSeries}`);
            console.log(`   • Unmatched: ${unmatchedTourSeries}`);
            console.log(`   • Success rate: ${tourSuccessRate}%`);
            
            if (matchedTourSeries === totalTourSeries) {
                console.log('\n🎉 ALL Tour Series products are now matched!');
            } else if (matchedTourSeries > 0) {
                console.log('\n✅ Most Tour Series products are matched!');
            } else {
                console.log('\n❌ Tour Series products are still not matching!');
            }
        }
        
        // Check what the biggest remaining issues are
        console.log('\n🔍 Checking biggest remaining parsing issues...');
        
        // Get unmatched products in chunks
        let allUnmatched = [];
        let offset = 0;
        const chunkSize = 1000;
        
        while (true) {
            const { data: unmatchedChunk, error: unmatchedError } = await supabase
                .from('it_discraft_order_sheet_lines')
                .select('plastic_name, mold_name, stamp_name')
                .is('calculated_mps_id', null)
                .range(offset, offset + chunkSize - 1);
            
            if (unmatchedError) break;
            if (!unmatchedChunk || unmatchedChunk.length === 0) break;
            
            allUnmatched = allUnmatched.concat(unmatchedChunk);
            
            if (unmatchedChunk.length < chunkSize) break;
            offset += chunkSize;
        }
        
        // Group unmatched products
        const unmatchedGroups = {};
        allUnmatched.forEach(row => {
            const key = `${row.plastic_name}|${row.mold_name}|${row.stamp_name}`;
            unmatchedGroups[key] = (unmatchedGroups[key] || 0) + 1;
        });
        
        const topUnmatched = Object.entries(unmatchedGroups)
            .sort(([,a], [,b]) => b - a)
            .slice(0, 10)
            .map(([key, count]) => {
                const [plastic_name, mold_name, stamp_name] = key.split('|');
                return { plastic_name, mold_name, stamp_name, count };
            });
        
        console.log('\n📊 Top 10 remaining unmatched product types:');
        topUnmatched.forEach((item, index) => {
            console.log(`${index + 1}. "${item.plastic_name}" | "${item.mold_name}" | "${item.stamp_name}" (${item.count} products)`);
        });
        
    } catch (error) {
        console.error('❌ Verification error:', error);
    }
}

verifyTourSeriesMatched();
