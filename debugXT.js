// Debug XT and DX plastic parsing issues

function parseInnovaDescription(description) {
    if (!description) return null;
    
    // Common Innova plastic types (order matters - longer names first)
    const plasticTypes = [
        'Champion I-Dye', 'Champion Proto Glow', 'Champion Classic Glow', 'Champion', 
        'Star I-Dye', 'Halo Star', 'Star', 
        'DX Proto Glow', 'DX Classic Glow', 'DX', 
        'Pro KC', 'KC Pro', 'R-Pro', 'Pro',
        'Halo Nexus', 'Nexus',
        'Metal Flake Champion', 'GStar', 'G-Star', 'XT'
    ];
    
    let plastic = null;
    let remainingText = description.trim();
    
    console.log(`  Starting with: "${description}"`);
    
    // Find plastic type
    for (const plasticType of plasticTypes) {
        // Use word boundaries to prevent partial matches (e.g., "Pro" in "Approach")
        const regex = new RegExp(`\\b${plasticType}\\b`, 'gi');
        if (regex.test(description)) {
            plastic = plasticType;
            // Remove plastic from description to get mold
            remainingText = description.replace(regex, '').trim();
            console.log(`  Found plastic "${plasticType}", remaining: "${remainingText}"`);
            break;
        }
    }
    
    // Extract mold name (remove Innova disc type classifications - these are ignored for matching)
    let mold = remainingText
        .replace(/\s+(Putt & Approach|Fairway Distance Driver|Distance Driver|Fairway Driver|Mid-Range|Midrange|Putter|Driver|Approach)(\s+Disc)?$/i, '')
        .replace(/\s+Disc$/i, '') // Remove standalone "Disc" at the end
        .replace(/\s+with\s+.*/i, '') // Remove "with Burst Logo" etc
        .trim();
    
    console.log(`  Final mold: "${mold}"`);
    
    return {
        plastic: plastic,
        mold: mold,
        original: description
    };
}

// Test the problematic cases
const testCases = [
    'XT Aviar3 Putt & Approach',
    'DX Aviar X3 Putt & Approach', 
    'XT Whale Putt & Approach',
    'DX Teebird 3 Fairway Driver'
];

console.log('=== XT/DX PARSING DEBUG ===\n');

testCases.forEach(description => {
    console.log(`Testing: "${description}"`);
    const result = parseInnovaDescription(description);
    console.log(`  → Plastic: "${result?.plastic || 'NOT FOUND'}"`);
    console.log(`  → Mold: "${result?.mold || 'NOT FOUND'}"`);
    console.log('');
});

// Test mold mappings
const moldMapping = {
    'AviardX3': 'Aviar X3',
    'AviarX3': 'Aviar X3',
    'Aviar3': 'Aviar3', // No mapping needed - should match directly
    'Mako3': 'Mako 3',
    'Aviar Classic': 'Classic Aviar',
    'Aviar Putter': 'Aviar',
    'Leopard3': 'Leopard 3',
    'Teebird3': 'Teebird 3'
};

function normalizeMoldName(moldName) {
    if (!moldName) return null;
    const mapped = moldMapping[moldName];
    if (mapped) return mapped;
    return moldName;
}

console.log('=== MOLD MAPPING TEST ===');
console.log(`"AviarX3" → "${normalizeMoldName('AviarX3')}"`);
console.log(`"Aviar X3" → "${normalizeMoldName('Aviar X3')}"`);
console.log(`"Aviar3" → "${normalizeMoldName('Aviar3')}"`);
console.log(`"Teebird3" → "${normalizeMoldName('Teebird3')}"`);
console.log(`"Teebird 3" → "${normalizeMoldName('Teebird 3')}"`);
